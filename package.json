{"name": "privxploit", "version": "1.0.0", "description": "PrivXploit - Checker Platform", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "connect-flash": "^0.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-ejs-layouts": "^2.5.1", "express-rate-limit": "^7.5.0", "express-session": "^1.17.3", "helmet": "^8.1.0", "method-override": "^3.0.0", "morgan": "^1.10.0", "multer": "^2.0.0", "mysql2": "^3.6.1", "sequelize": "^6.33.0", "validator": "^13.15.0", "xss": "^1.0.15"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}