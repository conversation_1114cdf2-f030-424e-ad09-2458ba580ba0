-- Adici<PERSON>r o checker de GayGay ao banco de dados
-- Execute este SQL no seu banco de dados para adicionar o checker

-- Primeiro, verifique se já existe uma categoria para consultas de pessoas
SELECT @category_id := id FROM categories WHERE name = 'Consultas de Pessoas' LIMIT 1;

-- Se não existir, crie a categoria
INSERT INTO categories (name, description, status, display_order, createdAt, updatedAt)
SELECT 'Consultas de Pessoas', 'Consultas de dados pessoais como CPF, RG, etc.', 'active', 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Consultas de Pessoas');

-- Se a categoria foi criada agora, obtenha o ID
SELECT @category_id := id FROM categories WHERE name = 'Consultas de Pessoas' LIMIT 1;

-- Verifique se o checker já existe
SELECT @checker_exists := COUNT(*) FROM checkers WHERE endpoint = 'gaygay';

-- Se o checker já existir, atualize-o
UPDATE checkers 
SET 
  title = 'Consulta GayGay',
  description = 'Consulta de dados pessoais através do sistema GayGay',
  category_id = @category_id,
  price = 1.0,
  status = 'active',
  icon = 'fa fa-user',
  charge_type = 'per_test',
  required_role = 'user',
  display_order = 2,
  module_path = 'modules/checkers/gaygay_checker.js',
  custom_code = NULL,
  updatedAt = NOW()
WHERE endpoint = 'gaygay';

-- Se o checker não existir, crie-o
INSERT INTO checkers (
  name, 
  endpoint, 
  title, 
  description, 
  category_id, 
  price, 
  status, 
  icon, 
  background_image, 
  charge_type, 
  required_role, 
  display_order,
  module_path,
  custom_code,
  createdAt, 
  updatedAt
)
SELECT
  'gaygay',
  'gaygay',
  'Consulta GayGay',
  'Consulta de dados pessoais através do sistema GayGay',
  @category_id,
  1.0,
  'active',
  'fa fa-user',
  NULL,
  'per_test',
  'user',
  2,
  'modules/checkers/gaygay_checker.js',
  NULL,
  NOW(),
  NOW()
WHERE @checker_exists = 0;
