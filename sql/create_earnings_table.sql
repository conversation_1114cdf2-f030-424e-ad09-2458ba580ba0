-- Create earnings table to track earnings for programmers and affiliates
CREATE TABLE IF NOT EXISTS earnings (
  id INT NOT NULL AUTO_INCREMENT,
  user_id INT NOT NULL,
  amount FLOAT NOT NULL,
  source_type <PERSON>NU<PERSON>('programmer', 'affiliate', 'site') NOT NULL,
  transaction_id INT NOT NULL,
  checker_id INT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  FOREIGN KEY (user_id) REFERENCES usuarios(id),
  FOREIGN KEY (transaction_id) REFERENCES transactions(id),
  FOREIGN KEY (checker_id) REFERENCES checkers(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Add programmer_id column to checkers table
ALTER TABLE checkers
ADD COLUMN programmer_id INT NULL AFTER custom_code,
ADD FOREIGN KEY (programmer_id) REFERENCES usuarios(id);

-- Add referral_code column to usuarios table
ALTER TABLE usuarios
ADD COLUMN referral_code VARCHAR(20) NULL AFTER criador,
ADD COLUMN referred_by INT NULL AFTER referral_code,
ADD FOREIGN KEY (referred_by) REFERENCES usuarios(id);

-- Add index for faster lookups
CREATE INDEX idx_referral_code ON usuarios(referral_code);
