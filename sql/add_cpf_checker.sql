-- Adici<PERSON>r o checker de CPF ao banco de dados
-- Execute este SQL no seu banco de dados para adicionar o checker de CPF

-- Primeiro, verifique se já existe uma categoria para consultas de pessoas
SELECT id FROM categories WHERE name = 'Consultas de Pessoas';

-- Se não existir, crie a categoria
INSERT INTO categories (name, description, status, display_order, createdAt, updatedAt)
VALUES ('Consultas de Pessoas', 'Consultas de dados pessoais como CPF, RG, etc.', 'active', 1, NOW(), NOW());

-- Agora, adicione o checker de CPF
INSERT INTO checkers (
  name, 
  endpoint, 
  title, 
  description, 
  category_id, 
  price, 
  status, 
  icon, 
  background_image, 
  charge_type, 
  required_role, 
  display_order,
  module_path,
  custom_code,
  createdAt, 
  updatedAt
)
VALUES (
  'cpf', 
  'cpf', 
  'Consulta de CPF', 
  'Consulta dados completos de pessoas físicas através do CPF', 
  (SELECT id FROM categories WHERE name = 'Consultas de Pessoas'), 
  1.0, 
  'active', 
  'fa fa-id-card', 
  NULL, 
  'per_test', 
  'user', 
  1,
  'modules/checkers/cpf_checker_template.js',
  NULL,
  NOW(), 
  NOW()
);
