-- Script para adicionar o Checker Locaweb ao sistema
-- Execute este script no banco de dados para registrar o novo checker

-- Inserir o checker na tabela checkers
INSERT INTO checkers (
    name,
    endpoint,
    title,
    description,
    category_id,
    price,
    status,
    icon,
    charge_type,
    required_role,
    display_order,
    module_path,
    programmer_id
) VALUES (
    'locaweb',
    'locaweb',
    'Checker Locaweb Webmail',
    'Valida credenciais de email Locaweb através do webmail-seguro.com.br. Formato: email:senha',
    (SELECT id FROM categories WHERE name = 'Email' LIMIT 1),
    2.0,
    'active',
    'fas fa-envelope',
    'per_test',
    'user',
    50,
    'modules/checkers/locaweb_checker.js',
    1
);

-- Verificar se foi inserido corretamente
SELECT 
    id,
    name,
    endpoint,
    title,
    price,
    status,
    module_path
FROM checkers 
WHERE name = 'locaweb';
