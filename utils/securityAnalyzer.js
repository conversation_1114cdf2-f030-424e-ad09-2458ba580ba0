const crypto = require('crypto');

/**
 * Analisador de segurança para código JavaScript
 */
class SecurityAnalyzer {
  constructor() {
    // Padrões perigosos com diferentes níveis de severidade
    this.dangerousPatterns = {
      critical: [
        { pattern: /eval\s*\(/, description: 'Uso de eval() - execução de código arbitrário', severity: 'critical' },
        { pattern: /Function\s*\(/, description: 'Construtor Function() - execução de código arbitrário', severity: 'critical' },
        { pattern: /child_process/, description: 'Módulo child_process - execução de comandos do sistema', severity: 'critical' },
        { pattern: /fs\.writeFile|fs\.writeFileSync/, description: 'Escrita de arquivos - possível modificação do sistema', severity: 'critical' },
        { pattern: /process\.exit/, description: 'Encerramento forçado do processo', severity: 'critical' },
        { pattern: /require\s*\(\s*['"`]vm['"`]\s*\)/, description: 'Módulo vm - execução de código em contexto', severity: 'critical' },
        { pattern: /global\s*\./, description: 'Acesso ao objeto global', severity: 'critical' },
        { pattern: /process\.env/, description: 'Acesso a variáveis de ambiente', severity: 'critical' }
      ],
      high: [
        { pattern: /require\s*\(\s*['"`]fs['"`]\s*\)/, description: 'Módulo fs - acesso ao sistema de arquivos', severity: 'high' },
        { pattern: /require\s*\(\s*['"`]os['"`]\s*\)/, description: 'Módulo os - informações do sistema operacional', severity: 'high' },
        { pattern: /require\s*\(\s*['"`]path['"`]\s*\)/, description: 'Módulo path - manipulação de caminhos', severity: 'high' },
        { pattern: /require\s*\(\s*['"`]cluster['"`]\s*\)/, description: 'Módulo cluster - controle de processos', severity: 'high' },
        { pattern: /Buffer\.from/, description: 'Manipulação de buffers - possível vazamento de memória', severity: 'high' },
        { pattern: /setTimeout\s*\(\s*.*,\s*[0-9]{6,}/, description: 'Timeout muito longo - possível DoS', severity: 'high' },
        { pattern: /setInterval/, description: 'Uso de setInterval - possível consumo excessivo de recursos', severity: 'high' }
      ],
      medium: [
        { pattern: /console\.log/, description: 'Uso de console.log - possível vazamento de informações', severity: 'medium' },
        { pattern: /JSON\.parse/, description: 'JSON.parse sem tratamento de erro', severity: 'medium' },
        { pattern: /parseInt\s*\(.*\)(?!\s*,\s*10)/, description: 'parseInt sem especificar base 10', severity: 'medium' },
        { pattern: /Math\.random/, description: 'Math.random para geração de valores críticos', severity: 'medium' },
        { pattern: /new\s+Date\s*\(\s*\)/, description: 'Uso de Date() - possível dependência de timezone', severity: 'medium' }
      ],
      low: [
        { pattern: /var\s+/, description: 'Uso de var ao invés de let/const', severity: 'low' },
        { pattern: /==(?!=)/, description: 'Comparação com == ao invés de ===', severity: 'low' },
        { pattern: /!=(?!=)/, description: 'Comparação com != ao invés de !==', severity: 'low' }
      ]
    };

    // Módulos permitidos por padrão
    this.allowedModules = [
      'axios',
      'crypto',
      'querystring',
      'url',
      'util'
    ];

    // Palavras-chave proibidas
    this.forbiddenKeywords = [
      '__dirname',
      '__filename',
      'process',
      'global',
      'Buffer',
      'require.cache',
      'require.resolve',
      'module.parent',
      'module.children'
    ];
  }

  /**
   * Analisa o código e retorna um relatório de segurança
   * @param {string} code - Código JavaScript para analisar
   * @param {Object} options - Opções de análise
   * @returns {Object} Relatório de segurança
   */
  analyzeCode(code, options = {}) {
    const report = {
      score: 100,
      issues: [],
      allowedModules: [...this.allowedModules],
      recommendations: [],
      codeHash: this.generateCodeHash(code),
      analyzedAt: new Date().toISOString()
    };

    // Verificar padrões perigosos
    this.checkDangerousPatterns(code, report);

    // Verificar módulos require
    this.checkRequireStatements(code, report);

    // Verificar palavras-chave proibidas
    this.checkForbiddenKeywords(code, report);

    // Verificar complexidade do código
    this.checkCodeComplexity(code, report);

    // Verificar tamanho do código
    this.checkCodeSize(code, report);

    // Gerar recomendações
    this.generateRecommendations(report);

    return report;
  }

  /**
   * Verifica padrões perigosos no código
   */
  checkDangerousPatterns(code, report) {
    Object.entries(this.dangerousPatterns).forEach(([severity, patterns]) => {
      patterns.forEach(({ pattern, description, severity: patternSeverity }) => {
        const matches = code.match(pattern);
        if (matches) {
          const issue = {
            type: 'dangerous_pattern',
            severity: patternSeverity,
            description,
            pattern: pattern.toString(),
            matches: matches.length,
            line: this.findLineNumber(code, matches[0])
          };

          report.issues.push(issue);

          // Reduzir score baseado na severidade
          const scoreReduction = {
            critical: 30,
            high: 15,
            medium: 5,
            low: 2
          };

          report.score -= scoreReduction[patternSeverity] || 5;
        }
      });
    });
  }

  /**
   * Verifica declarações require no código
   */
  checkRequireStatements(code, report) {
    const requirePattern = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
    let match;

    while ((match = requirePattern.exec(code)) !== null) {
      const moduleName = match[1];

      if (!this.allowedModules.includes(moduleName)) {
        report.issues.push({
          type: 'unauthorized_module',
          severity: 'high',
          description: `Módulo não autorizado: ${moduleName}`,
          module: moduleName,
          line: this.findLineNumber(code, match[0])
        });

        report.score -= 20;
      }
    }
  }

  /**
   * Verifica palavras-chave proibidas
   */
  checkForbiddenKeywords(code, report) {
    this.forbiddenKeywords.forEach(keyword => {
      const pattern = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = code.match(pattern);

      if (matches) {
        report.issues.push({
          type: 'forbidden_keyword',
          severity: 'high',
          description: `Palavra-chave proibida: ${keyword}`,
          keyword,
          matches: matches.length,
          line: this.findLineNumber(code, matches[0])
        });

        report.score -= 15;
      }
    });
  }

  /**
   * Verifica complexidade do código
   */
  checkCodeComplexity(code, report) {
    // Contar funções
    const functionCount = (code.match(/function\s+\w+|=>\s*{|function\s*\(/g) || []).length;
    
    // Contar loops
    const loopCount = (code.match(/for\s*\(|while\s*\(|do\s*{/g) || []).length;
    
    // Contar condicionais
    const conditionalCount = (code.match(/if\s*\(|switch\s*\(/g) || []).length;

    const complexity = functionCount + loopCount + conditionalCount;

    if (complexity > 20) {
      report.issues.push({
        type: 'high_complexity',
        severity: 'medium',
        description: `Código muito complexo (${complexity} pontos de complexidade)`,
        complexity
      });

      report.score -= 10;
    }
  }

  /**
   * Verifica tamanho do código
   */
  checkCodeSize(code, report) {
    const lines = code.split('\n').length;
    const characters = code.length;

    if (lines > 500) {
      report.issues.push({
        type: 'large_code',
        severity: 'low',
        description: `Código muito extenso (${lines} linhas)`,
        lines
      });

      report.score -= 5;
    }

    if (characters > 50000) {
      report.issues.push({
        type: 'large_code',
        severity: 'medium',
        description: `Código muito extenso (${characters} caracteres)`,
        characters
      });

      report.score -= 10;
    }
  }

  /**
   * Gera recomendações baseadas nos problemas encontrados
   */
  generateRecommendations(report) {
    const criticalIssues = report.issues.filter(i => i.severity === 'critical');
    const highIssues = report.issues.filter(i => i.severity === 'high');

    if (criticalIssues.length > 0) {
      report.recommendations.push('CRÍTICO: Remova todos os padrões críticos antes de submeter o código');
    }

    if (highIssues.length > 0) {
      report.recommendations.push('ALTO: Revise e corrija os problemas de alta severidade');
    }

    if (report.score < 50) {
      report.recommendations.push('O código precisa de revisão significativa antes da aprovação');
    } else if (report.score < 80) {
      report.recommendations.push('O código precisa de algumas correções antes da aprovação');
    }

    // Garantir que o score não seja negativo
    report.score = Math.max(0, report.score);
  }

  /**
   * Encontra o número da linha onde ocorre um match
   */
  findLineNumber(code, match) {
    const beforeMatch = code.substring(0, code.indexOf(match));
    return beforeMatch.split('\n').length;
  }

  /**
   * Gera hash SHA-256 do código
   */
  generateCodeHash(code) {
    return crypto.createHash('sha256').update(code).digest('hex');
  }

  /**
   * Verifica se o código é seguro para execução
   */
  isSafeForExecution(analysisReport) {
    const criticalIssues = analysisReport.issues.filter(i => i.severity === 'critical');
    return criticalIssues.length === 0 && analysisReport.score >= 70;
  }

  /**
   * Determina o nível de sandbox necessário
   */
  determineSandboxLevel(analysisReport) {
    if (analysisReport.score >= 90) {
      return 'standard';
    } else if (analysisReport.score >= 70) {
      return 'limited';
    } else {
      return 'strict';
    }
  }
}

module.exports = SecurityAnalyzer;
