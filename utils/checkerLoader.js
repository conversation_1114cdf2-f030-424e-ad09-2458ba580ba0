const path = require('path');
const fs = require('fs');
const vm = require('vm');
const crypto = require('crypto');
const { CodeReview } = require('../models');

// Lista de módulos permitidos no sandbox
const ALLOWED_MODULES = ['axios', 'crypto', 'querystring', 'url', 'util'];

// Cache de handlers compilados
const handlerCache = new Map();

/**
 * Função require segura que só permite módulos autorizados
 */
const secureRequire = (moduleName) => {
  if (!ALLOWED_MODULES.includes(moduleName)) {
    throw new Error(`Módulo '${moduleName}' não é permitido no sandbox`);
  }
  return require(moduleName);
};

/**
 * Cria um contexto seguro para execução de código
 */
const createSecureContext = (sandboxLevel = 'strict') => {
  const baseContext = {
    module: { exports: {} },
    exports: {},
    require: secureRequire,
    console: {
      log: (...args) => console.log('[CHECKER]', ...args),
      error: (...args) => console.error('[CHECKER]', ...args),
      warn: (...args) => console.warn('[CHECKER]', ...args)
    },
    setTimeout: (fn, delay) => {
      if (delay > 30000) throw new Error('Timeout máximo de 30 segundos');
      return setTimeout(fn, delay);
    },
    clearTimeout,
    // Bloquear acesso a objetos globais perigosos
    process: undefined,
    global: undefined,
    Buffer: undefined,
    __dirname: undefined,
    __filename: undefined,
    eval: undefined,
    Function: undefined
  };

  // Adicionar funcionalidades baseadas no nível do sandbox
  if (sandboxLevel === 'limited') {
    baseContext.Date = Date;
    baseContext.Math = Math;
    baseContext.JSON = JSON;
  } else if (sandboxLevel === 'standard') {
    baseContext.Date = Date;
    baseContext.Math = Math;
    baseContext.JSON = JSON;
    baseContext.parseInt = parseInt;
    baseContext.parseFloat = parseFloat;
    baseContext.encodeURIComponent = encodeURIComponent;
    baseContext.decodeURIComponent = decodeURIComponent;
  }

  return baseContext;
};

/**
 * Carrega um módulo de checker aprovado a partir de um arquivo
 * @param {string} modulePath - Caminho para o arquivo do módulo
 * @param {string} expectedHash - Hash esperado do arquivo para verificação de integridade
 * @returns {Function} - Função do módulo
 */
const loadCheckerModule = async (modulePath, expectedHash = null) => {
  try {
    // Verificar se está no cache
    const cacheKey = `module:${modulePath}:${expectedHash}`;
    if (handlerCache.has(cacheKey)) {
      return handlerCache.get(cacheKey);
    }

    // Caminho absoluto para o módulo
    const absolutePath = path.join(process.cwd(), modulePath);

    // Verificar se o arquivo existe
    if (!fs.existsSync(absolutePath)) {
      throw new Error(`Módulo não encontrado: ${absolutePath}`);
    }

    // Carregar o módulo
    const moduleCode = fs.readFileSync(absolutePath, 'utf8');

    // Verificar integridade se hash foi fornecido
    if (expectedHash) {
      const actualHash = crypto.createHash('sha256').update(moduleCode).digest('hex');
      if (actualHash !== expectedHash) {
        throw new Error('Integridade do módulo comprometida - hash não confere');
      }
    }

    // Verificar se o código foi aprovado
    const codeReview = await CodeReview.findOne({
      where: {
        approved_code_hash: crypto.createHash('sha256').update(moduleCode).digest('hex'),
        status: 'approved'
      }
    });

    if (!codeReview) {
      throw new Error('Código não aprovado para execução');
    }

    // Criar contexto seguro baseado na configuração da revisão
    const context = createSecureContext(codeReview.execution_sandbox);

    // Compilar e executar o código no contexto com timeout
    const script = new vm.Script(moduleCode, {
      filename: absolutePath,
      timeout: codeReview.max_execution_time || 5000
    });

    script.runInNewContext(context, {
      timeout: codeReview.max_execution_time || 5000,
      breakOnSigint: true
    });

    // Cache do handler
    const handler = context.module.exports;
    handlerCache.set(cacheKey, handler);

    return handler;
  } catch (err) {
    console.error(`Erro ao carregar módulo ${modulePath}:`, err);
    throw err;
  }
};

/**
 * Compila código personalizado aprovado em uma função
 * @param {string} code - Código JavaScript
 * @param {Object} reviewData - Dados da revisão de código
 * @returns {Function} - Função compilada
 */
const compileCustomCode = async (code, reviewData = null) => {
  try {
    // Verificar se está no cache
    const codeHash = crypto.createHash('sha256').update(code).digest('hex');
    const cacheKey = `custom:${codeHash}`;

    if (handlerCache.has(cacheKey)) {
      return handlerCache.get(cacheKey);
    }

    // Verificar se o código foi aprovado
    const codeReview = reviewData || await CodeReview.findOne({
      where: {
        approved_code_hash: codeHash,
        status: 'approved'
      }
    });

    if (!codeReview) {
      throw new Error('Código personalizado não aprovado para execução');
    }

    // Criar contexto seguro baseado na configuração da revisão
    const context = createSecureContext(codeReview.execution_sandbox);

    // Compilar e executar o código no contexto com timeout
    const script = new vm.Script(code, {
      filename: 'custom-checker.js',
      timeout: codeReview.max_execution_time || 5000
    });

    script.runInNewContext(context, {
      timeout: codeReview.max_execution_time || 5000,
      breakOnSigint: true
    });

    // Cache do handler
    const handler = context.module.exports;
    handlerCache.set(cacheKey, handler);

    return handler;
  } catch (err) {
    console.error('Erro ao compilar código personalizado:', err);
    throw err;
  }
};

/**
 * Carrega um handler para um checker de forma segura
 * @param {Object} checker - Objeto do checker
 * @returns {Function} - Função handler do checker
 */
const getCheckerHandler = async (checker) => {
  try {
    // VALIDAÇÃO OBRIGATÓRIA: Verificar se o código foi aprovado
    const isApproved = await validateCheckerExecution(checker);

    if (!isApproved) {
      throw new Error(`Checker "${checker.name}" possui código não aprovado. Execução bloqueada por segurança.`);
    }

    // Se tiver um módulo, carrega ele primeiro
    if (checker.module_path) {
      return await loadCheckerModule(checker.module_path, checker.approved_code_hash);
    }

    // Se tiver código personalizado, compila ele
    if (checker.custom_code) {
      return await compileCustomCode(checker.custom_code);
    }

    // Se não tiver nenhum dos dois, retorna um handler padrão seguro
    return async (req, res, checker) => {
      try {
        const { lista } = req.query;

        if (!lista) {
          return res.status(400).send('Parâmetro lista é obrigatório');
        }

        // Handler padrão - simular processamento
        const isApproved = Math.random() > 0.7; // 30% de chance de aprovação

        if (isApproved) {
          return res.send(`[LIVE] Aprovado (Padrão) | ${lista}`);
        } else {
          return res.send(`[DIE] Reprovado (Padrão) | ${lista}`);
        }
      } catch (err) {
        console.error('[CHECKER DEFAULT]', err);
        return res.status(500).send('[DIE] Erro ao processar requisição');
      }
    };
  } catch (err) {
    console.error(`Erro ao obter handler para checker ${checker.name}:`, err);

    // Retorna um handler de erro seguro
    return async (req, res) => {
      console.error(`[CHECKER ERROR] ${checker.name}:`, err.message);
      return res.status(500).send('[DIE] Checker indisponível - código não aprovado ou erro de segurança');
    };
  }
};

/**
 * Limpa o cache de handlers (útil para recarregar código atualizado)
 */
const clearHandlerCache = () => {
  handlerCache.clear();
  console.log('Cache de handlers limpo');
};

/**
 * Obtém estatísticas do cache
 */
const getCacheStats = () => {
  return {
    size: handlerCache.size,
    keys: Array.from(handlerCache.keys())
  };
};

/**
 * Valida se um checker pode ser executado (VALIDAÇÃO RIGOROSA)
 */
const validateCheckerExecution = async (checker) => {
  try {
    let codeContent = null;
    let codeHash = null;

    // Determinar o código a ser validado
    if (checker.module_path) {
      const modulePath = path.join(process.cwd(), checker.module_path);
      if (!fs.existsSync(modulePath)) {
        console.error(`❌ Arquivo do módulo não encontrado: ${modulePath}`);
        return false;
      }
      codeContent = fs.readFileSync(modulePath, 'utf8');
    } else if (checker.custom_code) {
      codeContent = checker.custom_code;
    } else {
      // Checker sem código - permitir apenas se for um checker básico do sistema
      console.warn(`⚠️  Checker ${checker.name} não possui código definido`);
      return false;
    }

    // Calcular hash do código atual
    codeHash = crypto.createHash('sha256').update(codeContent).digest('hex');

    // Verificar se existe code review aprovado para este hash
    const codeReview = await CodeReview.findOne({
      where: {
        approved_code_hash: codeHash,
        status: 'approved'
      }
    });

    if (!codeReview) {
      console.error(`❌ Código não aprovado para checker ${checker.name} (Hash: ${codeHash.substring(0, 16)}...)`);

      // Verificar se existe algum code review pendente
      const pendingReview = await CodeReview.findOne({
        where: {
          checker_id: checker.id,
          status: ['pending', 'needs_revision']
        },
        order: [['createdAt', 'DESC']]
      });

      if (pendingReview) {
        console.warn(`⏳ Existe revisão pendente para checker ${checker.name} (ID: ${pendingReview.id})`);
      } else {
        console.warn(`📝 Nenhuma revisão encontrada para checker ${checker.name} - código deve ser submetido para revisão`);
      }

      return false;
    }

    // Verificar se o hash do checker coincide com o aprovado
    if (checker.approved_code_hash && checker.approved_code_hash !== codeHash) {
      console.error(`❌ Hash do checker ${checker.name} não coincide com o código aprovado`);
      console.error(`   Esperado: ${checker.approved_code_hash}`);
      console.error(`   Atual: ${codeHash}`);
      return false;
    }

    console.log(`✅ Código aprovado para checker ${checker.name} (Review ID: ${codeReview.id})`);
    return true;

  } catch (err) {
    console.error('Erro ao validar execução do checker:', err);
    return false;
  }
};

module.exports = {
  loadCheckerModule,
  compileCustomCode,
  getCheckerHandler,
  clearHandlerCache,
  getCacheStats,
  validateCheckerExecution,
  createSecureContext,
  ALLOWED_MODULES
};
