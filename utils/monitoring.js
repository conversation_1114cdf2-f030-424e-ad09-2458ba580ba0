const EventEmitter = require('events');
const os = require('os');
const db = require('../config/database');

class ApplicationMonitor extends EventEmitter {
  constructor() {
    super();
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byEndpoint: new Map(),
        byUser: new Map(),
        responseTime: []
      },
      checkers: {
        executions: 0,
        successful: 0,
        failed: 0,
        by<PERSON>he<PERSON>: new Map(),
        averageExecutionTime: 0
      },
      security: {
        blockedRequests: 0,
        rateLimitHits: 0,
        suspiciousActivity: 0,
        codeReviewsSubmitted: 0,
        codeReviewsApproved: 0,
        codeReviewsRejected: 0
      },
      system: {
        memoryUsage: [],
        cpuUsage: [],
        diskUsage: [],
        activeConnections: 0,
        uptime: 0
      },
      database: {
        connections: 0,
        queries: 0,
        slowQueries: 0,
        errors: 0
      },
      cache: {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        hitRate: 0
      }
    };

    this.alerts = [];
    this.thresholds = {
      memoryUsage: 85, // %
      cpuUsage: 80, // %
      diskUsage: 90, // %
      responseTime: 2000, // ms
      errorRate: 5, // %
      dbConnections: 80 // % of max
    };

    this.startMonitoring();
  }

  startMonitoring() {
    // Coletar métricas a cada 30 segundos
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Verificar alertas a cada minuto
    setInterval(() => {
      this.checkAlerts();
    }, 60000);

    // Salvar métricas em memória a cada 5 minutos
    setInterval(() => {
      this.saveMetricsToMemory();
    }, 5 * 60 * 1000);

    console.log('Sistema de monitoramento iniciado');
  }

  // Registrar request HTTP
  recordRequest(req, res, responseTime) {
    this.metrics.requests.total++;

    if (res.statusCode >= 200 && res.statusCode < 400) {
      this.metrics.requests.successful++;
    } else {
      this.metrics.requests.failed++;
    }

    // Registrar por endpoint
    const endpoint = req.route?.path || req.path;
    const endpointCount = this.metrics.requests.byEndpoint.get(endpoint) || 0;
    this.metrics.requests.byEndpoint.set(endpoint, endpointCount + 1);

    // Registrar por usuário - verificar se session e user existem
    if (req.session && req.session.user) {
      const userId = req.session.user.id;
      const userCount = this.metrics.requests.byUser.get(userId) || 0;
      this.metrics.requests.byUser.set(userId, userCount + 1);
    }

    // Registrar tempo de resposta
    this.metrics.requests.responseTime.push(responseTime);

    // Manter apenas últimas 1000 medições
    if (this.metrics.requests.responseTime.length > 1000) {
      this.metrics.requests.responseTime.shift();
    }
  }

  // Registrar execução de checker
  recordCheckerExecution(checkerId, success, executionTime) {
    this.metrics.checkers.executions++;

    if (success) {
      this.metrics.checkers.successful++;
    } else {
      this.metrics.checkers.failed++;
    }

    // Registrar por checker
    const checkerStats = this.metrics.checkers.byChecker.get(checkerId) || {
      executions: 0,
      successful: 0,
      failed: 0,
      totalTime: 0
    };

    checkerStats.executions++;
    checkerStats.totalTime += executionTime;

    if (success) {
      checkerStats.successful++;
    } else {
      checkerStats.failed++;
    }

    this.metrics.checkers.byChecker.set(checkerId, checkerStats);

    // Calcular tempo médio de execução
    const totalExecutions = this.metrics.checkers.executions;
    const totalTime = Array.from(this.metrics.checkers.byChecker.values())
      .reduce((sum, stats) => sum + stats.totalTime, 0);

    this.metrics.checkers.averageExecutionTime = totalTime / totalExecutions;
  }

  // Registrar evento de segurança
  recordSecurityEvent(type, details = {}) {
    switch (type) {
      case 'blocked_request':
        this.metrics.security.blockedRequests++;
        break;
      case 'rate_limit_hit':
        this.metrics.security.rateLimitHits++;
        break;
      case 'suspicious_activity':
        this.metrics.security.suspiciousActivity++;
        break;
      case 'code_review_submitted':
        this.metrics.security.codeReviewsSubmitted++;
        break;
      case 'code_review_approved':
        this.metrics.security.codeReviewsApproved++;
        break;
      case 'code_review_rejected':
        this.metrics.security.codeReviewsRejected++;
        break;
    }

    // Emitir evento para alertas
    this.emit('securityEvent', { type, details, timestamp: new Date() });
  }

  // Registrar evento de cache
  recordCacheEvent(type) {
    switch (type) {
      case 'hit':
        this.metrics.cache.hits++;
        break;
      case 'miss':
        this.metrics.cache.misses++;
        break;
      case 'set':
        this.metrics.cache.sets++;
        break;
      case 'delete':
        this.metrics.cache.deletes++;
        break;
    }

    // Calcular hit rate
    const total = this.metrics.cache.hits + this.metrics.cache.misses;
    this.metrics.cache.hitRate = total > 0 ? (this.metrics.cache.hits / total) * 100 : 0;
  }

  // Coletar métricas do sistema
  async collectSystemMetrics() {
    try {
      // Memória
      const memUsage = process.memoryUsage();
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      const usedMem = totalMem - freeMem;
      const memoryPercent = (usedMem / totalMem) * 100;

      this.metrics.system.memoryUsage.push({
        timestamp: Date.now(),
        heap: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        systemUsed: usedMem,
        systemTotal: totalMem,
        percentage: memoryPercent
      });

      // CPU
      const cpus = os.cpus();
      const loadAvg = os.loadavg();
      const cpuUsage = (loadAvg[0] / cpus.length) * 100;

      this.metrics.system.cpuUsage.push({
        timestamp: Date.now(),
        usage: cpuUsage,
        loadAverage: loadAvg,
        cores: cpus.length
      });

      // Uptime
      this.metrics.system.uptime = process.uptime();

      // Manter apenas últimas 100 medições
      if (this.metrics.system.memoryUsage.length > 100) {
        this.metrics.system.memoryUsage.shift();
      }
      if (this.metrics.system.cpuUsage.length > 100) {
        this.metrics.system.cpuUsage.shift();
      }

      // Métricas do banco de dados
      await this.collectDatabaseMetrics();

    } catch (err) {
      console.error('Erro ao coletar métricas do sistema:', err);
    }
  }

  // Coletar métricas do banco de dados
  async collectDatabaseMetrics() {
    try {
      // Número de conexões ativas
      const [connections] = await db.query('SHOW STATUS LIKE "Threads_connected"');
      this.metrics.database.connections = parseInt(connections[0]?.Value || 0);

      // Queries por segundo
      const [queries] = await db.query('SHOW STATUS LIKE "Queries"');
      const currentQueries = parseInt(queries[0]?.Value || 0);

      if (this.lastQueryCount) {
        this.metrics.database.queries = currentQueries - this.lastQueryCount;
      }
      this.lastQueryCount = currentQueries;

      // Slow queries
      const [slowQueries] = await db.query('SHOW STATUS LIKE "Slow_queries"');
      this.metrics.database.slowQueries = parseInt(slowQueries[0]?.Value || 0);

    } catch (err) {
      console.error('Erro ao coletar métricas do banco:', err);
      this.metrics.database.errors++;
    }
  }

  // Verificar alertas
  checkAlerts() {
    const now = Date.now();

    // Verificar uso de memória
    const latestMemory = this.metrics.system.memoryUsage[this.metrics.system.memoryUsage.length - 1];
    if (latestMemory && latestMemory.percentage > this.thresholds.memoryUsage) {
      this.createAlert('HIGH_MEMORY_USAGE', {
        current: latestMemory.percentage,
        threshold: this.thresholds.memoryUsage,
        severity: 'warning'
      });
    }

    // Verificar uso de CPU
    const latestCpu = this.metrics.system.cpuUsage[this.metrics.system.cpuUsage.length - 1];
    if (latestCpu && latestCpu.usage > this.thresholds.cpuUsage) {
      this.createAlert('HIGH_CPU_USAGE', {
        current: latestCpu.usage,
        threshold: this.thresholds.cpuUsage,
        severity: 'warning'
      });
    }

    // Verificar taxa de erro
    const totalRequests = this.metrics.requests.total;
    const failedRequests = this.metrics.requests.failed;
    const errorRate = totalRequests > 0 ? (failedRequests / totalRequests) * 100 : 0;

    if (errorRate > this.thresholds.errorRate && totalRequests > 100) {
      this.createAlert('HIGH_ERROR_RATE', {
        current: errorRate,
        threshold: this.thresholds.errorRate,
        totalRequests: totalRequests,
        failedRequests: failedRequests,
        severity: 'critical'
      });
    }

    // Verificar tempo de resposta médio
    const responseTimes = this.metrics.requests.responseTime;
    if (responseTimes.length > 0) {
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;

      if (avgResponseTime > this.thresholds.responseTime) {
        this.createAlert('SLOW_RESPONSE_TIME', {
          current: avgResponseTime,
          threshold: this.thresholds.responseTime,
          severity: 'warning'
        });
      }
    }
  }

  // Criar alerta
  createAlert(type, data) {
    const alert = {
      id: Date.now(),
      type: type,
      data: data,
      timestamp: new Date(),
      acknowledged: false
    };

    this.alerts.push(alert);

    // Manter apenas últimos 100 alertas
    if (this.alerts.length > 100) {
      this.alerts.shift();
    }

    // Emitir evento de alerta
    this.emit('alert', alert);

    console.warn(`ALERT [${data.severity}]: ${type}`, data);
  }

  // Salvar métricas em memória (Redis removido)
  async saveMetricsToMemory() {
    try {
      const timestamp = Date.now();
      const metricsSnapshot = {
        timestamp: timestamp,
        metrics: JSON.parse(JSON.stringify(this.metrics))
      };

      // Manter apenas últimas 24 métricas em memória
      if (!this.metricsHistory) {
        this.metricsHistory = [];
      }

      this.metricsHistory.push(metricsSnapshot);

      // Manter apenas últimas 24 entradas
      if (this.metricsHistory.length > 24) {
        this.metricsHistory.shift();
      }

    } catch (err) {
      console.error('Erro ao salvar métricas em memória:', err);
    }
  }

  // Obter métricas atuais
  getMetrics() {
    return {
      ...this.metrics,
      alerts: this.alerts.filter(alert => !alert.acknowledged),
      timestamp: new Date()
    };
  }

  // Obter histórico de métricas
  async getMetricsHistory(hours = 24) {
    try {
      if (!this.metricsHistory) {
        return [];
      }

      const cutoff = Date.now() - (hours * 60 * 60 * 1000);

      return this.metricsHistory
        .filter(item => item.timestamp >= cutoff)
        .sort((a, b) => a.timestamp - b.timestamp);
    } catch (err) {
      console.error('Erro ao obter histórico de métricas:', err);
      return [];
    }
  }

  // Reconhecer alerta
  acknowledgeAlert(alertId) {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedAt = new Date();
    }
  }

  // Reset métricas
  resetMetrics() {
    this.metrics = {
      requests: { total: 0, successful: 0, failed: 0, byEndpoint: new Map(), byUser: new Map(), responseTime: [] },
      checkers: { executions: 0, successful: 0, failed: 0, byChecker: new Map(), averageExecutionTime: 0 },
      security: { blockedRequests: 0, rateLimitHits: 0, suspiciousActivity: 0, codeReviewsSubmitted: 0, codeReviewsApproved: 0, codeReviewsRejected: 0 },
      system: { memoryUsage: [], cpuUsage: [], diskUsage: [], activeConnections: 0, uptime: 0 },
      database: { connections: 0, queries: 0, slowQueries: 0, errors: 0 },
      cache: { hits: 0, misses: 0, sets: 0, deletes: 0, hitRate: 0 }
    };

    console.log('Métricas resetadas');
  }
}

// Instância singleton do monitor
const monitor = new ApplicationMonitor();

module.exports = monitor;
