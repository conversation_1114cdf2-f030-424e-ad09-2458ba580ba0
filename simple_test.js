const db = require('./config/database');

async function test() {
  try {
    console.log('Testando conexão com banco...');
    await db.authenticate();
    console.log('✅ Conexão com banco OK');
    
    const { Checker } = require('./models');
    const checkers = await Checker.findAll({ limit: 1 });
    console.log(`✅ Encontrados ${checkers.length} checkers`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Erro:', error.message);
    process.exit(1);
  }
}

test();
