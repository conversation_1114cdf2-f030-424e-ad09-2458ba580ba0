/**
 * Teste do Checker Locaweb
 * Execute: node test/test_locaweb_checker.js
 */

const locawebChecker = require('../modules/checkers/locaweb_checker');

// Mock do objeto req
const mockReq = {
  query: {
    lista: '<EMAIL>:!NComerMca521'
  },
  session: {
    user: {
      id: 1,
      username: 'admin',
      saldo: 100
    }
  },
  // Mock da função debitAndDistribute
  debitAndDistribute: async function(isApproved) {
    console.log(`💰 Simulando débito de créditos - Aprovado: ${isApproved}`);
    return true; // Simular sucesso no débito
  }
};

// Mock do objeto res
const mockRes = {
  status: function(code) {
    this.statusCode = code;
    return this;
  },
  send: function(message) {
    console.log(`📤 Resposta (${this.statusCode || 200}): ${message}`);
    return this;
  }
};

// Mock do objeto checker
const mockChecker = {
  id: 31,
  name: 'locaweb',
  price: 2.0
};

async function testLocawebChecker() {
  console.log('🧪 Iniciando teste do Checker Locaweb...');
  console.log(`📧 Testando credencial: ${mockReq.query.lista}`);
  console.log(`👤 Usuário: ${mockReq.session.user.username}`);
  console.log(`💰 Saldo: ${mockReq.session.user.saldo} créditos`);
  console.log('─'.repeat(60));

  try {
    await locawebChecker(mockReq, mockRes, mockChecker);
  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
  }

  console.log('─'.repeat(60));
  console.log('✅ Teste concluído!');
}

// Executar teste
testLocawebChecker();
