/**
 * Script de teste para o sistema de pagamentos PIX e Criptomoedas
 * Execute com: node test/payment-system-test.js
 */

const paymentService = require('../services/paymentService');

async function testPaymentSystem() {
  console.log('🚀 Iniciando testes do sistema de pagamentos...\n');

  // Teste 1: Verificar configuração das chaves
  console.log('📋 Teste 1: Verificação das chaves da API');
  console.log('Public Key:', paymentService.publicKey ? '✅ Configurada' : '❌ Não configurada');
  console.log('Secret Key:', paymentService.secretKey ? '✅ Configurada' : '❌ Não configurada');
  console.log('Base URL:', paymentService.baseURL);
  console.log('');

  // Teste 2: Testar conexão com a API
  console.log('🌐 Teste 2: Conexão com API PagFly');
  try {
    const apiTest = await paymentService.listTransactions({ page: 1, pageSize: 1 });
    if (apiTest.success) {
      console.log('✅ Conexão com API estabelecida com sucesso');
      console.log('📊 Dados recebidos:', JSON.stringify(apiTest.data, null, 2));
    } else {
      console.log('❌ Erro na conexão:', apiTest.error);
    }
  } catch (error) {
    console.log('❌ Erro na conexão:', error.message);
  }
  console.log('');

  // Teste 3: Gerar ID único
  console.log('🔑 Teste 3: Geração de ID único');
  const externalId = paymentService.generateExternalId(1);
  console.log('ID gerado:', externalId);
  console.log('Formato válido:', /^CS_\d+_\d+_[a-f0-9]{8}$/.test(externalId) ? '✅' : '❌');
  console.log('');

  // Teste 4: Conversão de criptomoedas
  console.log('💰 Teste 4: Conversão para criptomoedas');
  try {
    const btcAmount = await paymentService.convertToCrypto(100, 'BTC');
    const ethAmount = await paymentService.convertToCrypto(100, 'ETH');
    const usdtAmount = await paymentService.convertToCrypto(100, 'USDT');
    
    console.log('R$ 100,00 =');
    console.log(`  BTC: ${btcAmount}`);
    console.log(`  ETH: ${ethAmount}`);
    console.log(`  USDT: ${usdtAmount}`);
    console.log('✅ Conversões realizadas com sucesso');
  } catch (error) {
    console.log('❌ Erro na conversão:', error.message);
  }
  console.log('');

  // Teste 5: Criar transação PIX de teste (comentado para não criar transação real)
  console.log('💳 Teste 5: Criação de transação PIX (simulado)');
  const pixData = {
    amount: 10.00,
    externalId: externalId,
    customerName: 'Usuário Teste',
    customerEmail: '<EMAIL>',
    description: 'Teste de pagamento PIX'
  };
  console.log('Dados da transação PIX:', JSON.stringify(pixData, null, 2));
  console.log('⚠️  Transação não criada (modo teste)');
  console.log('');

  // Teste 6: Criar transação Crypto de teste (comentado para não criar transação real)
  console.log('₿ Teste 6: Criação de transação Crypto (simulado)');
  const cryptoData = {
    amount: 10.00,
    externalId: paymentService.generateExternalId(1),
    customerName: 'Usuário Teste',
    customerEmail: '<EMAIL>',
    cryptoCurrency: 'BTC',
    description: 'Teste de pagamento Bitcoin'
  };
  console.log('Dados da transação Crypto:', JSON.stringify(cryptoData, null, 2));
  console.log('⚠️  Transação não criada (modo teste)');
  console.log('');

  // Teste 7: Validação de webhook
  console.log('🔐 Teste 7: Validação de webhook');
  const testPayload = '{"externalId":"test","status":"paid"}';
  const testSignature = 'test_signature';
  const isValid = paymentService.validateWebhookSignature(testPayload, testSignature);
  console.log('Payload de teste:', testPayload);
  console.log('Assinatura válida:', isValid ? '✅' : '❌');
  console.log('');

  console.log('🎉 Testes concluídos!');
  console.log('');
  console.log('📝 Próximos passos:');
  console.log('1. Execute a migração do banco: mysql < migrations/create_payment_transactions_table.sql');
  console.log('2. Configure as variáveis de ambiente no arquivo .env');
  console.log('3. Teste a criação de pagamentos na interface web');
  console.log('4. Configure o webhook da PagFly para: ' + (process.env.BASE_URL || 'http://localhost:3001') + '/api/payments/webhook');
}

// Executar testes se o arquivo for chamado diretamente
if (require.main === module) {
  testPaymentSystem().catch(console.error);
}

module.exports = { testPaymentSystem };
