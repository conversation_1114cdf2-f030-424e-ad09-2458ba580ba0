require('dotenv').config();

async function testCodeReviewFixed() {
  try {
    console.log('🔧 Testando correções do sistema de revisão de código...\n');
    
    // Importar serviço corrigido
    const codeReviewService = require('./services/CodeReviewService');
    
    console.log('1. 🔍 Testando getPendingReviews...');
    try {
      const result = await codeReviewService.getPendingReviews(10, 0);
      console.log('   ✅ getPendingReviews funcionando');
      console.log(`   📊 Encontradas ${result.total} revisões`);
      console.log(`   📋 Pendentes: ${result.pending}, Precisam revisão: ${result.needsRevision}`);
      console.log(`   🔧 Checkers sem aprovação: ${result.checkersWithoutApproval}`);
    } catch (err) {
      console.log('   ❌ Erro em getPendingReviews:', err.message);
    }

    console.log('\n2. 🔍 Testando createMissingCodeReviews...');
    try {
      const result = await codeReviewService.createMissingCodeReviews();
      console.log('   ✅ createMissingCodeReviews funcionando');
      console.log(`   📋 Processados ${result.processedCheckers} checkers`);
    } catch (err) {
      console.log('   ❌ Erro em createMissingCodeReviews:', err.message);
    }

    console.log('\n3. 🔍 Testando conexão com modelos...');
    try {
      const { CodeReview, User, Checker } = require('./models');
      
      const reviewCount = await CodeReview.count();
      const userCount = await User.count();
      const checkerCount = await Checker.count();
      
      console.log('   ✅ Modelos funcionando');
      console.log(`   📊 Reviews: ${reviewCount}, Users: ${userCount}, Checkers: ${checkerCount}`);
    } catch (err) {
      console.log('   ❌ Erro nos modelos:', err.message);
    }

    console.log('\n✅ Teste das correções concluído!');
    process.exit(0);

  } catch (err) {
    console.error('❌ Erro geral:', err.message);
    process.exit(1);
  }
}

testCodeReviewFixed();
