const axios = require('axios');
const crypto = require('crypto');

class PaymentService {
  constructor() {
    // PagFly Configuration - PRODUÇÃO
    this.baseURL = 'https://api.pagfly.com/v1';
    this.publicKey = 'x'; // Sempre "x" conforme documentação
    this.secretKey = process.env.PAGFLY_SECRET_KEY;

    // Verificar se a secret key está configurada
    if (!this.secretKey) {
      throw new Error('Secret Key da PagFly não configurada. Configure PAGFLY_SECRET_KEY no .env');
    }

    // Autenticação usando formato correto: secretKey:publicKey (onde publicKey é "x")
    this.auth = 'Basic ' + Buffer.from(this.secretKey + ':' + this.publicKey).toString('base64');

    // NowPayments Configuration (Crypto)
    this.nowPaymentsBaseURL = 'https://api.nowpayments.io/v1';
    this.nowPaymentsApiKey = process.env.NOWPAYMENTS_API_KEY;
    this.nowPaymentsIpnSecret = process.env.NOWPAYMENTS_IPN_SECRET;

    // General Configuration
    this.webhookSecret = process.env.PAYMENT_WEBHOOK_SECRET;
    this.baseUrl = process.env.BASE_URL || 'http://localhost:80';
  }

  /**
   * Cria uma transação PIX via PagFly - PRODUÇÃO
   * @param {Object} data - Dados da transação
   * @returns {Object} - Resposta da API
   */
  async createPixTransaction(data) {
    try {
      console.log('🔄 Iniciando criação de transação PIX - PRODUÇÃO...');
      console.log('📊 Dados recebidos:', data);

      // Converter valor de reais para centavos (PagFly usa centavos)
      const amountInCents = Math.round(data.amount * 100);
      console.log(`💰 Valor convertido: R$ ${data.amount} = ${amountInCents} centavos`);

      const payload = {
        amount: amountInCents,
        paymentMethod: 'pix',
        installments: 1,
        pix: {
          expiresInDays: 1
        },
        items: [
          {
            title: data.description || 'Compra de créditos - PrivXploit',
            unitPrice: amountInCents,
            quantity: 1,
            tangible: false
          }
        ],
        customer: {
          name: data.customerName,
          email: data.customerEmail,
          document: data.customerDocument ? {
            number: data.customerDocument,
            type: 'cpf'
          } : null
        }
      };

      console.log('📤 Payload para PagFly:', JSON.stringify(payload, null, 2));
      console.log('🔐 Auth header:', this.auth);
      console.log('🌐 URL da API:', `${this.baseURL}/transactions`);

      const response = await axios.post(`${this.baseURL}/transactions`, payload, {
        headers: {
          'Authorization': this.auth,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 30000 // 30 segundos de timeout
      });

      console.log('✅ Resposta da PagFly recebida:', response.status);
      console.log('📥 Dados da resposta:', JSON.stringify(response.data, null, 2));

      const paymentData = response.data;

      // Verificar se a resposta tem os dados necessários
      if (!paymentData || !paymentData.id) {
        throw new Error('Resposta inválida da API PagFly - ID não encontrado');
      }

      return {
        success: true,
        data: {
          id: paymentData.id,
          status: paymentData.status,
          paymentUrl: paymentData.pix?.qrcode || paymentData.pix?.qrCodeText || null,
          qrCode: paymentData.pix?.qrcode || paymentData.pix?.qrCodeText || null,
          qrCodeText: paymentData.pix?.qrcode || paymentData.pix?.qrCodeText || null,
          externalId: data.externalId,
          amount: data.amount,
          expiresAt: paymentData.pix?.expirationDate || new Date(Date.now() + (24 * 60 * 60 * 1000)).toISOString(),
          secureId: paymentData.secureId,
          secureUrl: paymentData.secureUrl
        }
      };
    } catch (error) {
      console.error('❌ Erro ao criar transação PIX:');
      console.error('📋 Detalhes do erro:', error.message);

      if (error.response) {
        console.error('📊 Status da resposta:', error.response.status);
        console.error('📄 Dados da resposta:', JSON.stringify(error.response.data, null, 2));
        console.error('🔍 Headers da resposta:', error.response.headers);
      } else if (error.request) {
        console.error('📡 Erro de rede - sem resposta:', error.request);
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Erro interno do servidor'
      };
    }
  }

  /**
   * Cria uma transação de criptomoeda via NowPayments - PRODUÇÃO
   * @param {Object} data - Dados da transação
   * @returns {Object} - Resposta da API
   */
  async createCryptoTransaction(data) {
    try {
      if (!this.nowPaymentsApiKey) {
        throw new Error('API Key do NowPayments não configurada');
      }

      // Primeiro, obter o valor em USD
      const usdAmount = await this.convertBRLToUSD(data.amount);

      // Criar o pagamento
      const payload = {
        price_amount: usdAmount,
        price_currency: 'usd',
        pay_currency: data.cryptoCurrency.toLowerCase(),
        order_id: data.externalId,
        order_description: data.description || 'Compra de créditos - PrivXploit',
        ipn_callback_url: `${this.baseUrl}/api/payments/webhook/nowpayments`,
        success_url: `${this.baseUrl}/painel/credits?success=1`,
        cancel_url: `${this.baseUrl}/painel/credits?cancelled=1`
      };

      const response = await axios.post(`${this.nowPaymentsBaseURL}/payment`, payload, {
        headers: {
          'x-api-key': this.nowPaymentsApiKey,
          'Content-Type': 'application/json'
        }
      });

      const paymentData = response.data;

      return {
        success: true,
        data: {
          id: paymentData.payment_id,
          status: paymentData.payment_status,
          paymentUrl: paymentData.pay_address,
          cryptoAmount: paymentData.pay_amount,
          cryptoCurrency: data.cryptoCurrency,
          externalId: data.externalId,
          amount: data.amount,
          usdAmount: usdAmount,
          expiresAt: new Date(Date.now() + (2 * 60 * 60 * 1000)).toISOString() // 2 horas
        }
      };
    } catch (error) {
      console.error('Erro ao criar transação crypto:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Erro interno do servidor'
      };
    }
  }

  /**
   * Consulta o status de uma transação
   * @param {string} externalId - ID externo da transação
   * @returns {Object} - Status da transação
   */
  async getTransactionStatus(externalId) {
    try {
      // Para PagFly, usar o endpoint correto com external reference
      const response = await axios.get(`${this.baseURL}/transactions`, {
        params: {
          externalRef: externalId
        },
        headers: {
          'accept': 'application/json',
          'authorization': this.auth
        }
      });

      // PagFly retorna uma lista, pegar o primeiro item
      const transactions = response.data.data || response.data;
      const transaction = Array.isArray(transactions) ? transactions[0] : transactions;

      if (!transaction) {
        return {
          success: false,
          error: 'Transação não encontrada na API'
        };
      }

      return {
        success: true,
        data: transaction
      };
    } catch (error) {
      console.error('Erro ao consultar transação:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || 'Erro interno do servidor'
      };
    }
  }

  /**
   * Lista todas as transações (para admin)
   * @param {Object} params - Parâmetros de consulta
   * @returns {Object} - Lista de transações
   */
  async listTransactions(params = {}) {
    try {
      const queryParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        ...params
      };

      const response = await axios.get(`${this.baseURL}/transactions`, {
        params: queryParams,
        headers: {
          'accept': 'application/json',
          'authorization': this.auth
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Erro ao listar transações:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || 'Erro interno do servidor'
      };
    }
  }

  /**
   * Gera um ID único para a transação
   * @param {number} userId - ID do usuário
   * @returns {string} - ID único
   */
  generateExternalId(userId) {
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString('hex');
    return `CS_${userId}_${timestamp}_${random}`;
  }

  /**
   * Valida webhook signature (se a API suportar)
   * @param {string} payload - Payload do webhook
   * @param {string} signature - Assinatura recebida
   * @returns {boolean} - Se a assinatura é válida
   */
  validateWebhookSignature(payload, signature) {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.secretKey)
        .update(payload)
        .digest('hex');

      return signature === expectedSignature;
    } catch (error) {
      console.error('Erro ao validar webhook:', error);
      return false;
    }
  }

  /**
   * Converte BRL para USD usando uma API de cotação
   * @param {number} amountBRL - Valor em reais
   * @returns {number} - Valor em USD
   */
  async convertBRLToUSD(amountBRL) {
    try {
      // Usar uma API gratuita para conversão (exemplo: exchangerate-api.com)
      const response = await axios.get('https://api.exchangerate-api.com/v4/latest/BRL');
      const usdRate = response.data.rates.USD;
      return parseFloat((amountBRL * usdRate).toFixed(2));
    } catch (error) {
      console.error('Erro ao converter BRL para USD:', error);
      // Fallback: usar uma taxa aproximada (1 USD = 5 BRL)
      return parseFloat((amountBRL / 5).toFixed(2));
    }
  }

  /**
   * Obtém moedas disponíveis no NowPayments
   * @returns {Array} - Lista de moedas suportadas
   */
  async getAvailableCurrencies() {
    try {
      if (!this.nowPaymentsApiKey) {
        throw new Error('API Key do NowPayments não configurada');
      }

      const response = await axios.get(`${this.nowPaymentsBaseURL}/currencies`, {
        headers: {
          'x-api-key': this.nowPaymentsApiKey
        }
      });

      return {
        success: true,
        data: response.data.currencies || []
      };
    } catch (error) {
      console.error('Erro ao obter moedas disponíveis:', error);
      // Retornar moedas padrão em caso de erro
      return {
        success: true,
        data: ['btc', 'eth', 'usdt', 'ltc', 'bch', 'ada', 'dot', 'bnb']
      };
    }
  }

  /**
   * Valida webhook da PagFly
   * @param {Object} data - Dados do webhook
   * @param {string} signature - Assinatura recebida
   * @returns {boolean} - Se a assinatura é válida
   */
  validatePagFlyWebhook(data, signature) {
    try {
      // Implementar validação específica da PagFly se necessário
      return true; // Por enquanto, aceitar todos
    } catch (error) {
      console.error('Erro ao validar webhook PagFly:', error);
      return false;
    }
  }

  /**
   * Valida webhook do NowPayments
   * @param {string} payload - Payload do webhook
   * @param {string} signature - Assinatura recebida
   * @returns {boolean} - Se a assinatura é válida
   */
  validateNowPaymentsWebhook(payload, signature) {
    try {
      if (!this.nowPaymentsIpnSecret) {
        return true; // Se não há secret configurado, aceitar
      }

      const expectedSignature = crypto
        .createHmac('sha512', this.nowPaymentsIpnSecret)
        .update(payload)
        .digest('hex');

      return signature === expectedSignature;
    } catch (error) {
      console.error('Erro ao validar webhook NowPayments:', error);
      return false;
    }
  }


}

module.exports = new PaymentService();
