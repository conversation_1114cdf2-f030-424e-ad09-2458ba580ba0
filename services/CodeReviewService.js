const { CodeReview, User, Checker } = require('../models');
const SecurityAnalyzer = require('../utils/securityAnalyzer');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const monitor = require('../utils/monitoring');

class CodeReviewService {
  constructor() {
    this.securityAnalyzer = new SecurityAnalyzer();
  }

  /**
   * Submete código para revisão
   */
  async submitCodeForReview(data) {
    const {
      code,
      codeType = 'custom_code',
      submittedBy,
      checkerId = null,
      originalFilename = null
    } = data;

    try {
      // Análise automática de segurança
      const securityReport = this.securityAnalyzer.analyzeCode(code);

      // Determinar configurações baseadas na análise
      const sandboxLevel = this.securityAnalyzer.determineSandboxLevel(securityReport);
      const maxExecutionTime = this.calculateExecutionTime(securityReport);
      const allowedModules = securityReport.allowedModules;

      // Criar registro de revisão
      const codeReview = await CodeReview.create({
        checker_id: checkerId,
        submitted_by: submittedBy,
        code_content: code,
        code_type: codeType,
        original_filename: originalFilename,
        status: securityReport.score >= 70 ? 'pending' : 'needs_revision',
        security_score: securityReport.score,
        security_issues: securityReport.issues,
        execution_sandbox: sandboxLevel,
        max_execution_time: maxExecutionTime,
        allowed_modules: allowedModules,
        test_results: await this.runAutomatedTests(code),
        performance_metrics: await this.analyzePerformance(code)
      });

      // Registrar evento de monitoramento
      monitor.recordSecurityEvent('code_review_submitted', {
        reviewId: codeReview.id,
        securityScore: securityReport.score,
        issuesCount: securityReport.issues.length
      });

      // Se o score for muito baixo, rejeitar automaticamente
      if (securityReport.score < 30) {
        await this.rejectCodeReview(codeReview.id, 'system', 'Score de segurança muito baixo - revisão automática');
      }

      return {
        success: true,
        reviewId: codeReview.id,
        status: codeReview.status,
        securityReport,
        message: this.getSubmissionMessage(codeReview.status, securityReport.score)
      };

    } catch (err) {
      console.error('Erro ao submeter código para revisão:', err);
      throw new Error('Erro interno ao processar submissão');
    }
  }

  /**
   * Aprova uma revisão de código
   */
  async approveCodeReview(reviewId, reviewedBy, notes = '') {
    try {
      const codeReview = await CodeReview.findByPk(reviewId, {
        include: [
          { model: User, as: 'submitter' },
          { model: Checker, as: 'checker' }
        ]
      });

      if (!codeReview) {
        throw new Error('Revisão não encontrada');
      }

      if (codeReview.status !== 'pending' && codeReview.status !== 'needs_revision') {
        throw new Error('Revisão não está pendente');
      }

      // Gerar hash do código aprovado
      const approvedCodeHash = crypto.createHash('sha256')
        .update(codeReview.code_content)
        .digest('hex');

      // Atualizar revisão
      await codeReview.update({
        status: 'approved',
        reviewed_by: reviewedBy,
        review_notes: notes,
        approved_code_hash: approvedCodeHash,
        reviewed_at: new Date()
      });

      // Atualizar o checker com o hash aprovado
      if (codeReview.checker) {
        await codeReview.checker.update({
          approved_code_hash: approvedCodeHash
        });
      }

      // Se estiver associado a um checker, atualizar o checker
      if (codeReview.checker_id) {
        await this.updateCheckerWithApprovedCode(codeReview);
      }

      // Registrar evento de monitoramento
      monitor.recordSecurityEvent('code_review_approved', {
        reviewId: reviewId,
        checkerId: codeReview.checker_id
      });

      return {
        success: true,
        message: 'Código aprovado com sucesso',
        approvedCodeHash
      };

    } catch (err) {
      console.error('Erro ao aprovar código:', err);
      throw err;
    }
  }

  /**
   * Rejeita uma revisão de código
   */
  async rejectCodeReview(reviewId, reviewedBy, reason) {
    try {
      const codeReview = await CodeReview.findByPk(reviewId);

      if (!codeReview) {
        throw new Error('Revisão não encontrada');
      }

      await codeReview.update({
        status: 'rejected',
        reviewed_by: reviewedBy,
        rejection_reason: reason,
        reviewed_at: new Date()
      });

      // Registrar evento de monitoramento
      monitor.recordSecurityEvent('code_review_rejected', {
        reviewId: reviewId,
        reason: reason
      });

      return {
        success: true,
        message: 'Código rejeitado'
      };

    } catch (err) {
      console.error('Erro ao rejeitar código:', err);
      throw err;
    }
  }

  /**
   * Solicita revisão de código
   */
  async requestRevision(reviewId, reviewedBy, notes) {
    try {
      const codeReview = await CodeReview.findByPk(reviewId);

      if (!codeReview) {
        throw new Error('Revisão não encontrada');
      }

      await codeReview.update({
        status: 'needs_revision',
        reviewed_by: reviewedBy,
        review_notes: notes,
        reviewed_at: new Date()
      });

      return {
        success: true,
        message: 'Revisão solicitada'
      };

    } catch (err) {
      console.error('Erro ao solicitar revisão:', err);
      throw err;
    }
  }

  /**
   * Lista revisões pendentes (CORRIGIDO - tratamento de erros melhorado)
   */
  async getPendingReviews(limit = 50, offset = 0) {
    try {
      console.log('🔍 Buscando revisões pendentes...');

      // Buscar revisões com tratamento de erro robusto
      let reviews = [];
      let total = 0;

      try {
        const result = await CodeReview.findAndCountAll({
          where: {
            status: ['pending', 'needs_revision']
          },
          include: [
            {
              model: User,
              as: 'submitter',
              attributes: ['id', 'usuario', 'role'],
              required: false // LEFT JOIN para evitar erro se usuário não existir
            },
            {
              model: User,
              as: 'reviewer',
              attributes: ['id', 'usuario'],
              required: false
            },
            {
              model: Checker,
              as: 'checker',
              attributes: ['id', 'name', 'title', 'status'],
              required: false
            }
          ],
          order: [
            ['status', 'ASC'], // Pendentes primeiro
            ['submitted_at', 'ASC'] // Mais antigos primeiro
          ],
          limit,
          offset
        });

        reviews = result.rows;
        total = result.count;
        console.log(`✅ Encontradas ${total} revisões pendentes`);

      } catch (includeError) {
        console.warn('⚠️  Erro com includes, tentando busca simples:', includeError.message);

        // Fallback: busca simples sem includes
        const result = await CodeReview.findAndCountAll({
          where: {
            status: ['pending', 'needs_revision']
          },
          order: [
            ['status', 'ASC'],
            ['submitted_at', 'ASC']
          ],
          limit,
          offset
        });

        reviews = result.rows;
        total = result.count;

        // Buscar dados relacionados manualmente
        for (const review of reviews) {
          try {
            if (review.submitted_by) {
              review.submitter = await User.findByPk(review.submitted_by, {
                attributes: ['id', 'usuario', 'role']
              });
            }
            if (review.reviewed_by) {
              review.reviewer = await User.findByPk(review.reviewed_by, {
                attributes: ['id', 'usuario']
              });
            }
            if (review.checker_id) {
              review.checker = await Checker.findByPk(review.checker_id, {
                attributes: ['id', 'name', 'title', 'status']
              });
            }
          } catch (relationError) {
            console.warn(`⚠️  Erro ao buscar dados relacionados para review ${review.id}:`, relationError.message);
          }
        }
      }

      // Contar estatísticas com tratamento de erro
      let pending = 0, needsRevision = 0, approved = 0, rejected = 0;

      try {
        [pending, needsRevision, approved, rejected] = await Promise.all([
          CodeReview.count({ where: { status: 'pending' } }),
          CodeReview.count({ where: { status: 'needs_revision' } }),
          CodeReview.count({ where: { status: 'approved' } }),
          CodeReview.count({ where: { status: 'rejected' } })
        ]);
      } catch (countError) {
        console.warn('⚠️  Erro ao contar estatísticas:', countError.message);
        // Calcular a partir dos dados já obtidos
        pending = reviews.filter(r => r.status === 'pending').length;
        needsRevision = reviews.filter(r => r.status === 'needs_revision').length;
      }

      // Buscar checkers sem aprovação com tratamento de erro
      let checkersWithoutApproval = [];

      try {
        const { Op } = require('sequelize');

        checkersWithoutApproval = await Checker.findAll({
          where: {
            [Op.or]: [
              { approved_code_hash: null },
              { approved_code_hash: '' }
            ],
            [Op.and]: [
              {
                [Op.or]: [
                  { custom_code: { [Op.ne]: null } },
                  { module_path: { [Op.ne]: null } }
                ]
              }
            ]
          },
          attributes: ['id', 'name', 'title', 'status']
        });
      } catch (checkerError) {
        console.warn('⚠️  Erro ao buscar checkers sem aprovação:', checkerError.message);
      }

      console.log(`📊 Estatísticas: ${pending} pendentes, ${needsRevision} precisam revisão, ${approved} aprovados, ${rejected} rejeitados`);

      return {
        reviews: reviews || [],
        total: total || 0,
        pending: pending || 0,
        needsRevision: needsRevision || 0,
        approved: approved || 0,
        rejected: rejected || 0,
        checkersWithoutApproval: checkersWithoutApproval.length || 0,
        checkersNeedingReview: checkersWithoutApproval || []
      };

    } catch (err) {
      console.error('❌ Erro crítico ao listar revisões pendentes:', err);

      // Retornar estrutura vazia em caso de erro crítico
      return {
        reviews: [],
        total: 0,
        pending: 0,
        needsRevision: 0,
        approved: 0,
        rejected: 0,
        checkersWithoutApproval: 0,
        checkersNeedingReview: []
      };
    }
  }

  /**
   * Força criação de code review para checkers sem aprovação (CORRIGIDO)
   */
  async createMissingCodeReviews() {
    try {
      console.log('🔧 Iniciando criação de code reviews faltantes...');

      const { Checker } = require('../models');
      const { Op } = require('sequelize');

      // Buscar checkers sem aprovação com tratamento de erro
      let checkersWithoutApproval = [];

      try {
        checkersWithoutApproval = await Checker.findAll({
          where: {
            [Op.or]: [
              { approved_code_hash: null },
              { approved_code_hash: '' }
            ],
            [Op.and]: [
              {
                [Op.or]: [
                  { custom_code: { [Op.ne]: null } },
                  { module_path: { [Op.ne]: null } }
                ]
              }
            ]
          }
        });

        console.log(`📋 Encontrados ${checkersWithoutApproval.length} checkers sem aprovação`);

      } catch (searchError) {
        console.error('❌ Erro ao buscar checkers sem aprovação:', searchError);

        // Fallback: buscar todos os checkers e filtrar manualmente
        try {
          const allCheckers = await Checker.findAll();
          checkersWithoutApproval = allCheckers.filter(checker =>
            (!checker.approved_code_hash || checker.approved_code_hash === '') &&
            (checker.custom_code || checker.module_path)
          );
          console.log(`📋 Fallback: encontrados ${checkersWithoutApproval.length} checkers`);
        } catch (fallbackError) {
          console.error('❌ Erro no fallback:', fallbackError);
          throw new Error('Não foi possível buscar checkers');
        }
      }

      const results = [];

      for (const checker of checkersWithoutApproval) {
        try {
          // Verificar se já existe code review pendente
          const existingReview = await CodeReview.findOne({
            where: {
              checker_id: checker.id,
              status: ['pending', 'needs_revision']
            }
          });

          if (existingReview) {
            results.push({
              checkerId: checker.id,
              checkerName: checker.name,
              status: 'existing_review',
              reviewId: existingReview.id
            });
            continue;
          }

          // Determinar código a ser revisado
          let codeContent = null;
          let codeType = 'custom_code';

          if (checker.custom_code) {
            codeContent = checker.custom_code;
          } else if (checker.module_path) {
            const fs = require('fs');
            const path = require('path');
            const modulePath = path.join(process.cwd(), checker.module_path);

            if (fs.existsSync(modulePath)) {
              codeContent = fs.readFileSync(modulePath, 'utf8');
              codeType = 'module';
            }
          }

          if (!codeContent) {
            results.push({
              checkerId: checker.id,
              checkerName: checker.name,
              status: 'no_code',
              message: 'Checker sem código definido'
            });
            continue;
          }

          // Criar code review
          const result = await this.submitCodeForReview({
            code: codeContent,
            codeType: codeType,
            submittedBy: checker.programmer_id || 1, // Admin como fallback
            checkerId: checker.id
          });

          results.push({
            checkerId: checker.id,
            checkerName: checker.name,
            status: 'created',
            reviewId: result.reviewId
          });

        } catch (err) {
          results.push({
            checkerId: checker.id,
            checkerName: checker.name,
            status: 'error',
            error: err.message
          });
        }
      }

      return {
        success: true,
        processedCheckers: results.length,
        results
      };

    } catch (err) {
      console.error('Erro ao criar code reviews faltantes:', err);
      throw err;
    }
  }

  /**
   * Obtém detalhes de uma revisão (CORRIGIDO)
   */
  async getReviewDetails(reviewId) {
    try {
      console.log(`🔍 Buscando detalhes da revisão ${reviewId}...`);

      let codeReview;

      try {
        // Tentar busca com includes primeiro
        codeReview = await CodeReview.findByPk(reviewId, {
          include: [
            {
              model: User,
              as: 'submitter',
              attributes: ['id', 'usuario', 'role', 'email'],
              required: false
            },
            {
              model: User,
              as: 'reviewer',
              attributes: ['id', 'usuario'],
              required: false
            },
            {
              model: Checker,
              as: 'checker',
              required: false
            }
          ]
        });
      } catch (includeError) {
        console.warn('⚠️  Erro com includes, tentando busca simples:', includeError.message);

        // Fallback: busca simples
        codeReview = await CodeReview.findByPk(reviewId);

        if (codeReview) {
          // Buscar dados relacionados manualmente
          try {
            if (codeReview.submitted_by) {
              codeReview.submitter = await User.findByPk(codeReview.submitted_by, {
                attributes: ['id', 'usuario', 'role', 'email']
              });
            }
            if (codeReview.reviewed_by) {
              codeReview.reviewer = await User.findByPk(codeReview.reviewed_by, {
                attributes: ['id', 'usuario']
              });
            }
            if (codeReview.checker_id) {
              codeReview.checker = await Checker.findByPk(codeReview.checker_id);
            }
          } catch (relationError) {
            console.warn('⚠️  Erro ao buscar dados relacionados:', relationError.message);
          }
        }
      }

      if (!codeReview) {
        throw new Error('Revisão não encontrada');
      }

      console.log(`✅ Detalhes da revisão ${reviewId} carregados com sucesso`);
      return codeReview;

    } catch (err) {
      console.error('Erro ao obter detalhes da revisão:', err);
      throw err;
    }
  }

  /**
   * Executa testes automatizados no código
   */
  async runAutomatedTests(code) {
    const tests = [];

    try {
      // Teste 1: Verificar se exporta uma função
      const exportTest = /module\.exports\s*=/.test(code);
      tests.push({
        name: 'Module Export',
        passed: exportTest,
        description: 'Código deve exportar uma função'
      });

      // Teste 2: Verificar se é uma função assíncrona
      const asyncTest = /async\s+function|=>\s*{|async\s*\(/.test(code);
      tests.push({
        name: 'Async Function',
        passed: asyncTest,
        description: 'Função deve ser assíncrona'
      });

      // Teste 3: Verificar tratamento de erro
      const errorHandlingTest = /try\s*{|catch\s*\(/.test(code);
      tests.push({
        name: 'Error Handling',
        passed: errorHandlingTest,
        description: 'Código deve ter tratamento de erro'
      });

      // Teste 4: Verificar validação de entrada
      const inputValidationTest = /if\s*\(\s*!/.test(code);
      tests.push({
        name: 'Input Validation',
        passed: inputValidationTest,
        description: 'Código deve validar entradas'
      });

      return {
        totalTests: tests.length,
        passedTests: tests.filter(t => t.passed).length,
        tests: tests
      };

    } catch (err) {
      console.error('Erro ao executar testes automatizados:', err);
      return {
        totalTests: 0,
        passedTests: 0,
        tests: [],
        error: err.message
      };
    }
  }

  /**
   * Analisa performance do código
   */
  async analyzePerformance(code) {
    try {
      const metrics = {
        linesOfCode: code.split('\n').length,
        characters: code.length,
        complexity: this.calculateComplexity(code),
        estimatedMemoryUsage: this.estimateMemoryUsage(code),
        potentialBottlenecks: this.findBottlenecks(code)
      };

      return metrics;

    } catch (err) {
      console.error('Erro ao analisar performance:', err);
      return {};
    }
  }

  /**
   * Calcula complexidade ciclomática
   */
  calculateComplexity(code) {
    const patterns = [
      /if\s*\(/g,
      /else\s*if\s*\(/g,
      /while\s*\(/g,
      /for\s*\(/g,
      /switch\s*\(/g,
      /case\s+/g,
      /catch\s*\(/g,
      /&&/g,
      /\|\|/g
    ];

    let complexity = 1; // Base complexity

    patterns.forEach(pattern => {
      const matches = code.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    });

    return complexity;
  }

  /**
   * Estima uso de memória
   */
  estimateMemoryUsage(code) {
    let estimate = 0;

    // Arrays e objetos grandes
    const arrayMatches = code.match(/\[\s*[^\]]{50,}\s*\]/g);
    if (arrayMatches) estimate += arrayMatches.length * 1000;

    const objectMatches = code.match(/\{\s*[^}]{100,}\s*\}/g);
    if (objectMatches) estimate += objectMatches.length * 2000;

    // Strings grandes
    const stringMatches = code.match(/['"`][^'"`]{100,}['"`]/g);
    if (stringMatches) estimate += stringMatches.length * 500;

    return Math.max(estimate, 1000); // Mínimo 1KB
  }

  /**
   * Encontra possíveis gargalos
   */
  findBottlenecks(code) {
    const bottlenecks = [];

    // Loops aninhados
    if (/for\s*\([^}]*for\s*\(/s.test(code)) {
      bottlenecks.push('Loops aninhados detectados');
    }

    // Recursão sem limite
    if (/function\s+\w+[^}]*\1\s*\(/s.test(code)) {
      bottlenecks.push('Possível recursão infinita');
    }

    // Operações síncronas em loops
    if (/for\s*\([^}]*(?:readFileSync|execSync)/s.test(code)) {
      bottlenecks.push('Operações síncronas em loops');
    }

    return bottlenecks;
  }

  /**
   * Calcula tempo de execução baseado na análise
   */
  calculateExecutionTime(securityReport) {
    let baseTime = 5000; // 5 segundos base

    // Reduzir tempo para códigos mais seguros
    if (securityReport.score >= 90) {
      baseTime = 10000; // 10 segundos
    } else if (securityReport.score >= 70) {
      baseTime = 7000; // 7 segundos
    } else if (securityReport.score < 50) {
      baseTime = 3000; // 3 segundos
    }

    return baseTime;
  }

  /**
   * Atualiza checker com código aprovado
   */
  async updateCheckerWithApprovedCode(codeReview) {
    try {
      const updateData = {
        approved_code_hash: codeReview.approved_code_hash
      };

      if (codeReview.code_type === 'custom_code') {
        updateData.custom_code = codeReview.code_content;
      } else if (codeReview.code_type === 'module') {
        // Salvar arquivo do módulo
        const filename = `checker_${codeReview.checker_id}_${Date.now()}.js`;
        const modulePath = `modules/checkers/${filename}`;
        const fullPath = path.join(process.cwd(), modulePath);

        // Criar diretório se não existir
        const dir = path.dirname(fullPath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }

        fs.writeFileSync(fullPath, codeReview.code_content);
        updateData.module_path = modulePath;
      }

      await Checker.update(updateData, {
        where: { id: codeReview.checker_id }
      });

    } catch (err) {
      console.error('Erro ao atualizar checker:', err);
      throw err;
    }
  }

  /**
   * Gera mensagem de submissão
   */
  getSubmissionMessage(status, score) {
    if (status === 'approved') {
      return 'Código aprovado automaticamente - score de segurança excelente';
    } else if (status === 'pending') {
      return `Código submetido para revisão manual (Score: ${score}/100)`;
    } else if (status === 'needs_revision') {
      return `Código precisa de correções antes da aprovação (Score: ${score}/100)`;
    } else {
      return 'Código rejeitado - problemas de segurança críticos';
    }
  }

  /**
   * Obtém estatísticas de revisões
   */
  async getReviewStats() {
    try {
      const { Op } = require('sequelize');
      const db = require('../config/database');

      // Contar por status
      const pending = await CodeReview.count({ where: { status: 'pending' } });
      const approved = await CodeReview.count({ where: { status: 'approved' } });
      const rejected = await CodeReview.count({ where: { status: 'rejected' } });
      const needsRevision = await CodeReview.count({ where: { status: 'needs_revision' } });

      const totalReviews = await CodeReview.count();

      // Score médio de segurança
      const avgScoreResult = await CodeReview.findAll({
        attributes: [
          [db.fn('AVG', db.col('security_score')), 'avgScore']
        ],
        where: {
          security_score: { [Op.not]: null }
        }
      });

      // Tempo médio de processamento
      let avgProcessingTime = 0;
      try {
        const avgTimeResult = await CodeReview.findAll({
          attributes: [
            [db.fn('AVG', db.literal('TIMESTAMPDIFF(HOUR, submitted_at, reviewed_at)')), 'avgHours']
          ],
          where: {
            reviewed_at: { [Op.not]: null }
          }
        });
        avgProcessingTime = avgTimeResult[0]?.dataValues?.avgHours || 0;
      } catch (timeErr) {
        console.warn('Erro ao calcular tempo médio:', timeErr.message);
      }

      return {
        totalReviews,
        pending,
        approved,
        rejected,
        needsRevision,
        avgSecurityScore: avgScoreResult[0]?.dataValues?.avgScore || 0,
        avgProcessingTime: parseFloat(avgProcessingTime) || 0,
        byStatus: {
          pending,
          approved,
          rejected,
          needs_revision: needsRevision
        }
      };

    } catch (err) {
      console.error('Erro ao obter estatísticas de revisão:', err);
      return {
        totalReviews: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        needsRevision: 0,
        avgSecurityScore: 0,
        avgProcessingTime: 0,
        byStatus: {
          pending: 0,
          approved: 0,
          rejected: 0,
          needs_revision: 0
        }
      };
    }
  }
}

module.exports = new CodeReviewService();
