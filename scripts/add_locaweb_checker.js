/**
 * Script para adicionar o Checker Locaweb ao sistema
 * Execute: node scripts/add_locaweb_checker.js
 */

const { Checker, Category } = require('../models');

async function addLocawebChecker() {
  try {
    console.log('🔄 Adicionando Checker Locaweb...');

    // Verificar se já existe
    const existingChecker = await Checker.findOne({
      where: { name: 'locaweb' }
    });

    if (existingChecker) {
      console.log('⚠️  Checker Locaweb já existe. Atualizando...');
      
      await existingChecker.update({
        title: 'Checker Locaweb Webmail',
        description: 'Valida credenciais de email Locaweb através do webmail-seguro.com.br. Formato: email:senha',
        price: 2.0,
        status: 'active',
        icon: 'fas fa-envelope',
        charge_type: 'per_test',
        required_role: 'user',
        display_order: 50,
        module_path: 'modules/checkers/locaweb_checker.js'
      });
      
      console.log('✅ Checker Locaweb atualizado com sucesso!');
    } else {
      // Buscar categoria Email
      let emailCategory = await Category.findOne({
        where: { name: 'Email' }
      });

      // Se não existir, criar categoria Email
      if (!emailCategory) {
        console.log('📁 Criando categoria Email...');
        emailCategory = await Category.create({
          name: 'Email',
          description: 'Checkers relacionados a validação de emails',
          icon: 'fas fa-envelope',
          display_order: 3
        });
        console.log('✅ Categoria Email criada!');
      }

      // Criar o checker
      const newChecker = await Checker.create({
        name: 'locaweb',
        endpoint: 'locaweb',
        title: 'Checker Locaweb Webmail',
        description: 'Valida credenciais de email Locaweb através do webmail-seguro.com.br. Formato: email:senha',
        category_id: emailCategory.id,
        price: 2.0,
        status: 'active',
        icon: 'fas fa-envelope',
        charge_type: 'per_test',
        required_role: 'user',
        display_order: 50,
        module_path: 'modules/checkers/locaweb_checker.js',
        programmer_id: 1
      });

      console.log('✅ Checker Locaweb adicionado com sucesso!');
      console.log(`📋 ID: ${newChecker.id}`);
      console.log(`🔗 Endpoint: /api/dynamic/${newChecker.endpoint}`);
    }

    // Mostrar informações do checker
    const checker = await Checker.findOne({
      where: { name: 'locaweb' },
      include: [{ model: Category, as: 'category' }]
    });

    console.log('\n📊 Informações do Checker:');
    console.log(`   Nome: ${checker.name}`);
    console.log(`   Título: ${checker.title}`);
    console.log(`   Endpoint: ${checker.endpoint}`);
    console.log(`   Categoria: ${checker.category?.name || 'N/A'}`);
    console.log(`   Preço: ${checker.price} crédito(s)`);
    console.log(`   Status: ${checker.status}`);
    console.log(`   Módulo: ${checker.module_path}`);
    console.log(`   URL: /api/dynamic/${checker.endpoint}?lista=email:senha`);

    console.log('\n🎯 Como usar:');
    console.log('   Formato: email:senha');
    console.log('   Exemplo: <EMAIL>:minhasenha');
    console.log('   URL: /api/dynamic/locaweb?lista=<EMAIL>:minhasenha');

  } catch (error) {
    console.error('❌ Erro ao adicionar checker:', error);
    console.error(error.stack);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  addLocawebChecker()
    .then(() => {
      console.log('\n🚀 Script concluído!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Erro fatal:', error);
      process.exit(1);
    });
}

module.exports = addLocawebChecker;
