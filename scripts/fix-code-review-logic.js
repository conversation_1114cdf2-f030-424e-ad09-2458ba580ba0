#!/usr/bin/env node

/**
 * Script para corrigir e aplicar a lógica de revisão de código
 * Execute: node scripts/fix-code-review-logic.js
 */

require('dotenv').config();
const { Checker, CodeReview, User } = require('../models');
const codeReviewService = require('../services/CodeReviewService');
const { validateCheckerExecution } = require('../utils/checkerLoader');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

async function fixCodeReviewLogic() {
  try {
    console.log('🔧 Iniciando correção da lógica de revisão de código...\n');

    // codeReviewService já é uma instância

    // 1. Verificar checkers sem aprovação
    console.log('📊 Analisando checkers sem aprovação...');
    
    const checkersWithoutApproval = await Checker.findAll({
      where: {
        [require('sequelize').Op.or]: [
          { approved_code_hash: null },
          { approved_code_hash: '' }
        ],
        [require('sequelize').Op.and]: [
          {
            [require('sequelize').Op.or]: [
              { custom_code: { [require('sequelize').Op.ne]: null } },
              { module_path: { [require('sequelize').Op.ne]: null } }
            ]
          }
        ]
      }
    });

    console.log(`   📋 Encontrados ${checkersWithoutApproval.length} checkers sem aprovação`);

    // 2. Verificar quais podem ser executados
    console.log('\n🔍 Verificando status de execução dos checkers...');
    
    let blockableCheckers = 0;
    let executableCheckers = 0;
    
    for (const checker of checkersWithoutApproval) {
      const canExecute = await validateCheckerExecution(checker);
      
      if (canExecute) {
        executableCheckers++;
        console.log(`   ✅ ${checker.name} - Pode ser executado`);
      } else {
        blockableCheckers++;
        console.log(`   ❌ ${checker.name} - BLOQUEADO (código não aprovado)`);
      }
    }

    console.log(`\n📊 Resumo da validação:`);
    console.log(`   ✅ Executáveis: ${executableCheckers}`);
    console.log(`   ❌ Bloqueados: ${blockableCheckers}`);

    // 3. Criar revisões faltantes
    if (blockableCheckers > 0) {
      console.log('\n📝 Criando revisões de código faltantes...');
      
      const result = await codeReviewService.createMissingCodeReviews();
      
      console.log(`   📋 Processados: ${result.processedCheckers} checkers`);
      
      result.results.forEach(item => {
        switch (item.status) {
          case 'created':
            console.log(`   ✅ ${item.checkerName} - Revisão criada (ID: ${item.reviewId})`);
            break;
          case 'existing_review':
            console.log(`   ⏳ ${item.checkerName} - Já possui revisão pendente (ID: ${item.reviewId})`);
            break;
          case 'no_code':
            console.log(`   ⚠️  ${item.checkerName} - Sem código definido`);
            break;
          case 'error':
            console.log(`   ❌ ${item.checkerName} - Erro: ${item.error}`);
            break;
        }
      });
    }

    // 4. Verificar revisões pendentes
    console.log('\n📋 Verificando revisões pendentes...');
    
    const pendingReviews = await codeReviewService.getPendingReviews(100, 0);
    
    console.log(`   ⏳ Pendentes: ${pendingReviews.pending}`);
    console.log(`   🔄 Necessitam revisão: ${pendingReviews.needsRevision}`);
    console.log(`   ✅ Aprovados: ${pendingReviews.approved}`);
    console.log(`   ❌ Rejeitados: ${pendingReviews.rejected}`);

    // 5. Listar revisões que precisam de atenção
    if (pendingReviews.pending > 0 || pendingReviews.needsRevision > 0) {
      console.log('\n🚨 REVISÕES QUE PRECISAM DE ATENÇÃO:');
      
      pendingReviews.reviews.forEach(review => {
        const submitter = review.submitter ? review.submitter.usuario : 'N/A';
        const checker = review.checker ? review.checker.name : 'N/A';
        const statusIcon = review.status === 'pending' ? '⏳' : '🔄';
        
        console.log(`   ${statusIcon} ID ${review.id} - ${submitter} - ${checker} (${review.status})`);
      });
      
      console.log(`\n💡 Acesse /admin/code-reviews para revisar estes códigos.`);
    }

    // 6. Verificar integridade dos hashes
    console.log('\n🔐 Verificando integridade dos códigos aprovados...');
    
    const approvedReviews = await CodeReview.findAll({
      where: { status: 'approved' },
      include: [{ model: Checker, as: 'checker', required: false }]
    });

    let integrityIssues = 0;
    
    for (const review of approvedReviews) {
      if (review.checker && review.checker.approved_code_hash) {
        if (review.approved_code_hash !== review.checker.approved_code_hash) {
          integrityIssues++;
          console.log(`   ⚠️  Inconsistência: Checker ${review.checker.name} - Hash não confere`);
        }
      }
    }

    if (integrityIssues === 0) {
      console.log(`   ✅ Todos os hashes estão íntegros`);
    } else {
      console.log(`   ⚠️  Encontradas ${integrityIssues} inconsistências de hash`);
    }

    // 7. Estatísticas finais
    console.log('\n📊 ESTATÍSTICAS FINAIS:');
    console.log('=====================================');
    console.log(`🔍 Checkers analisados: ${checkersWithoutApproval.length}`);
    console.log(`❌ Checkers bloqueados: ${blockableCheckers}`);
    console.log(`⏳ Revisões pendentes: ${pendingReviews.pending}`);
    console.log(`🔄 Revisões necessárias: ${pendingReviews.needsRevision}`);
    console.log(`✅ Códigos aprovados: ${pendingReviews.approved}`);
    console.log(`🔐 Problemas de integridade: ${integrityIssues}`);
    console.log('=====================================');

    if (blockableCheckers > 0 || pendingReviews.pending > 0 || pendingReviews.needsRevision > 0) {
      console.log('\n🚨 AÇÃO NECESSÁRIA:');
      console.log('   1. Acesse /admin/code-reviews');
      console.log('   2. Revise e aprove/rejeite os códigos pendentes');
      console.log('   3. Checkers com código não aprovado estão BLOQUEADOS');
      console.log('   4. Usuários não conseguirão executar checkers bloqueados');
    } else {
      console.log('\n🎉 TUDO OK! Todos os códigos estão aprovados e funcionando.');
    }

    console.log('\n✅ Correção da lógica de revisão de código concluída!\n');

  } catch (err) {
    console.error('❌ Erro ao corrigir lógica de revisão:', err);
    process.exit(1);
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  fixCodeReviewLogic();
}

module.exports = fixCodeReviewLogic;
