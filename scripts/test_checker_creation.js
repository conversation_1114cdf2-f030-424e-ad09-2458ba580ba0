#!/usr/bin/env node

/**
 * Script para testar a criação de checkers e code reviews
 */

const db = require('../config/database');
const { Checker, CodeReview, User, Category } = require('../models');
const codeReviewService = require('../services/CodeReviewService');

async function testCheckerCreation() {
  try {
    console.log('🧪 Testando criação de checker com code review...\n');

    // Buscar usuário admin
    const adminUser = await User.findOne({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.error('❌ Usuário admin não encontrado!');
      return;
    }

    // Buscar uma categoria
    const category = await Category.findOne();
    if (!category) {
      console.error('❌ Nenhuma categoria encontrada!');
      return;
    }

    console.log(`👤 Usando admin: ${adminUser.usuario} (ID: ${adminUser.id})`);
    console.log(`📁 Usando categoria: ${category.name} (ID: ${category.id})\n`);

    // Código de exemplo para o checker
    const testCode = `// Teste Checker - Validador de Email
const axios = require('axios');

module.exports = async function(email) {
  try {
    // Validar formato do email
    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        status: 'DIE',
        message: 'Formato de email inválido'
      };
    }

    // Verificar se o domínio existe
    const domain = email.split('@')[1];
    const response = await axios.get(\`https://dns.google/resolve?name=\${domain}&type=MX\`, {
      timeout: 5000
    });

    if (response.data.Answer && response.data.Answer.length > 0) {
      return {
        status: 'LIVE',
        message: 'Email válido',
        data: {
          email: email,
          domain: domain,
          mx_records: response.data.Answer.length
        }
      };
    } else {
      return {
        status: 'DIE',
        message: 'Domínio não possui registros MX'
      };
    }
  } catch (error) {
    return {
      status: 'DIE',
      message: 'Erro ao validar email: ' + error.message
    };
  }
};`;

    // Criar checker
    console.log('📝 Criando checker de teste...');
    const testChecker = await Checker.create({
      name: `test_email_validator_${Date.now()}`,
      endpoint: `test-email-${Date.now()}`,
      title: 'Teste - Validador de Email',
      description: 'Checker de teste para validar emails e verificar domínios MX',
      category_id: category.id,
      price: 1.0,
      status: 'active',
      icon: 'fa fa-envelope',
      charge_type: 'per_test',
      required_role: 'user',
      display_order: 999,
      custom_code: testCode,
      programmer_id: adminUser.id
    });

    console.log(`✅ Checker criado: ID ${testChecker.id} - ${testChecker.title}`);

    // Criar code review usando o serviço
    console.log('\n🔍 Criando code review...');
    const reviewResult = await codeReviewService.submitCodeForReview({
      checkerId: testChecker.id,
      submittedBy: adminUser.id,
      code: testCode,
      codeType: 'custom_code',
      originalFilename: 'email_validator.js'
    });

    console.log(`✅ Code review criado: ID ${reviewResult.reviewId}`);
    console.log(`📊 Status: ${reviewResult.status}`);
    console.log(`🔒 Score de segurança: ${reviewResult.securityReport.score}/100`);
    console.log(`⚠️  Issues encontrados: ${reviewResult.securityReport.issues.length}`);
    console.log(`💬 Mensagem: ${reviewResult.message}\n`);

    // Verificar se aparece nas revisões pendentes
    console.log('🔍 Verificando revisões pendentes...');
    const pendingReviews = await codeReviewService.getPendingReviews();
    
    console.log(`📊 Total de revisões pendentes: ${pendingReviews.total}`);
    console.log(`⏳ Pendentes: ${pendingReviews.pending}`);
    console.log(`⚠️  Precisam revisão: ${pendingReviews.needsRevision}\n`);

    // Listar as revisões pendentes
    if (pendingReviews.reviews.length > 0) {
      console.log('📋 Revisões pendentes:');
      pendingReviews.reviews.forEach((review, index) => {
        console.log(`  ${index + 1}. ID: ${review.id} | Status: ${review.status} | Score: ${review.security_score} | Checker: ${review.checker ? review.checker.title : 'N/A'}`);
      });
    } else {
      console.log('❌ Nenhuma revisão pendente encontrada!');
    }

    console.log('\n🎉 Teste concluído com sucesso!');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
    throw error;
  }
}

// Executar teste
async function main() {
  try {
    await db.authenticate();
    console.log('🗄️  Conectado ao banco de dados\n');
    
    await testCheckerCreation();
    
    console.log('\n✅ Script executado com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Erro fatal:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testCheckerCreation };
