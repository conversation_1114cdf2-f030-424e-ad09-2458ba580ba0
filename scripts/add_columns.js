// Script para adicionar as colunas module_path e custom_code à tabela checkers
require('dotenv').config();
const db = require('../config/database');

async function addColumns() {
  try {
    console.log('Conectando ao banco de dados...');
    await db.authenticate();
    console.log('Conexão estabelecida com sucesso.');

    // Verificar se a coluna module_path já existe
    console.log('Verificando se a coluna module_path existe...');
    const [modulePathResults] = await db.query(`
      SELECT COUNT(*) as count
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'checkers'
      AND COLUMN_NAME = 'module_path'
    `);

    if (modulePathResults[0].count === 0) {
      console.log('Adicionando coluna module_path...');
      await db.query('ALTER TABLE checkers ADD COLUMN module_path VARCHAR(255) NULL AFTER display_order');
      console.log('Coluna module_path adicionada com sucesso!');
    } else {
      console.log('Coluna module_path já existe.');
    }

    // Verificar se a coluna custom_code já existe
    console.log('Verificando se a coluna custom_code existe...');
    const [customCodeResults] = await db.query(`
      SELECT COUNT(*) as count
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'checkers'
      AND COLUMN_NAME = 'custom_code'
    `);

    if (customCodeResults[0].count === 0) {
      console.log('Adicionando coluna custom_code...');
      await db.query('ALTER TABLE checkers ADD COLUMN custom_code TEXT NULL AFTER module_path');
      console.log('Coluna custom_code adicionada com sucesso!');
    } else {
      console.log('Coluna custom_code já existe.');
    }

    console.log('Operação concluída com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro ao adicionar colunas:', error);
    process.exit(1);
  }
}

addColumns();
