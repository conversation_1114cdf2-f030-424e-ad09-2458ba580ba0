/**
 * Script para adicionar o checker de GayGay ao banco de dados
 * 
 * Execute este script com: node scripts/add_gaygay_checker.js
 */

const path = require('path');
const fs = require('fs');
const { Checker, Category } = require('../models');

async function addGayGayChecker() {
  try {
    console.log('Iniciando adição do checker de GayGay...');
    
    // Verificar se existe uma categoria para consultas de pessoas
    let category = await Category.findOne({
      where: {
        name: 'Consultas de Pessoas'
      }
    });
    
    // Se não existir, criar a categoria
    if (!category) {
      console.log('Categoria "Consultas de Pessoas" não encontrada. Criando...');
      
      category = await Category.create({
        name: 'Consultas de Pessoas',
        description: 'Consultas de dados pessoais como CPF, RG, etc.',
        status: 'active',
        display_order: 1
      });
      
      console.log('Categoria criada com sucesso!');
    }
    
    // Verificar se o checker já existe
    const existingChecker = await Checker.findOne({
      where: {
        endpoint: 'gaygay'
      }
    });
    
    // Verificar se o diretório de módulos existe
    const modulesDir = path.join(__dirname, '..', 'modules', 'checkers');
    if (!fs.existsSync(modulesDir)) {
      console.log('Criando diretório de módulos...');
      fs.mkdirSync(modulesDir, { recursive: true });
    }
    
    if (existingChecker) {
      console.log('Checker de GayGay já existe. Atualizando...');
      
      // Atualizar o checker
      await existingChecker.update({
        title: 'Consulta GayGay',
        description: 'Consulta de dados pessoais através do sistema GayGay',
        category_id: category.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-user',
        charge_type: 'per_test',
        required_role: 'user',
        display_order: 2,
        module_path: 'modules/checkers/gaygay_checker.js'
      });
      
      console.log('Checker de GayGay atualizado com sucesso!');
    } else {
      console.log('Criando checker de GayGay...');
      
      // Criar o checker
      await Checker.create({
        name: 'gaygay',
        endpoint: 'gaygay',
        title: 'Consulta GayGay',
        description: 'Consulta de dados pessoais através do sistema GayGay',
        category_id: category.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-user',
        background_image: null,
        charge_type: 'per_test',
        required_role: 'user',
        display_order: 2,
        module_path: 'modules/checkers/gaygay_checker.js',
        custom_code: null
      });
      
      console.log('Checker de GayGay criado com sucesso!');
    }

    console.log('Operação concluída com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro ao adicionar checker de GayGay:', error);
    process.exit(1);
  }
}

addGayGayChecker();
