#!/usr/bin/env node

/**
 * Script para executar migração do banco de dados
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');

async function runMigration() {
  try {
    console.log('🗄️  Executando migração do banco de dados...\n');

    // Importar configuração do banco
    const db = require('../config/database');

    // Ler arquivo de migração
    const migrationPath = path.join(__dirname, '../migrations/20241201000000-create-code-reviews.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Dividir em comandos individuais (remover comentários e comandos vazios)
    const commands = migrationSQL
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--') && !cmd.startsWith('/*'));

    console.log(`📝 Executando ${commands.length} comandos SQL...\n`);

    // Executar cada comando
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      
      if (command.toLowerCase().includes('create table')) {
        const tableName = command.match(/create table (?:if not exists )?`?(\w+)`?/i)?.[1];
        console.log(`📋 Criando tabela: ${tableName}`);
      } else if (command.toLowerCase().includes('alter table')) {
        const tableName = command.match(/alter table `?(\w+)`?/i)?.[1];
        console.log(`🔧 Alterando tabela: ${tableName}`);
      } else if (command.toLowerCase().includes('select')) {
        console.log(`🔍 Executando verificação...`);
      }

      try {
        await db.query(command);
        console.log(`   ✅ Comando ${i + 1} executado com sucesso`);
      } catch (err) {
        if (err.message.includes('already exists') || err.message.includes('Duplicate')) {
          console.log(`   ⚠️  Comando ${i + 1} já aplicado (ignorando)`);
        } else {
          console.error(`   ❌ Erro no comando ${i + 1}:`, err.message);
          // Continuar com outros comandos
        }
      }
    }

    console.log('\n🎉 Migração concluída!\n');

    // Verificar se as tabelas foram criadas
    console.log('🔍 Verificando tabelas criadas...');
    
    try {
      const [tables] = await db.query(`
        SELECT TABLE_NAME, TABLE_ROWS, CREATE_TIME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME IN ('code_reviews', 'checkers')
      `);

      console.log('\n📊 Status das tabelas:');
      tables.forEach(table => {
        console.log(`   ${table.TABLE_NAME}: ${table.TABLE_ROWS || 0} registros`);
      });

      // Verificar se a coluna approved_code_hash foi adicionada
      const [columns] = await db.query(`
        SELECT COLUMN_NAME 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'checkers' 
          AND COLUMN_NAME = 'approved_code_hash'
      `);

      if (columns.length > 0) {
        console.log('   ✅ Coluna approved_code_hash adicionada à tabela checkers');
      } else {
        console.log('   ⚠️  Coluna approved_code_hash não encontrada na tabela checkers');
      }

    } catch (err) {
      console.error('❌ Erro ao verificar tabelas:', err.message);
    }

    // Fechar conexão
    await db.close();
    
    console.log('\n✅ Migração aplicada com sucesso!');
    console.log('🚀 Agora você pode reiniciar a aplicação para usar as novas funcionalidades.\n');

  } catch (err) {
    console.error('❌ Erro ao executar migração:', err);
    process.exit(1);
  }
}

// Executar migração
runMigration();
