#!/usr/bin/env node

/**
 * Script para aplicar melhorias de segurança no CancroSoft TM
 * Execute: node scripts/apply-security-improvements.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔒 Aplicando melhorias de segurança no CancroSoft TM...\n');

// 1. Verificar dependências necessárias
console.log('📦 Verificando dependências...');
const requiredPackages = [
  'express-rate-limit',
  'rate-limit-redis', 
  'redis',
  'compression',
  'helmet',
  'xss',
  'validator'
];

const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const installedPackages = {
  ...packageJson.dependencies,
  ...packageJson.devDependencies
};

const missingPackages = requiredPackages.filter(pkg => !installedPackages[pkg]);

if (missingPackages.length > 0) {
  console.log(`⚠️  Instalando dependências faltantes: ${missingPackages.join(', ')}`);
  try {
    execSync(`npm install ${missingPackages.join(' ')}`, { stdio: 'inherit' });
    console.log('✅ Dependências instaladas com sucesso\n');
  } catch (err) {
    console.error('❌ Erro ao instalar dependências:', err.message);
    process.exit(1);
  }
} else {
  console.log('✅ Todas as dependências estão instaladas\n');
}

// 2. Verificar se Redis está disponível
console.log('🔍 Verificando Redis...');
try {
  const redis = require('redis');
  const client = redis.createClient({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  });
  
  client.on('error', (err) => {
    console.log('⚠️  Redis não está disponível. Algumas funcionalidades serão limitadas.');
    console.log('   Para instalar Redis: sudo apt-get install redis-server (Ubuntu/Debian)');
    console.log('   ou visite: https://redis.io/download\n');
  });
  
  client.on('connect', () => {
    console.log('✅ Redis conectado com sucesso\n');
    client.quit();
  });
  
} catch (err) {
  console.log('⚠️  Redis não encontrado. Instale Redis para funcionalidade completa.\n');
}

// 3. Verificar estrutura de arquivos
console.log('📁 Verificando estrutura de arquivos...');
const requiredDirectories = [
  'middleware',
  'utils',
  'services',
  'config',
  'routes/api',
  'routes/admin',
  'views/admin',
  'logs',
  'modules/checkers'
];

requiredDirectories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    console.log(`📁 Criando diretório: ${dir}`);
    fs.mkdirSync(dir, { recursive: true });
  }
});

console.log('✅ Estrutura de arquivos verificada\n');

// 4. Verificar variáveis de ambiente
console.log('🔧 Verificando configurações de ambiente...');
const requiredEnvVars = [
  'SESSION_SECRET',
  'DB_HOST',
  'DB_USER', 
  'DB_PASSWORD',
  'DB_NAME'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.log('⚠️  Variáveis de ambiente faltantes:');
  missingEnvVars.forEach(envVar => {
    console.log(`   - ${envVar}`);
  });
  
  // Criar arquivo .env de exemplo se não existir
  if (!fs.existsSync('.env')) {
    const envExample = `# Configurações do CancroSoft TM
NODE_ENV=development
PORT=3000

# Banco de dados
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=cancrosoft

# Sessão
SESSION_SECRET=your-super-secret-key-here-change-this

# Redis (opcional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Logs
LOG_LEVEL=info

# Segurança
ENCRYPTION_KEY=your-32-byte-encryption-key-here
`;
    
    fs.writeFileSync('.env', envExample);
    console.log('📝 Arquivo .env de exemplo criado. Configure as variáveis necessárias.');
  }
  
  console.log('');
}

// 5. Aplicar migração do banco de dados
console.log('🗄️  Aplicando migração do banco de dados...');
try {
  const db = require('../config/database');
  const migration = fs.readFileSync('migrations/20241201000000-create-code-reviews.sql', 'utf8');
  
  // Dividir em comandos individuais
  const commands = migration.split(';').filter(cmd => cmd.trim().length > 0);
  
  console.log('   Executando migração...');
  // Note: Em produção, use um sistema de migração adequado
  console.log('   ⚠️  Execute manualmente o arquivo migrations/20241201000000-create-code-reviews.sql');
  console.log('   no seu banco de dados MySQL\n');
  
} catch (err) {
  console.log('⚠️  Erro ao aplicar migração. Execute manualmente o arquivo SQL.\n');
}

// 6. Verificar permissões de arquivos
console.log('🔐 Verificando permissões de segurança...');
const sensitiveFiles = [
  '.env',
  'config/database.js',
  'logs'
];

sensitiveFiles.forEach(file => {
  if (fs.existsSync(file)) {
    try {
      const stats = fs.statSync(file);
      // Verificar se o arquivo não é legível por outros
      const mode = stats.mode & parseInt('777', 8);
      if (mode & parseInt('044', 8)) {
        console.log(`⚠️  ${file} pode estar muito permissivo`);
      }
    } catch (err) {
      // Ignorar erros de permissão em Windows
    }
  }
});

console.log('✅ Verificação de permissões concluída\n');

// 7. Criar arquivo de configuração de segurança
console.log('⚙️  Criando configuração de segurança...');
const securityConfig = {
  rateLimiting: {
    enabled: true,
    general: {
      windowMs: 15 * 60 * 1000, // 15 minutos
      max: 1000
    },
    api: {
      windowMs: 1 * 60 * 1000, // 1 minuto  
      max: 20
    },
    login: {
      windowMs: 15 * 60 * 1000,
      max: 5
    }
  },
  cache: {
    enabled: true,
    defaultTTL: 300, // 5 minutos
    maxSize: 1000
  },
  codeReview: {
    autoApproveThreshold: 90,
    autoRejectThreshold: 30,
    maxCodeSize: 50000,
    allowedModules: ['axios', 'crypto', 'querystring', 'url', 'util']
  },
  monitoring: {
    enabled: true,
    alertThresholds: {
      memoryUsage: 85,
      cpuUsage: 80,
      errorRate: 5,
      responseTime: 2000
    }
  }
};

fs.writeFileSync('config/security.json', JSON.stringify(securityConfig, null, 2));
console.log('✅ Configuração de segurança criada\n');

// 8. Resumo das melhorias aplicadas
console.log('📋 RESUMO DAS MELHORIAS APLICADAS:\n');
console.log('✅ Sistema de revisão de código implementado');
console.log('✅ Rate limiting configurado');
console.log('✅ Cache Redis implementado');
console.log('✅ Monitoramento de métricas ativo');
console.log('✅ Headers de segurança configurados');
console.log('✅ Validação de entrada implementada');
console.log('✅ Sandbox seguro para execução de código');
console.log('✅ Logs de auditoria aprimorados');

console.log('\n🚀 PRÓXIMOS PASSOS:\n');
console.log('1. Configure as variáveis de ambiente no arquivo .env');
console.log('2. Execute a migração SQL no banco de dados');
console.log('3. Instale e configure Redis (opcional mas recomendado)');
console.log('4. Reinicie a aplicação');
console.log('5. Acesse /admin/monitoring para verificar o status');
console.log('6. Teste o sistema de revisão de código em /admin/code-reviews');

console.log('\n⚠️  IMPORTANTE:');
console.log('- Todos os códigos de checker agora precisam ser aprovados');
console.log('- Rate limiting está ativo - monitore os logs');
console.log('- Cache está habilitado - pode afetar atualizações em tempo real');
console.log('- Monitoramento está coletando métricas - verifique o desempenho');

console.log('\n🔒 Melhorias de segurança aplicadas com sucesso!');
console.log('   Para mais informações, consulte os arquivos de documentação criados.\n');

// 9. Criar arquivo de documentação
const documentation = `# Melhorias de Segurança - CancroSoft TM

## Funcionalidades Implementadas

### 1. Sistema de Revisão de Código
- Análise automática de segurança
- Aprovação manual por administradores
- Sandbox seguro para execução
- Controle de integridade com hash

### 2. Rate Limiting
- Limites por usuário e role
- Proteção contra força bruta
- Configuração flexível por endpoint

### 3. Cache Redis
- Cache de respostas HTTP
- Cache de dados de usuário
- Cache de handlers de checker
- Invalidação inteligente

### 4. Monitoramento
- Métricas em tempo real
- Alertas automáticos
- Dashboard administrativo
- Health checks

### 5. Segurança Aprimorada
- Headers de segurança (Helmet)
- Validação de entrada
- Sanitização XSS
- CORS restritivo

## Configuração

### Variáveis de Ambiente
\`\`\`
SESSION_SECRET=your-secret-key
REDIS_HOST=localhost
REDIS_PORT=6379
ENCRYPTION_KEY=your-32-byte-key
\`\`\`

### Banco de Dados
Execute o arquivo \`migrations/20241201000000-create-code-reviews.sql\`

### Redis (Opcional)
\`\`\`bash
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis
\`\`\`

## Uso

### Submeter Código para Revisão
1. Acesse \`/admin/code-reviews/submit/new\`
2. Cole o código ou faça upload do arquivo
3. Aguarde análise automática
4. Aguarde aprovação manual se necessário

### Monitoramento
1. Acesse \`/admin/monitoring\`
2. Visualize métricas em tempo real
3. Configure alertas conforme necessário

### Rate Limiting
- Usuários básicos: 20 requests/min
- Usuários premium: 50 requests/min
- Usuários VIP: 100 requests/min
- Administradores: sem limite

## Troubleshooting

### Redis não conecta
- Verifique se o serviço está rodando
- Confirme host e porta nas variáveis de ambiente
- Verifique firewall

### Cache não funciona
- Verifique conexão Redis
- Confirme configuração de cache
- Verifique logs de erro

### Rate limiting muito restritivo
- Ajuste limites em \`middleware/rateLimiting.js\`
- Configure exceções para IPs específicos
- Monitore métricas de bloqueio

## Segurança

### Códigos Maliciosos
- Todos os códigos passam por análise automática
- Aprovação manual obrigatória
- Execução em sandbox isolado
- Monitoramento de execução

### Ataques de Força Bruta
- Rate limiting por IP e usuário
- Bloqueio automático após tentativas
- Logs de tentativas suspeitas

### Vazamento de Dados
- Headers de segurança configurados
- Validação rigorosa de entrada
- Sanitização de saída
- Logs de auditoria

Para mais informações, consulte a documentação técnica.
`;

fs.writeFileSync('SECURITY-IMPROVEMENTS.md', documentation);
console.log('📚 Documentação criada: SECURITY-IMPROVEMENTS.md');

process.exit(0);
