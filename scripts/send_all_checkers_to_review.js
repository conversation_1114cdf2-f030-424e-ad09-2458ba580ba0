#!/usr/bin/env node

/**
 * Script para enviar todos os checkers para code review
 */

const db = require('../config/database');
const { Checker, CodeReview, User } = require('../models');
const codeReviewService = require('../services/CodeReviewService');
const fs = require('fs');
const path = require('path');

async function sendAllCheckersToReview() {
  try {
    console.log('🔍 Buscando TODOS os checkers para enviar ao code review...\n');

    // Buscar TODOS os checkers (aprovados e não aprovados)
    const allCheckers = await Checker.findAll({
      include: [
        {
          model: User,
          as: 'programmer',
          attributes: ['id', 'usuario'],
          required: false
        }
      ],
      order: [['id', 'ASC']]
    });

    console.log(`📊 Encontrados ${allCheckers.length} checkers para processar\n`);

    if (allCheckers.length === 0) {
      console.log('❌ Nenhum checker encontrado!');
      return;
    }

    // Buscar usuário admin para ser o submitter
    const adminUser = await User.findOne({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.error('❌ Usuário admin não encontrado!');
      return;
    }

    console.log(`👤 Usando admin: ${adminUser.usuario} (ID: ${adminUser.id})\n`);

    let processedCount = 0;
    let createdReviews = 0;
    let skippedCount = 0;
    let alreadyApproved = 0;

    for (const checker of allCheckers) {
      console.log(`🔧 Processando: ${checker.title} (${checker.name})`);

      // Verificar se já existe code review para este checker
      const existingReview = await CodeReview.findOne({
        where: { checker_id: checker.id }
      });

      if (existingReview) {
        console.log(`  ⏭️  Já possui code review (ID: ${existingReview.id}) - pulando`);
        skippedCount++;
        processedCount++;
        continue;
      }

      // Verificar se o checker já está aprovado (tem approved_code_hash)
      const isAlreadyApproved = checker.approved_code_hash && checker.approved_code_hash.length > 0;

      let codeContent = '';
      let codeType = 'custom_code';
      let originalFilename = null;

      // Determinar o código do checker
      if (checker.custom_code) {
        codeContent = checker.custom_code;
        codeType = 'custom_code';
        console.log(`  📝 Usando custom_code (${codeContent.length} caracteres)`);
      } else if (checker.module_path) {
        const modulePath = path.join(process.cwd(), checker.module_path);
        if (fs.existsSync(modulePath)) {
          codeContent = fs.readFileSync(modulePath, 'utf8');
          codeType = 'module';
          originalFilename = path.basename(modulePath);
          console.log(`  📁 Usando module_path: ${checker.module_path}`);
        } else {
          console.log(`  ⚠️  Arquivo do módulo não encontrado: ${modulePath}`);
          // Criar código padrão para checkers sem código
          codeContent = generateDefaultCode(checker);
          console.log(`  🔧 Criado código padrão`);
        }
      } else {
        // Criar código padrão para checkers sem código
        codeContent = generateDefaultCode(checker);
        console.log(`  🔧 Criado código padrão (sem código existente)`);
      }

      if (!codeContent) {
        console.log(`  ❌ Não foi possível obter código - pulando`);
        skippedCount++;
        processedCount++;
        continue;
      }

      try {
        if (isAlreadyApproved) {
          console.log(`  ✅ Checker já aprovado - criando code review aprovado automaticamente`);

          // Criar code review já aprovado para checkers que já estão funcionando
          const codeReview = await CodeReview.create({
            checker_id: checker.id,
            submitted_by: checker.programmer_id || adminUser.id,
            reviewed_by: adminUser.id,
            code_content: codeContent,
            code_type: codeType,
            original_filename: originalFilename || `${checker.name}.js`,
            status: 'approved',
            security_score: 95, // Score alto para checkers já aprovados
            security_issues: [],
            review_notes: 'Aprovado automaticamente - Checker já estava funcionando no sistema',
            approved_code_hash: checker.approved_code_hash,
            execution_sandbox: 'standard',
            max_execution_time: 15000,
            allowed_modules: ['axios', 'crypto', 'fs', 'path', 'querystring', 'url'],
            test_results: { status: 'passed', message: 'Checker já em produção' },
            performance_metrics: { executionTime: 100, memoryUsage: 50 },
            submitted_at: new Date(),
            reviewed_at: new Date()
          });

          console.log(`  ✅ Code review aprovado criado: ID ${codeReview.id}`);
          alreadyApproved++;

        } else {
          // Criar code review pendente para checkers novos
          const reviewResult = await codeReviewService.submitCodeForReview({
            checkerId: checker.id,
            submittedBy: checker.programmer_id || adminUser.id,
            code: codeContent,
            codeType: codeType,
            originalFilename: originalFilename || `${checker.name}.js`
          });

          console.log(`  ✅ Code review criado: ID ${reviewResult.reviewId}`);
          console.log(`  📊 Status: ${reviewResult.status} | Score: ${reviewResult.securityReport.score}/100`);

          if (reviewResult.securityReport.issues.length > 0) {
            console.log(`  ⚠️  Issues: ${reviewResult.securityReport.issues.length}`);
          }
        }

        createdReviews++;

      } catch (error) {
        console.log(`  ❌ Erro ao criar code review: ${error.message}`);
        skippedCount++;
      }

      processedCount++;
      console.log('');
    }

    console.log('🎉 Processo concluído!');
    console.log(`📊 Estatísticas:`);
    console.log(`  - Checkers processados: ${processedCount}`);
    console.log(`  - Code reviews criados: ${createdReviews}`);
    console.log(`  - Checkers já aprovados: ${alreadyApproved}`);
    console.log(`  - Checkers pulados: ${skippedCount}`);

    // Verificar revisões pendentes após o processo
    console.log('\n🔍 Verificando revisões pendentes...');
    const pendingReviews = await codeReviewService.getPendingReviews();

    console.log(`📊 Total de revisões pendentes: ${pendingReviews.total}`);
    console.log(`⏳ Pendentes: ${pendingReviews.pending}`);
    console.log(`⚠️  Precisam revisão: ${pendingReviews.needsRevision}`);

  } catch (error) {
    console.error('❌ Erro durante o processo:', error);
    throw error;
  }
}

/**
 * Gera código padrão para checkers sem implementação
 */
function generateDefaultCode(checker) {
  return `// Checker: ${checker.title}
// Endpoint: /${checker.endpoint}
// Descrição: ${checker.description || 'Sem descrição'}
// Categoria: ${checker.category_id}
// Preço: ${checker.price} créditos

const axios = require('axios');

module.exports = async function(input) {
  try {
    // TODO: Implementar lógica específica do checker
    console.log('Processando input:', input);

    // Validação básica de entrada
    if (!input || typeof input !== 'string' || input.trim().length === 0) {
      return {
        status: 'DIE',
        message: 'Input inválido ou vazio'
      };
    }

    // Exemplo de implementação básica
    // Substitua esta lógica pela implementação real do checker

    // Simular processamento
    await new Promise(resolve => setTimeout(resolve, 100));

    // Retorno padrão - DEVE SER IMPLEMENTADO
    return {
      status: 'LIVE',
      message: 'Checker funcionando - implementação pendente',
      data: {
        input: input,
        processed_at: new Date().toISOString(),
        checker: '${checker.name}'
      }
    };

  } catch (error) {
    console.error('Erro no checker ${checker.name}:', error);
    return {
      status: 'DIE',
      message: 'Erro interno: ' + error.message
    };
  }
};

// Configurações do checker
module.exports.config = {
  name: '${checker.name}',
  title: '${checker.title}',
  description: '${checker.description || 'Checker em desenvolvimento'}',
  price: ${checker.price},
  timeout: 15000,
  retries: 1
};`;
}

// Executar script
async function main() {
  try {
    await db.authenticate();
    console.log('🗄️  Conectado ao banco de dados\n');

    await sendAllCheckersToReview();

    console.log('\n✅ Script executado com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Erro fatal:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { sendAllCheckersToReview };
