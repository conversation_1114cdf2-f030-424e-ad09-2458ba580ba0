#!/usr/bin/env node

/**
 * Script para corrigir problemas de estrutura do banco de dados
 * Execute: node scripts/fix-database-structure.js
 */

require('dotenv').config();
const db = require('../config/database');

async function fixDatabaseStructure() {
  try {
    console.log('🔧 Iniciando correção da estrutura do banco de dados...\n');

    // 1. Adicionar colunas de timestamp na tabela usuarios se não existirem
    console.log('📅 Verificando timestamps na tabela usuarios...');
    
    try {
      await db.query(`
        ALTER TABLE usuarios 
        ADD COLUMN createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        ADD COLUMN updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      `);
      console.log('   ✅ Colunas de timestamp adicionadas à tabela usuarios');
    } catch (err) {
      if (err.message.includes('Duplicate column')) {
        console.log('   ⚠️  Colunas de timestamp já existem na tabela usuarios');
      } else {
        console.log('   ⚠️  Erro ao adicionar timestamps:', err.message);
      }
    }

    // 2. Adicionar role_granted na tabela plans se não existir
    console.log('🎯 Verificando coluna role_granted na tabela plans...');
    
    try {
      await db.query(`
        ALTER TABLE plans 
        ADD COLUMN role_granted ENUM('user', 'premium', 'vip') DEFAULT 'user'
      `);
      console.log('   ✅ Coluna role_granted adicionada à tabela plans');
    } catch (err) {
      if (err.message.includes('Duplicate column')) {
        console.log('   ⚠️  Coluna role_granted já existe na tabela plans');
      } else {
        console.log('   ⚠️  Erro ao adicionar role_granted:', err.message);
      }
    }

    // 3. Criar índices importantes se não existirem
    console.log('📊 Criando índices importantes...');
    
    const indexes = [
      // Índices para usuarios
      { table: 'usuarios', name: 'idx_usuarios_status', fields: 'status' },
      { table: 'usuarios', name: 'idx_usuarios_role', fields: 'role' },
      { table: 'usuarios', name: 'idx_usuarios_lastLogin', fields: 'lastLogin' },
      
      // Índices para transactions
      { table: 'transactions', name: 'idx_transactions_user_type', fields: 'user_id, type' },
      { table: 'transactions', name: 'idx_transactions_user_status', fields: 'user_id, status' },
      { table: 'transactions', name: 'idx_transactions_created', fields: 'createdAt' },
      
      // Índices para activity_logs
      { table: 'activity_logs', name: 'idx_activity_user_action', fields: 'user_id, action' },
      { table: 'activity_logs', name: 'idx_activity_user_created', fields: 'user_id, createdAt' },
      { table: 'activity_logs', name: 'idx_activity_resource', fields: 'resource_type, resource_id' },
      
      // Índices para plans
      { table: 'plans', name: 'idx_plans_status', fields: 'status' },
      { table: 'plans', name: 'idx_plans_price', fields: 'price' },
      { table: 'plans', name: 'idx_plans_role_granted', fields: 'role_granted' },
      
      // Índices para checkers
      { table: 'checkers', name: 'idx_checkers_category_status', fields: 'category_id, status' },
      { table: 'checkers', name: 'idx_checkers_status_order', fields: 'status, display_order' },
      
      // Índices para categories
      { table: 'categories', name: 'idx_categories_status_order', fields: 'status, display_order' }
    ];

    for (const index of indexes) {
      try {
        await db.query(`
          CREATE INDEX ${index.name} ON ${index.table} (${index.fields})
        `);
        console.log(`   ✅ Índice ${index.name} criado`);
      } catch (err) {
        if (err.message.includes('Duplicate key')) {
          console.log(`   ⚠️  Índice ${index.name} já existe`);
        } else {
          console.log(`   ⚠️  Erro ao criar índice ${index.name}:`, err.message);
        }
      }
    }

    // 4. Otimizar tabelas
    console.log('⚡ Otimizando tabelas...');
    
    const tables = ['usuarios', 'checkers', 'categories', 'transactions', 'activity_logs', 'plans', 'subscriptions', 'payment_transactions'];
    
    for (const table of tables) {
      try {
        await db.query(`OPTIMIZE TABLE ${table}`);
        console.log(`   ✅ Tabela ${table} otimizada`);
      } catch (err) {
        console.log(`   ⚠️  Erro ao otimizar tabela ${table}:`, err.message);
      }
    }

    // 5. Verificar estatísticas finais
    console.log('\n📊 Verificando estatísticas finais...');
    
    const [tables_info] = await db.query(`
      SELECT 
        TABLE_NAME,
        TABLE_ROWS,
        DATA_LENGTH,
        INDEX_LENGTH,
        (DATA_LENGTH + INDEX_LENGTH) as TOTAL_SIZE
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
      ORDER BY TOTAL_SIZE DESC
    `);

    console.log('\n📋 Resumo das tabelas:');
    tables_info.forEach(table => {
      const sizeInMB = (table.TOTAL_SIZE / 1024 / 1024).toFixed(2);
      console.log(`   ${table.TABLE_NAME}: ${table.TABLE_ROWS || 0} registros, ${sizeInMB} MB`);
    });

    await db.close();
    
    console.log('\n🎉 Correção da estrutura do banco concluída com sucesso!');
    console.log('🚀 O banco de dados está otimizado e pronto para uso.\n');

  } catch (err) {
    console.error('❌ Erro ao corrigir estrutura do banco:', err);
    process.exit(1);
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  fixDatabaseStructure();
}

module.exports = fixDatabaseStructure;
