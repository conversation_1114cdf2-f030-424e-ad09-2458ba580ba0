// Script para adicionar o checker de CPF ao banco de dados
require('dotenv').config();
const db = require('../config/database');
const { Checker, Category } = require('../models');
const fs = require('fs');
const path = require('path');

async function addCpfChecker() {
  try {
    console.log('Conectando ao banco de dados...');
    await db.authenticate();
    console.log('Conexão estabelecida com sucesso.');

    // Verificar se já existe uma categoria para consultas de pessoas
    console.log('Verificando se existe categoria para consultas de pessoas...');
    let category = await Category.findOne({
      where: { name: 'Consultas de Pessoas' }
    });

    // Se não existir, criar a categoria
    if (!category) {
      console.log('Criando categoria para consultas de pessoas...');
      category = await Category.create({
        name: 'Consultas de Pessoas',
        description: 'Consultas de dados pessoais como CPF, RG, etc.',
        status: 'active',
        display_order: 1
      });
      console.log('Categoria criada com sucesso!');
    } else {
      console.log('Categoria já existe.');
    }

    // Verificar se o checker de CPF já existe
    console.log('Verificando se o checker de CPF já existe...');
    const existingChecker = await Checker.findOne({
      where: { name: 'cpf' }
    });

    if (existingChecker) {
      console.log('Checker de CPF já existe. Atualizando...');
      
      // Copiar o arquivo de template para o diretório de módulos
      const templatePath = path.join(__dirname, '..', 'modules', 'checkers', 'cpf_checker_template.js');
      
      // Atualizar o checker
      await existingChecker.update({
        endpoint: 'cpf',
        title: 'Consulta de CPF',
        description: 'Consulta dados completos de pessoas físicas através do CPF',
        category_id: category.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-id-card',
        charge_type: 'per_test',
        required_role: 'user',
        display_order: 1,
        module_path: 'modules/checkers/cpf_checker_template.js'
      });
      
      console.log('Checker de CPF atualizado com sucesso!');
    } else {
      console.log('Criando checker de CPF...');
      
      // Criar o checker
      await Checker.create({
        name: 'cpf',
        endpoint: 'cpf',
        title: 'Consulta de CPF',
        description: 'Consulta dados completos de pessoas físicas através do CPF',
        category_id: category.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-id-card',
        background_image: null,
        charge_type: 'per_test',
        required_role: 'user',
        display_order: 1,
        module_path: 'modules/checkers/cpf_checker_template.js',
        custom_code: null
      });
      
      console.log('Checker de CPF criado com sucesso!');
    }

    console.log('Operação concluída com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro ao adicionar checker de CPF:', error);
    process.exit(1);
  }
}

addCpfChecker();
