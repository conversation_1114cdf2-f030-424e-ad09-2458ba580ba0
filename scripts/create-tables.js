#!/usr/bin/env node

/**
 * Script para criar tabelas necessárias para as melhorias de segurança
 */

require('dotenv').config();

async function createTables() {
  try {
    console.log('🗄️  Criando tabelas necessárias...\n');

    const db = require('../config/database');

    // 1. Criar tabela code_reviews
    console.log('📋 Criando tabela code_reviews...');
    
    const createCodeReviewsTable = `
      CREATE TABLE IF NOT EXISTS code_reviews (
        id int(11) NOT NULL AUTO_INCREMENT,
        checker_id int(11) DEFAULT NULL,
        submitted_by int(11) NOT NULL,
        reviewed_by int(11) DEFAULT NULL,
        code_content longtext NOT NULL,
        code_type enum('module','custom_code') NOT NULL DEFAULT 'custom_code',
        original_filename varchar(255) DEFAULT NULL,
        status enum('pending','approved','rejected','needs_revision') NOT NULL DEFAULT 'pending',
        security_score int(11) DEFAULT NULL,
        security_issues json DEFAULT NULL,
        review_notes text DEFAULT NULL,
        rejection_reason text DEFAULT NULL,
        approved_code_hash varchar(64) DEFAULT NULL,
        execution_sandbox enum('strict','limited','standard') NOT NULL DEFAULT 'strict',
        max_execution_time int(11) NOT NULL DEFAULT 5000,
        allowed_modules json DEFAULT NULL,
        test_results json DEFAULT NULL,
        performance_metrics json DEFAULT NULL,
        submitted_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        reviewed_at datetime DEFAULT NULL,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_code_reviews_status (status),
        KEY idx_code_reviews_submitted_by (submitted_by),
        KEY idx_code_reviews_reviewed_by (reviewed_by),
        KEY idx_code_reviews_checker_id (checker_id),
        KEY idx_code_reviews_submitted_at (submitted_at),
        KEY idx_code_reviews_approved_hash (approved_code_hash)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    await db.query(createCodeReviewsTable);
    console.log('   ✅ Tabela code_reviews criada');

    // 2. Adicionar coluna approved_code_hash na tabela checkers
    console.log('🔧 Adicionando coluna approved_code_hash na tabela checkers...');
    
    try {
      await db.query(`
        ALTER TABLE checkers 
        ADD COLUMN approved_code_hash varchar(64) DEFAULT NULL 
        COMMENT 'Hash do código aprovado' 
        AFTER custom_code
      `);
      console.log('   ✅ Coluna approved_code_hash adicionada');
    } catch (err) {
      if (err.message.includes('Duplicate column')) {
        console.log('   ⚠️  Coluna approved_code_hash já existe');
      } else {
        throw err;
      }
    }

    // 3. Adicionar índice para approved_code_hash
    console.log('📊 Adicionando índice para approved_code_hash...');
    
    try {
      await db.query(`
        ALTER TABLE checkers 
        ADD INDEX idx_checkers_approved_hash (approved_code_hash)
      `);
      console.log('   ✅ Índice idx_checkers_approved_hash criado');
    } catch (err) {
      if (err.message.includes('Duplicate key')) {
        console.log('   ⚠️  Índice idx_checkers_approved_hash já existe');
      } else {
        throw err;
      }
    }

    // 4. Adicionar foreign keys se não existirem
    console.log('🔗 Configurando foreign keys...');
    
    try {
      await db.query(`
        ALTER TABLE code_reviews 
        ADD CONSTRAINT fk_code_reviews_submitted_by 
        FOREIGN KEY (submitted_by) REFERENCES usuarios(id) ON DELETE CASCADE
      `);
      console.log('   ✅ Foreign key fk_code_reviews_submitted_by criada');
    } catch (err) {
      if (err.message.includes('Duplicate foreign key')) {
        console.log('   ⚠️  Foreign key fk_code_reviews_submitted_by já existe');
      } else {
        console.log('   ⚠️  Erro ao criar foreign key submitted_by:', err.message);
      }
    }

    try {
      await db.query(`
        ALTER TABLE code_reviews 
        ADD CONSTRAINT fk_code_reviews_reviewed_by 
        FOREIGN KEY (reviewed_by) REFERENCES usuarios(id) ON DELETE SET NULL
      `);
      console.log('   ✅ Foreign key fk_code_reviews_reviewed_by criada');
    } catch (err) {
      if (err.message.includes('Duplicate foreign key')) {
        console.log('   ⚠️  Foreign key fk_code_reviews_reviewed_by já existe');
      } else {
        console.log('   ⚠️  Erro ao criar foreign key reviewed_by:', err.message);
      }
    }

    try {
      await db.query(`
        ALTER TABLE code_reviews 
        ADD CONSTRAINT fk_code_reviews_checker 
        FOREIGN KEY (checker_id) REFERENCES checkers(id) ON DELETE SET NULL
      `);
      console.log('   ✅ Foreign key fk_code_reviews_checker criada');
    } catch (err) {
      if (err.message.includes('Duplicate foreign key')) {
        console.log('   ⚠️  Foreign key fk_code_reviews_checker já existe');
      } else {
        console.log('   ⚠️  Erro ao criar foreign key checker_id:', err.message);
      }
    }

    // 5. Verificar estrutura final
    console.log('\n🔍 Verificando estrutura final...');
    
    const [tables] = await db.query(`
      SELECT TABLE_NAME, TABLE_ROWS 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME IN ('code_reviews', 'checkers')
    `);

    console.log('\n📊 Tabelas criadas:');
    tables.forEach(table => {
      console.log(`   ${table.TABLE_NAME}: ${table.TABLE_ROWS || 0} registros`);
    });

    // Verificar colunas da tabela checkers
    const [checkerColumns] = await db.query(`
      SELECT COLUMN_NAME 
      FROM information_schema.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'checkers' 
        AND COLUMN_NAME = 'approved_code_hash'
    `);

    if (checkerColumns.length > 0) {
      console.log('   ✅ Coluna approved_code_hash presente na tabela checkers');
    }

    // Verificar colunas da tabela code_reviews
    const [reviewColumns] = await db.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'code_reviews'
    `);

    console.log(`   ✅ Tabela code_reviews tem ${reviewColumns[0].count} colunas`);

    await db.close();
    
    console.log('\n🎉 Todas as tabelas foram criadas com sucesso!');
    console.log('🚀 O sistema de revisão de código está pronto para uso.\n');

  } catch (err) {
    console.error('❌ Erro ao criar tabelas:', err);
    process.exit(1);
  }
}

createTables();
