#!/usr/bin/env node

/**
 * Script para corrigir security_issues que podem ter propriedades undefined
 */

const db = require('../config/database');
const { CodeReview } = require('../models');

async function fixSecurityIssues() {
  try {
    console.log('🔍 Verificando code reviews com security_issues...\n');

    const codeReviews = await CodeReview.findAll({
      where: {
        security_issues: { [db.Sequelize.Op.not]: null }
      }
    });

    console.log(`📊 Encontrados ${codeReviews.length} code reviews com security_issues\n`);

    let fixedCount = 0;

    for (const review of codeReviews) {
      console.log(`🔧 Verificando review ID: ${review.id}`);
      
      let securityIssues = review.security_issues;
      let needsUpdate = false;

      // Verificar se security_issues é uma string (JSON) e converter
      if (typeof securityIssues === 'string') {
        try {
          securityIssues = JSON.parse(securityIssues);
          needsUpdate = true;
          console.log(`  📝 Convertido string JSON para objeto`);
        } catch (e) {
          console.log(`  ⚠️  Erro ao parsear JSON: ${e.message}`);
          securityIssues = [];
          needsUpdate = true;
        }
      }

      // Verificar se é um array
      if (!Array.isArray(securityIssues)) {
        console.log(`  ⚠️  security_issues não é um array, convertendo...`);
        securityIssues = [];
        needsUpdate = true;
      }

      // Corrigir cada issue
      const fixedIssues = securityIssues.map((issue, index) => {
        let fixed = false;
        const fixedIssue = { ...issue };

        // Garantir que severity existe
        if (!fixedIssue.severity) {
          fixedIssue.severity = 'medium';
          fixed = true;
          console.log(`    🔧 Issue ${index}: Adicionado severity padrão 'medium'`);
        }

        // Garantir que type existe
        if (!fixedIssue.type) {
          fixedIssue.type = 'security_issue';
          fixed = true;
          console.log(`    🔧 Issue ${index}: Adicionado type padrão 'security_issue'`);
        }

        // Garantir que description existe
        if (!fixedIssue.description && !fixedIssue.message) {
          fixedIssue.description = 'Problema de segurança detectado';
          fixed = true;
          console.log(`    🔧 Issue ${index}: Adicionado description padrão`);
        }

        if (fixed) {
          needsUpdate = true;
        }

        return fixedIssue;
      });

      // Atualizar se necessário
      if (needsUpdate) {
        await review.update({
          security_issues: fixedIssues
        });
        
        console.log(`  ✅ Review ${review.id} atualizado com ${fixedIssues.length} issues corrigidos`);
        fixedCount++;
      } else {
        console.log(`  ✅ Review ${review.id} já está correto`);
      }

      console.log('');
    }

    console.log('🎉 Processo concluído!');
    console.log(`📊 Estatísticas:`);
    console.log(`  - Reviews verificados: ${codeReviews.length}`);
    console.log(`  - Reviews corrigidos: ${fixedCount}`);

  } catch (error) {
    console.error('❌ Erro durante o processo:', error);
    throw error;
  }
}

// Executar script
async function main() {
  try {
    await db.authenticate();
    console.log('🗄️  Conectado ao banco de dados\n');
    
    await fixSecurityIssues();
    
    console.log('\n✅ Script executado com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Erro fatal:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixSecurityIssues };
