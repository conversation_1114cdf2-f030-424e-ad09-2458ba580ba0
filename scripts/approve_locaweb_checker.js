/**
 * Script para aprovar automaticamente o Checker Locaweb
 * Execute: node scripts/approve_locaweb_checker.js
 */

const { Checker, CodeReview } = require('../models');
const fs = require('fs');
const crypto = require('crypto');
const path = require('path');

async function approveLocawebChecker() {
  try {
    console.log('🔍 Buscando checker Locaweb...');

    // Buscar o checker Locaweb
    const checker = await Checker.findOne({
      where: { name: 'locaweb' }
    });

    if (!checker) {
      console.log('❌ Checker Locaweb não encontrado');
      return;
    }

    console.log(`✅ Checker encontrado: ID ${checker.id}`);

    // Ler o código do arquivo
    const modulePath = path.join(process.cwd(), checker.module_path);
    
    if (!fs.existsSync(modulePath)) {
      console.log('❌ Arquivo do módulo não encontrado:', modulePath);
      return;
    }

    const moduleCode = fs.readFileSync(modulePath, 'utf8');
    const codeHash = crypto.createHash('sha256').update(moduleCode).digest('hex');

    console.log(`📄 Código carregado: ${moduleCode.length} caracteres`);
    console.log(`🔐 Hash do código: ${codeHash.substring(0, 16)}...`);

    // Verificar se já existe um code review
    let codeReview = await CodeReview.findOne({
      where: { approved_code_hash: codeHash }
    });

    if (codeReview) {
      if (codeReview.status === 'approved') {
        console.log('✅ Código já está aprovado!');
        return;
      } else {
        console.log('🔄 Atualizando code review existente...');
        await codeReview.update({
          status: 'approved',
          reviewed_by: 1, // Admin
          reviewed_at: new Date(),
          review_notes: 'Aprovado automaticamente - Checker Locaweb'
        });
      }
    } else {
      console.log('📝 Criando novo code review...');
      codeReview = await CodeReview.create({
        checker_id: checker.id,
        submitted_code: moduleCode,
        approved_code_hash: codeHash,
        status: 'approved',
        submitted_by: checker.programmer_id || 1,
        reviewed_by: 1, // Admin
        submitted_at: new Date(),
        reviewed_at: new Date(),
        review_notes: 'Aprovado automaticamente - Checker Locaweb',
        execution_sandbox: 'standard',
        max_execution_time: 15000,
        security_level: 'medium'
      });
    }

    console.log(`✅ Code Review aprovado: ID ${codeReview.id}`);

    // Atualizar o checker com o hash aprovado
    await checker.update({
      approved_code_hash: codeHash
    });

    console.log('🎯 Checker Locaweb aprovado e pronto para uso!');
    console.log(`🔗 Endpoint disponível: /api/dynamic/${checker.endpoint}`);
    console.log(`📋 Teste: /api/dynamic/${checker.endpoint}?lista=email:senha`);

  } catch (error) {
    console.error('❌ Erro ao aprovar checker:', error);
    console.error(error.stack);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  approveLocawebChecker()
    .then(() => {
      console.log('\n🚀 Script concluído!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Erro fatal:', error);
      process.exit(1);
    });
}

module.exports = approveLocawebChecker;
