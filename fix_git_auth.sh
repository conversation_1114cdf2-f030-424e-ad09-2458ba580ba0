#!/bin/bash

# Solução rápida para o problema de autenticação do Git
# Execute este script no servidor onde está o projeto

echo "🔧 Corrigindo autenticação do Git..."

# Ir para o diretório do projeto (ajuste o caminho se necessário)
cd /var/www/privxploit 2>/dev/null || cd /home/<USER>/privxploit 2>/dev/null || cd ~/privxploit 2>/dev/null

if [ ! -d ".git" ]; then
    echo "❌ Repositório Git não encontrado neste diretório"
    echo "📍 Diretório atual: $(pwd)"
    echo "📁 Conteúdo:"
    ls -la
    exit 1
fi

echo "📍 Repositório encontrado em: $(pwd)"

# Método 1: Configurar credential helper para armazenar credenciais
echo "⚙️  Configurando credential helper..."
git config credential.helper store

# Método 2: Configurar para usar token via URL
echo "🔑 Para usar token de acesso pessoal:"
echo ""
echo "1️⃣  Gere um token em: https://github.com/settings/tokens"
echo "2️⃣  Execute este comando (substitua SEU_TOKEN):"
echo ""
echo "git remote set-url origin https://MatxCoder:<EMAIL>/MatxCoder/privxploit.git"
echo ""

# Método 3: Configurar Git para não pedir senha interativamente
echo "🚫 Desabilitando prompts interativos..."
git config core.askpass ""
git config credential.helper 'cache --timeout=3600'

# Verificar configuração atual
echo "📊 Configuração atual:"
echo "Remote URL: $(git remote get-url origin)"
echo "User: $(git config user.name)"
echo "Email: $(git config user.email)"

echo ""
echo "✅ Configuração aplicada!"
echo ""
echo "💡 Próximos passos:"
echo "1. Gere um token em: https://github.com/settings/tokens"
echo "2. Execute: git remote set-url origin https://MatxCoder:<EMAIL>/MatxCoder/privxploit.git"
echo "3. Teste com: git fetch origin"
