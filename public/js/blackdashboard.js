// Black Dashboard initialization
var blackDashboard = {
  misc: {
    navbar_menu_visible: 0
  },

  checkScrollForTransparentNavbar: function() {
    if ($(document).scrollTop() > 60) {
      if (transparent) {
        transparent = false;
        $('.navbar[color-on-scroll]').removeClass('navbar-transparent');
      }
    } else {
      if (!transparent) {
        transparent = true;
        $('.navbar[color-on-scroll]').addClass('navbar-transparent');
      }
    }
  },

  initMinimizeSidebar: function() {
    if ($('.sidebar-mini').length != 0) {
      sidebar_mini_active = true;
    }

    $('#minimizeSidebar').click(function() {
      var $btn = $(this);

      if (sidebar_mini_active == true) {
        $('body').removeClass('sidebar-mini');
        sidebar_mini_active = false;
        blackDashboard.showSidebarMessage('Sidebar mini deactivated...');
      } else {
        $('body').addClass('sidebar-mini');
        sidebar_mini_active = true;
        blackDashboard.showSidebarMessage('Sidebar mini activated...');
      }

      // we simulate the window Resize so the charts will get updated in realtime.
      var simulateWindowResize = setInterval(function() {
        window.dispatchEvent(new Event('resize'));
      }, 180);

      // we stop the simulation of Window Resize after the animations are completed
      setTimeout(function() {
        clearInterval(simulateWindowResize);
      }, 1000);
    });
  },

  showSidebarMessage: function(message) {
    try {
      $.notify({
        icon: "tim-icons ui-1_bell-53",
        message: message
      }, {
        type: 'info',
        timer: 4000,
        placement: {
          from: 'top',
          align: 'right'
        }
      });
    } catch (e) {
      console.log('Notify library is missing, please make sure you have the notifications library added.');
    }
  }
};

// Global variables
var transparent = true;
var sidebar_mini_active = false;

// Initialize Black Dashboard
$(document).ready(function() {
  // Initialize sidebar mini functionality
  blackDashboard.initMinimizeSidebar();

  // Initialize navbar transparency
  if ($('.navbar[color-on-scroll]').length != 0) {
    $(window).on('scroll', blackDashboard.checkScrollForTransparentNavbar);
  }

  // Initialize perfect scrollbar for sidebar
  if ($('.sidebar .sidebar-wrapper').length != 0 && typeof PerfectScrollbar !== 'undefined') {
    try {
      var ps_sidebar = new PerfectScrollbar('.sidebar .sidebar-wrapper');
    } catch (e) {
      console.log('Perfect Scrollbar initialization failed:', e);
    }
  }

  // Mobile menu toggle
  $('.navbar-toggler').click(function() {
    if (blackDashboard.misc.navbar_menu_visible == 1) {
      $('html').removeClass('nav-open');
      blackDashboard.misc.navbar_menu_visible = 0;
      $('#bodyClick').remove();
      setTimeout(function() {
        $('.navbar-toggler').removeClass('toggled');
      }, 550);
    } else {
      setTimeout(function() {
        $('.navbar-toggler').addClass('toggled');
      }, 580);

      var div = '<div id="bodyClick"></div>';
      $(div).appendTo('body').click(function() {
        $('html').removeClass('nav-open');
        blackDashboard.misc.navbar_menu_visible = 0;
        setTimeout(function() {
          $('.navbar-toggler').removeClass('toggled');
          $('#bodyClick').remove();
        }, 550);
      });

      $('html').addClass('nav-open');
      blackDashboard.misc.navbar_menu_visible = 1;
    }
  });

  // Close dropdowns when clicking outside
  $(document).click(function(e) {
    if (!$(e.target).closest('.dropdown').length) {
      $('.dropdown-menu').removeClass('show');
    }
  });
});
