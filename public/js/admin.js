/**
 * CancroSoft TM Admin Panel JavaScript
 * Optimized for better performance and user experience
 */

// Admin Panel Controller
const AdminPanel = {
  // Initialize admin panel
  init: function() {
    this.setupEventListeners();
    this.setupDataTables();
    this.setupConfirmDialogs();
    this.setupFormValidation();
    this.setupNotifications();
    this.setupSidebarToggle();
  },

  // Setup event listeners
  setupEventListeners: function() {
    // Close alert messages after 5 seconds
    setTimeout(function() {
      $('.alert').fadeOut('slow');
    }, 5000);

    // Prevent double form submission
    $('form').on('submit', function() {
      const submitBtn = $(this).find('button[type="submit"]');
      if (submitBtn.hasClass('disabled')) {
        return false;
      }
      submitBtn.addClass('disabled').html('<i class="fa fa-spinner fa-spin"></i> Processando...');
    });

    // Toggle sidebar on mobile
    $('.navbar-toggler').on('click', function() {
      $('body').toggleClass('sidebar-mini');
    });
  },

  // Setup DataTables for better table experience
  setupDataTables: function() {
    if ($.fn.DataTable) {
      $('.data-table').DataTable({
        responsive: true,
        language: {
          url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json'
        },
        pageLength: 25,
        dom: '<"top"fl>rt<"bottom"ip>',
        ordering: true
      });
    }
  },

  // Setup confirmation dialogs
  setupConfirmDialogs: function() {
    // Confirm delete actions
    $('.btn-delete').on('click', function(e) {
      if (!confirm('Tem certeza que deseja excluir este item? Esta ação não pode ser desfeita.')) {
        e.preventDefault();
        return false;
      }
    });
  },

  // Setup form validation
  setupFormValidation: function() {
    // Add validation classes to required fields
    $('form input[required], form select[required], form textarea[required]').each(function() {
      const label = $('label[for="' + $(this).attr('id') + '"]');
      if (label.length) {
        label.append(' <span class="text-danger">*</span>');
      }
      $(this).addClass('validate');
    });

    // Validate forms on submit
    $('form.needs-validation').on('submit', function(e) {
      if (this.checkValidity() === false) {
        e.preventDefault();
        e.stopPropagation();
        
        // Highlight first invalid field
        const firstInvalid = $(this).find(':invalid').first();
        if (firstInvalid.length) {
          $('html, body').animate({
            scrollTop: firstInvalid.offset().top - 100
          }, 500);
          firstInvalid.focus();
        }
      }
      
      $(this).addClass('was-validated');
    });
  },

  // Setup notifications
  setupNotifications: function() {
    // Show notification function
    window.showNotification = function(message, type = 'primary') {
      $.notify({
        icon: "fa fa-bell",
        message: message
      }, {
        type: type,
        timer: 5000,
        placement: {
          from: "top",
          align: "right"
        },
        animate: {
          enter: 'animated fadeInDown',
          exit: 'animated fadeOutUp'
        }
      });
    };

    // Show notifications from flash messages
    if ($('#success-message').length && $('#success-message').text().trim() !== '') {
      showNotification($('#success-message').text(), 'success');
    }
    
    if ($('#error-message').length && $('#error-message').text().trim() !== '') {
      showNotification($('#error-message').text(), 'danger');
    }
  },

  // Setup sidebar toggle
  setupSidebarToggle: function() {
    // Expand active menu item
    const activeLink = $('.sidebar .nav li a.active');
    if (activeLink.length) {
      activeLink.closest('.collapse').addClass('show');
      activeLink.closest('li').find('a[data-toggle="collapse"]').removeClass('collapsed');
    }

    // Remember sidebar state
    if (localStorage.getItem('sidebar-mini') === 'true') {
      $('body').addClass('sidebar-mini');
    }

    $('#minimizeSidebar').on('click', function() {
      $('body').toggleClass('sidebar-mini');
      localStorage.setItem('sidebar-mini', $('body').hasClass('sidebar-mini'));
    });
  },

  // AJAX helpers
  ajax: {
    // Load content via AJAX
    load: function(url, container, callback) {
      $(container).html('<div class="text-center p-5"><i class="fa fa-spinner fa-spin fa-3x"></i><p class="mt-3">Carregando...</p></div>');
      
      $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
          $(container).html(response);
          if (typeof callback === 'function') {
            callback(response);
          }
        },
        error: function(xhr) {
          $(container).html('<div class="alert alert-danger">Erro ao carregar conteúdo. Por favor, tente novamente.</div>');
          console.error('AJAX Error:', xhr);
        }
      });
    }
  }
};

// Initialize when document is ready
$(document).ready(function() {
  AdminPanel.init();
});
