/*!
 * Modern Sidebar JavaScript - CancroSoft TM
 * Enhanced sidebar functionality with smooth animations
 */

(function() {
  'use strict';

  // DOM Elements
  const sidebar = document.getElementById('sidebar');
  const sidebarOverlay = document.getElementById('sidebarOverlay');
  const navbarToggler = document.querySelector('.navbar-toggler');
  const body = document.body;

  // Sidebar state
  let sidebarOpen = false;

  // Initialize sidebar functionality
  function initSidebar() {
    // Mobile toggle functionality
    if (navbarToggler) {
      navbarToggler.addEventListener('click', toggleSidebar);
    }

    // Overlay click to close sidebar
    if (sidebarOverlay) {
      sidebarOverlay.addEventListener('click', closeSidebar);
    }

    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && sidebarOpen) {
        closeSidebar();
      }
    });

    // Handle window resize
    window.addEventListener('resize', handleResize);

    // Initialize collapse functionality
    initCollapseMenus();

    // Initialize smooth scrolling for sidebar
    initSmoothScrolling();

    // Initialize tooltips for mobile
    initTooltips();

    // Set initial state
    handleResize();
  }

  // Toggle sidebar visibility
  function toggleSidebar() {
    if (sidebarOpen) {
      closeSidebar();
    } else {
      openSidebar();
    }
  }

  // Open sidebar
  function openSidebar() {
    if (window.innerWidth <= 991.98) {
      sidebar.classList.add('show');
      sidebarOverlay.classList.add('show');
      body.classList.add('sidebar-open');
      sidebarOpen = true;

      // Animate navbar toggler
      if (navbarToggler) {
        navbarToggler.classList.add('active');
      }
    }
  }

  // Close sidebar
  function closeSidebar() {
    sidebar.classList.remove('show');
    sidebarOverlay.classList.remove('show');
    body.classList.remove('sidebar-open');
    sidebarOpen = false;

    // Animate navbar toggler
    if (navbarToggler) {
      navbarToggler.classList.remove('active');
    }
  }

  // Handle window resize
  function handleResize() {
    if (window.innerWidth > 991.98) {
      // Desktop view
      closeSidebar();
      sidebar.classList.remove('show');
      sidebarOverlay.classList.remove('show');
      body.classList.remove('sidebar-open');
    }
  }

  // Initialize collapse menus
  function initCollapseMenus() {
    // Wait for jQuery to be available
    if (typeof $ !== 'undefined') {
      // Use jQuery/Bootstrap collapse functionality
      $('[data-toggle="collapse"]').on('click', function(e) {
        e.preventDefault();

        const targetId = $(this).attr('href');
        const $target = $(targetId);
        const isExpanded = $(this).attr('aria-expanded') === 'true';

        if ($target.length) {
          if (isExpanded) {
            // Collapse
            $target.removeClass('show');
            $(this).attr('aria-expanded', 'false');
          } else {
            // Expand
            $target.addClass('show');
            $(this).attr('aria-expanded', 'true');

            // Close other open menus (accordion behavior)
            $('[data-toggle="collapse"]').not(this).each(function() {
              const otherTargetId = $(this).attr('href');
              const $otherTarget = $(otherTargetId);

              if ($otherTarget.hasClass('show')) {
                $otherTarget.removeClass('show');
                $(this).attr('aria-expanded', 'false');
              }
            });
          }

          // Animate caret
          const $caret = $(this).find('.caret');
          if ($caret.length) {
            if (isExpanded) {
              $caret.css('transform', 'rotate(0deg)');
            } else {
              $caret.css('transform', 'rotate(180deg)');
            }
          }
        }
      });
    } else {
      // Fallback to vanilla JavaScript
      const collapseLinks = document.querySelectorAll('[data-toggle="collapse"]');

      collapseLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();

          const targetId = this.getAttribute('href');
          const target = document.querySelector(targetId);
          const isExpanded = this.getAttribute('aria-expanded') === 'true';

          if (target) {
            if (isExpanded) {
              // Collapse
              target.classList.remove('show');
              this.setAttribute('aria-expanded', 'false');
            } else {
              // Expand
              target.classList.add('show');
              this.setAttribute('aria-expanded', 'true');

              // Close other open menus (accordion behavior)
              collapseLinks.forEach(otherLink => {
                if (otherLink !== this) {
                  const otherTargetId = otherLink.getAttribute('href');
                  const otherTarget = document.querySelector(otherTargetId);

                  if (otherTarget && otherTarget.classList.contains('show')) {
                    otherTarget.classList.remove('show');
                    otherLink.setAttribute('aria-expanded', 'false');
                  }
                }
              });
            }

            // Animate caret
            const caret = this.querySelector('.caret');
            if (caret) {
              if (isExpanded) {
                caret.style.transform = 'rotate(0deg)';
              } else {
                caret.style.transform = 'rotate(180deg)';
              }
            }
          }
        });
      });
    }
  }

  // Initialize smooth scrolling
  function initSmoothScrolling() {
    const sidebarWrapper = document.querySelector('.sidebar-wrapper');

    if (sidebarWrapper) {
      // Add smooth scrolling behavior
      sidebarWrapper.style.scrollBehavior = 'smooth';

      // Highlight active menu item on scroll
      const menuItems = sidebarWrapper.querySelectorAll('.nav > li > a');

      menuItems.forEach(item => {
        item.addEventListener('click', function() {
          // Remove active class from all items
          menuItems.forEach(menuItem => {
            menuItem.parentElement.classList.remove('active');
          });

          // Add active class to clicked item
          this.parentElement.classList.add('active');

          // Close sidebar on mobile after navigation
          if (window.innerWidth <= 991.98) {
            setTimeout(closeSidebar, 300);
          }
        });
      });
    }
  }

  // Initialize tooltips for mobile
  function initTooltips() {
    const menuItems = document.querySelectorAll('.sidebar .nav > li > a');

    menuItems.forEach(item => {
      const text = item.querySelector('p');
      if (text) {
        item.setAttribute('title', text.textContent.trim());
      }
    });
  }

  // Add loading animation to menu items
  function addLoadingAnimation() {
    const menuLinks = document.querySelectorAll('.sidebar .nav a[href]:not([data-toggle])');

    menuLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        // Don't add loading for external links or collapse toggles
        if (this.getAttribute('target') === '_blank' || this.getAttribute('data-toggle')) {
          return;
        }

        // Add loading state
        const icon = this.querySelector('i');
        if (icon) {
          const originalClass = icon.className;
          icon.className = 'fas fa-spinner fa-spin';

          // Restore original icon after navigation
          setTimeout(() => {
            icon.className = originalClass;
          }, 1000);
        }
      });
    });
  }

  // Add hover effects
  function addHoverEffects() {
    const menuItems = document.querySelectorAll('.sidebar .nav > li > a');

    menuItems.forEach(item => {
      item.addEventListener('mouseenter', function() {
        this.style.transform = 'translateX(4px)';
      });

      item.addEventListener('mouseleave', function() {
        this.style.transform = 'translateX(0)';
      });
    });
  }

  // Initialize user avatar click functionality
  function initUserAvatar() {
    const userAvatar = document.querySelector('.user-avatar img');

    if (userAvatar) {
      userAvatar.addEventListener('click', function() {
        // Add a subtle animation
        this.style.transform = 'scale(1.1)';
        setTimeout(() => {
          this.style.transform = 'scale(1)';
        }, 200);
      });
    }
  }

  // Add status indicator animation
  function animateStatusIndicator() {
    const statusIndicator = document.querySelector('.status-indicator.online');

    if (statusIndicator) {
      setInterval(() => {
        statusIndicator.style.boxShadow = '0 0 12px rgba(72, 187, 120, 0.8)';
        setTimeout(() => {
          statusIndicator.style.boxShadow = '0 0 8px rgba(72, 187, 120, 0.5)';
        }, 1000);
      }, 3000);
    }
  }

  // Initialize everything when DOM is ready
  function initializeAll() {
    initSidebar();
    addLoadingAnimation();
    addHoverEffects();
    initUserAvatar();
    animateStatusIndicator();

    // Add fade-in animation to sidebar
    if (sidebar) {
      sidebar.style.opacity = '0';
      sidebar.style.transform = 'translateX(-20px)';

      setTimeout(() => {
        sidebar.style.transition = 'all 0.3s ease';
        sidebar.style.opacity = '1';
        sidebar.style.transform = 'translateX(0)';
      }, 100);
    }
  }

  // Wait for both DOM and jQuery to be ready
  document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined') {
      // jQuery is available, initialize immediately
      initializeAll();
    } else {
      // Wait for jQuery to load
      const checkJQuery = setInterval(() => {
        if (typeof $ !== 'undefined') {
          clearInterval(checkJQuery);
          initializeAll();
        }
      }, 50);
    }
  });

  // Export functions for external use
  window.ModernSidebar = {
    toggle: toggleSidebar,
    open: openSidebar,
    close: closeSidebar
  };

})();
