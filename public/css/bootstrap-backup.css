:root {
  --blue: #5e72e4;
  --indigo: #5603ad;
  --purple: #9d4edd;
  --pink: #ff6b9d;
  --red: #f5365c;
  --orange: #fb6340;
  --yellow: #ffd600;
  --green: #2dce89;
  --teal: #11cdef;
  --cyan: #2bffc6;
  --gray: #6c757d;
  --gray-dark: #32325d;
  --light: #ced4da;
  --lighter: #e9ecef;
  --primary: #9d4edd;
  --secondary: #1a1a1a;
  --success: #00f2c3;
  --info: #7209b7;
  --warning: #ff8d72;
  --danger: #ff6b9d;
  --light: #adb5bd;
  --dark: #000000;
  --default: #2d1b69;
  --white: #fff;
  --neutral: #fff;
  --darker: #000;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system,BlinkMacSystemFont,"Segoe UI",<PERSON>o,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
  --font-family-monospace: SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace
}

*,:after,:before {
  box-sizing: border-box
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(34,42,66,0)
}

@-ms-viewport {
  width:device-width
}

article,aside,figcaption,figure,footer,header,hgroup,main,nav,section {
  display: block
}

body {
  margin: 0;
  font-family: Poppins,sans-serif;
  font-size: .875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #525f7f;
  text-align: left;
  background-color: #1e1e2f
}

[tabindex="-1"]:focus {
  outline: 0!important
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible
}

h1,h2,h3,h4,h5,h6 {
  margin-top: 0;
  margin-bottom: .5rem
}

p {
  margin-top: 0;
  margin-bottom: 1rem
}

abbr[data-original-title],abbr[title] {
  text-decoration: underline;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0
}

address {
  font-style: normal;
  line-height: inherit
}

address,dl,ol,ul {
  margin-bottom: 1rem
}

dl,ol,ul {
  margin-top: 0
}

ol ol,ol ul,ul ol,ul ul {
  margin-bottom: 0
}

dt {
  font-weight: 600
}

dd {
  margin-bottom: .5rem;
  margin-left: 0
}

blockquote {
  margin: 0 0 1rem
}

dfn {
  font-style: italic
}

b,strong {
  font-weight: bolder
}

small {
  font-size: 80%
}

sub,sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline
}

sub {
  bottom: -.25em
}

sup {
  top: -.5em
}

a {
  color: #e14eca;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects
}

a:hover {
  color: #c221a9;
  text-decoration: none
}

a:not([href]):not([tabindex]),a:not([href]):not([tabindex]):focus,a:not([href]):not([tabindex]):hover {
  color: inherit;
  text-decoration: none
}

a:not([href]):not([tabindex]):focus {
  outline: 0
}

code,kbd,pre,samp {
  font-family: SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;
  font-size: 1em
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar
}

figure {
  margin: 0 0 1rem
}

img {
  border-style: none
}

img,svg {
  vertical-align: middle
}

svg {
  overflow: hidden
}

table {
  border-collapse: collapse
}

caption {
  padding-top: 1rem;
  padding-bottom: 1rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom
}

th {
  text-align: inherit
}

label {
  display: inline-block;
  margin-bottom: .5rem
}

button {
  border-radius: 0
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color
}

button,input,optgroup,select,textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit
}

button,input {
  overflow: visible
}

button,select {
  text-transform: none
}

[type=reset],[type=submit],button,html [type=button] {
  -webkit-appearance: button
}

[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner {
  padding: 0;
  border-style: none
}

input[type=checkbox],input[type=radio] {
  box-sizing: border-box;
  padding: 0
}

input[type=date],input[type=datetime-local],input[type=month],input[type=time] {
  -webkit-appearance: listbox
}

textarea {
  overflow: auto;
  resize: vertical
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal
}

progress {
  vertical-align: baseline
}

[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button {
  height: auto
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: none
}

[type=search]::-webkit-search-cancel-button,[type=search]::-webkit-search-decoration {
  -webkit-appearance: none
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button
}

output {
  display: inline-block
}

summary {
  display: list-item;
  cursor: pointer
}

template {
  display: none
}

[hidden] {
  display: none!important
}

.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6 {
  margin-bottom: .5rem;
  font-family: inherit;
  font-weight: 400;
  color: #32325d
}

.h1,h1 {
  font-size: 2.0625rem
}

.h2,h2 {
  font-size: 1.6875rem
}

.h3,h3 {
  font-size: 1.4375rem
}

.h4,h4 {
  font-size: 1.0625rem
}

.h5,h5 {
  font-size: .8125rem
}

.h6,h6 {
  font-size: .75rem
}

.lead {
  font-size: .78125rem;
  font-weight: 300
}

.display-1 {
  font-size: 3.3rem
}

.display-1,.display-2 {
  font-weight: 600;
  line-height: 1.2
}

.display-2 {
  font-size: 2.75rem
}

.display-3 {
  font-size: 2.1875rem
}

.display-3,.display-4 {
  font-weight: 600;
  line-height: 1.2
}

.display-4 {
  font-size: 1.6275rem
}

hr {
  margin-top: 2rem;
  margin-bottom: 2rem;
  border: 0;
  border-top: .0625rem solid rgba(34,42,66,.1)
}

.small,small {
  font-size: 80%;
  font-weight: 400
}

.mark,mark {
  padding: .2em;
  background-color: #fcf8e3
}

.list-inline,.list-unstyled {
  padding-left: 0;
  list-style: none
}

.list-inline-item {
  display: inline-block
}

.list-inline-item:not(:last-child) {
  margin-right: .5rem
}

.initialism {
  font-size: 90%;
  text-transform: uppercase
}

.blockquote {
  margin-bottom: 1rem
}

.blockquote-footer {
  display: block;
  font-size: 80%;
  color: #6c757d
}

.blockquote-footer:before {
  content: "\2014 \00A0"
}

.img-fluid,.img-thumbnail {
  max-width: 100%;
  height: auto
}

.img-thumbnail {
  padding: .25rem;
  background-color: #1e1e2f;
  border: .0625rem solid #e3e3e3;
  border-radius: .25rem;
  box-shadow: 0 1px 2px rgba(34,42,66,.075)
}

.figure {
  display: inline-block
}

.figure-img {
  margin-bottom: .5rem;
  line-height: 1
}

.figure-caption {
  font-size: 90%;
  color: #6c757d
}

code {
  font-size: 87.5%;
  word-break: break-word
}

a>code {
  color: inherit
}

kbd {
  padding: .2rem .4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: .2857rem;
  box-shadow: inset 0 -.1rem 0 rgba(34,42,66,.25)
}

kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 600;
  box-shadow: none
}

pre {
  display: block;
  font-size: 87.5%;
  color: #212529
}

pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto
}

@media (min-width:576px) {
  .container {
    max-width: 540px
  }}

@media (min-width:768px) {
  .container {
    max-width: 720px
  }}

@media (min-width:992px) {
  .container {
    max-width: 960px
  }}

@media (min-width:1200px) {
  .container {
    max-width: 1140px
  }}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px
}

.no-gutters {
  margin-right: 0;
  margin-left: 0
}

.no-gutters>.col,.no-gutters>[class*=col-] {
  padding-right: 0;
  padding-left: 0
}

.col,.col-1,.col-2,.col-3,.col-4,.col-5,.col-6,.col-7,.col-8,.col-9,.col-10,.col-11,.col-12,.col-auto,.col-lg,.col-lg-1,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-auto,.col-md,.col-md-1,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-md-10,.col-md-11,.col-md-12,.col-md-auto,.col-sm,.col-sm-1,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-auto,.col-xl,.col-xl-1,.col-xl-2,.col-xl-3,.col-xl-4,.col-xl-5,.col-xl-6,.col-xl-7,.col-xl-8,.col-xl-9,.col-xl-10,.col-xl-11,.col-xl-12,.col-xl-auto {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: none
}

.col-1 {
  flex: 0 0 8.333333%;
  max-width: 8.333333%
}

.col-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%
}

.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%
}

.col-5 {
  flex: 0 0 41.666667%;
  max-width: 41.666667%
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%
}

.col-7 {
  flex: 0 0 58.333333%;
  max-width: 58.333333%
}

.col-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%
}

.col-10 {
  flex: 0 0 83.333333%;
  max-width: 83.333333%
}

.col-11 {
  flex: 0 0 91.666667%;
  max-width: 91.666667%
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%
}

.order-first {
  order: -1
}

.order-last {
  order: 13
}

.order-0 {
  order: 0
}

.order-1 {
  order: 1
}

.order-2 {
  order: 2
}

.order-3 {
  order: 3
}

.order-4 {
  order: 4
}

.order-5 {
  order: 5
}

.order-6 {
  order: 6
}

.order-7 {
  order: 7
}

.order-8 {
  order: 8
}

.order-9 {
  order: 9
}

.order-10 {
  order: 10
}

.order-11 {
  order: 11
}

.order-12 {
  order: 12
}

.offset-1 {
  margin-left: 8.333333%
}

.offset-2 {
  margin-left: 16.666667%
}

.offset-3 {
  margin-left: 25%
}

.offset-4 {
  margin-left: 33.333333%
}

.offset-5 {
  margin-left: 41.666667%
}

.offset-6 {
  margin-left: 50%
}

.offset-7 {
  margin-left: 58.333333%
}

.offset-8 {
  margin-left: 66.666667%
}

.offset-9 {
  margin-left: 75%
}

.offset-10 {
  margin-left: 83.333333%
}

.offset-11 {
  margin-left: 91.666667%
}

@media (min-width:576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%
  }

  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none
  }

  .col-sm-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%
  }

  .col-sm-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%
  }

  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%
  }

  .col-sm-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%
  }

  .col-sm-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%
  }

  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%
  }

  .col-sm-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%
  }

  .col-sm-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%
  }

  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%
  }

  .col-sm-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%
  }

  .col-sm-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%
  }

  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%
  }

  .order-sm-first {
    order: -1
  }

  .order-sm-last {
    order: 13
  }

  .order-sm-0 {
    order: 0
  }

  .order-sm-1 {
    order: 1
  }

  .order-sm-2 {
    order: 2
  }

  .order-sm-3 {
    order: 3
  }

  .order-sm-4 {
    order: 4
  }

  .order-sm-5 {
    order: 5
  }

  .order-sm-6 {
    order: 6
  }

  .order-sm-7 {
    order: 7
  }

  .order-sm-8 {
    order: 8
  }

  .order-sm-9 {
    order: 9
  }

  .order-sm-10 {
    order: 10
  }

  .order-sm-11 {
    order: 11
  }

  .order-sm-12 {
    order: 12
  }

  .offset-sm-0 {
    margin-left: 0
  }

  .offset-sm-1 {
    margin-left: 8.333333%
  }

  .offset-sm-2 {
    margin-left: 16.666667%
  }

  .offset-sm-3 {
    margin-left: 25%
  }

  .offset-sm-4 {
    margin-left: 33.333333%
  }

  .offset-sm-5 {
    margin-left: 41.666667%
  }

  .offset-sm-6 {
    margin-left: 50%
  }

  .offset-sm-7 {
    margin-left: 58.333333%
  }

  .offset-sm-8 {
    margin-left: 66.666667%
  }

  .offset-sm-9 {
    margin-left: 75%
  }

  .offset-sm-10 {
    margin-left: 83.333333%
  }

  .offset-sm-11 {
    margin-left: 91.666667%
  }}

@media (min-width:768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%
  }

  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none
  }

  .col-md-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%
  }

  .col-md-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%
  }

  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%
  }

  .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%
  }

  .col-md-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%
  }

  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%
  }

  .col-md-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%
  }

  .col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%
  }

  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%
  }

  .col-md-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%
  }

  .col-md-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%
  }

  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%
  }

  .order-md-first {
    order: -1
  }

  .order-md-last {
    order: 13
  }

  .order-md-0 {
    order: 0
  }

  .order-md-1 {
    order: 1
  }

  .order-md-2 {
    order: 2
  }

  .order-md-3 {
    order: 3
  }

  .order-md-4 {
    order: 4
  }

  .order-md-5 {
    order: 5
  }

  .order-md-6 {
    order: 6
  }

  .order-md-7 {
    order: 7
  }

  .order-md-8 {
    order: 8
  }

  .order-md-9 {
    order: 9
  }

  .order-md-10 {
    order: 10
  }

  .order-md-11 {
    order: 11
  }

  .order-md-12 {
    order: 12
  }

  .offset-md-0 {
    margin-left: 0
  }

  .offset-md-1 {
    margin-left: 8.333333%
  }

  .offset-md-2 {
    margin-left: 16.666667%
  }

  .offset-md-3 {
    margin-left: 25%
  }

  .offset-md-4 {
    margin-left: 33.333333%
  }

  .offset-md-5 {
    margin-left: 41.666667%
  }

  .offset-md-6 {
    margin-left: 50%
  }

  .offset-md-7 {
    margin-left: 58.333333%
  }

  .offset-md-8 {
    margin-left: 66.666667%
  }

  .offset-md-9 {
    margin-left: 75%
  }

  .offset-md-10 {
    margin-left: 83.333333%
  }

  .offset-md-11 {
    margin-left: 91.666667%
  }}

@media (min-width:992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%
  }

  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none
  }

  .col-lg-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%
  }

  .col-lg-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%
  }

  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%
  }

  .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%
  }

  .col-lg-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%
  }

  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%
  }

  .col-lg-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%
  }

  .col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%
  }

  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%
  }

  .col-lg-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%
  }

  .col-lg-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%
  }

  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%
  }

  .order-lg-first {
    order: -1
  }

  .order-lg-last {
    order: 13
  }

  .order-lg-0 {
    order: 0
  }

  .order-lg-1 {
    order: 1
  }

  .order-lg-2 {
    order: 2
  }

  .order-lg-3 {
    order: 3
  }

  .order-lg-4 {
    order: 4
  }

  .order-lg-5 {
    order: 5
  }

  .order-lg-6 {
    order: 6
  }

  .order-lg-7 {
    order: 7
  }

  .order-lg-8 {
    order: 8
  }

  .order-lg-9 {
    order: 9
  }

  .order-lg-10 {
    order: 10
  }

  .order-lg-11 {
    order: 11
  }

  .order-lg-12 {
    order: 12
  }

  .offset-lg-0 {
    margin-left: 0
  }

  .offset-lg-1 {
    margin-left: 8.333333%
  }

  .offset-lg-2 {
    margin-left: 16.666667%
  }

  .offset-lg-3 {
    margin-left: 25%
  }

  .offset-lg-4 {
    margin-left: 33.333333%
  }

  .offset-lg-5 {
    margin-left: 41.666667%
  }

  .offset-lg-6 {
    margin-left: 50%
  }

  .offset-lg-7 {
    margin-left: 58.333333%
  }

  .offset-lg-8 {
    margin-left: 66.666667%
  }

  .offset-lg-9 {
    margin-left: 75%
  }

  .offset-lg-10 {
    margin-left: 83.333333%
  }

  .offset-lg-11 {
    margin-left: 91.666667%
  }}

@media (min-width:1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%
  }

  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none
  }

  .col-xl-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%
  }

  .col-xl-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%
  }

  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%
  }

  .col-xl-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%
  }

  .col-xl-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%
  }

  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%
  }

  .col-xl-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%
  }

  .col-xl-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%
  }

  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%
  }

  .col-xl-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%
  }

  .col-xl-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%
  }

  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%
  }

  .order-xl-first {
    order: -1
  }

  .order-xl-last {
    order: 13
  }

  .order-xl-0 {
    order: 0
  }

  .order-xl-1 {
    order: 1
  }

  .order-xl-2 {
    order: 2
  }

  .order-xl-3 {
    order: 3
  }

  .order-xl-4 {
    order: 4
  }

  .order-xl-5 {
    order: 5
  }

  .order-xl-6 {
    order: 6
  }

  .order-xl-7 {
    order: 7
  }

  .order-xl-8 {
    order: 8
  }

  .order-xl-9 {
    order: 9
  }

  .order-xl-10 {
    order: 10
  }

  .order-xl-11 {
    order: 11
  }

  .order-xl-12 {
    order: 12
  }

  .offset-xl-0 {
    margin-left: 0
  }

  .offset-xl-1 {
    margin-left: 8.333333%
  }

  .offset-xl-2 {
    margin-left: 16.666667%
  }

  .offset-xl-3 {
    margin-left: 25%
  }

  .offset-xl-4 {
    margin-left: 33.333333%
  }

  .offset-xl-5 {
    margin-left: 41.666667%
  }

  .offset-xl-6 {
    margin-left: 50%
  }

  .offset-xl-7 {
    margin-left: 58.333333%
  }

  .offset-xl-8 {
    margin-left: 66.666667%
  }

  .offset-xl-9 {
    margin-left: 75%
  }

  .offset-xl-10 {
    margin-left: 83.333333%
  }

  .offset-xl-11 {
    margin-left: 91.666667%
  }}

.table {
  width: 100%;
  margin-bottom: 1rem;
  background-color: transparent
}

.table td,.table th {
  padding: 1rem;
  vertical-align: top;
  border-top: .0625rem solid #e3e3e3
}

.table thead th {
  vertical-align: bottom;
  border-bottom: .125rem solid #e3e3e3
}

.table tbody+tbody {
  border-top: .125rem solid #e3e3e3
}

.table .table {
  background-color: #1e1e2f
}

.table-sm td,.table-sm th {
  padding: .3rem
}

.table-bordered,.table-bordered td,.table-bordered th {
  border: .0625rem solid #e3e3e3
}

.table-bordered thead td,.table-bordered thead th {
  border-bottom-width: .125rem
}

.table-borderless tbody+tbody,.table-borderless td,.table-borderless th,.table-borderless thead th {
  border: 0
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(34,42,66,.05)
}

.table-hover tbody tr:hover {
  background-color: rgba(34,42,66,.075)
}

.table-primary,.table-primary>td,.table-primary>th {
  background-color: #f7cdf0
}

.table-hover .table-primary:hover,.table-hover .table-primary:hover>td,.table-hover .table-primary:hover>th {
  background-color: #f3b7e9
}

.table-secondary,.table-secondary>td,.table-secondary>th {
  background-color: #fcfcfd
}

.table-hover .table-secondary:hover,.table-hover .table-secondary:hover>td,.table-hover .table-secondary:hover>th {
  background-color: #ededf3
}

.table-success,.table-success>td,.table-success>th {
  background-color: #b8fbee
}

.table-hover .table-success:hover,.table-hover .table-success:hover>td,.table-hover .table-success:hover>th {
  background-color: #a0fae8
}

.table-info,.table-info>td,.table-info>th {
  background-color: #c0dffd
}

.table-hover .table-info:hover,.table-hover .table-info:hover>td,.table-hover .table-info:hover>th {
  background-color: #a7d2fc
}

.table-warning,.table-warning>td,.table-warning>th {
  background-color: #ffdfd8
}

.table-hover .table-warning:hover,.table-hover .table-warning:hover>td,.table-hover .table-warning:hover>th {
  background-color: #ffcabf
}

.table-danger,.table-danger>td,.table-danger>th {
  background-color: #fed2e1
}

.table-hover .table-danger:hover,.table-hover .table-danger:hover>td,.table-hover .table-danger:hover>th {
  background-color: #fdb9d0
}

.table-light,.table-light>td,.table-light>th {
  background-color: #e8eaed
}

.table-hover .table-light:hover,.table-hover .table-light:hover>td,.table-hover .table-light:hover>th {
  background-color: #dadde2
}

.table-dark,.table-dark>td,.table-dark>th {
  background-color: #c1c2c3
}

.table-hover .table-dark:hover,.table-hover .table-dark:hover>td,.table-hover .table-dark:hover>th {
  background-color: #b4b5b6
}

.table-default,.table-default>td,.table-default>th {
  background-color: #c6cbd8
}

.table-hover .table-default:hover,.table-hover .table-default:hover>td,.table-hover .table-default:hover>th {
  background-color: #b7bdce
}

.table-white,.table-white>td,.table-white>th {
  background-color: #fff
}

.table-hover .table-white:hover,.table-hover .table-white:hover>td,.table-hover .table-white:hover>th {
  background-color: #f2f2f2
}

.table-neutral,.table-neutral>td,.table-neutral>th {
  background-color: #fff
}

.table-hover .table-neutral:hover,.table-hover .table-neutral:hover>td,.table-hover .table-neutral:hover>th {
  background-color: #f2f2f2
}

.table-darker,.table-darker>td,.table-darker>th {
  background-color: #b8b8b8
}

.table-hover .table-darker:hover,.table-hover .table-darker:hover>td,.table-hover .table-darker:hover>th {
  background-color: #ababab
}

.table-active,.table-active>td,.table-active>th {
  background-color: rgba(34,42,66,.075)
}

.table-hover .table-active:hover,.table-hover .table-active:hover>td,.table-hover .table-active:hover>th {
  background-color: rgba(25,31,49,.075)
}

.table .thead-dark th {
  color: #1e1e2f;
  background-color: #212529;
  border-color: #32383e
}

.table .thead-light th {
  color: #525f7f;
  background-color: #e9ecef;
  border-color: #e3e3e3
}

.table-dark {
  color: #1e1e2f;
  background-color: #212529
}

.table-dark td,.table-dark th,.table-dark thead th {
  border-color: #32383e
}

.table-dark.table-bordered {
  border: 0
}

.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: hsla(0,0%,100%,.05)
}

.table-dark.table-hover tbody tr:hover {
  background-color: hsla(0,0%,100%,.075)
}

@media (max-width:575.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
  }

  .table-responsive-sm>.table-bordered {
    border: 0
  }}

@media (max-width:767.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
  }

  .table-responsive-md>.table-bordered {
    border: 0
  }}

@media (max-width:991.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
  }

  .table-responsive-lg>.table-bordered {
    border: 0
  }}

@media (max-width:1199.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
  }

  .table-responsive-xl>.table-bordered {
    border: 0
  }}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar
}

.table-responsive>.table-bordered {
  border: 0
}

.form-control {
  display: block;
  width: 100%;
  height: calc(2.25rem + 2px);
  padding: .5rem .7rem;
  font-size: .875rem;
  line-height: 1.428571;
  color: hsla(0,0%,100%,.8);
  background-color: transparent;
  background-clip: padding-box;
  border: 1px solid #cad1d7;
  border-radius: .25rem;
  box-shadow: none;
  transition: all .2s cubic-bezier(.68,-.55,.265,1.55)
}

@media screen and (prefers-reduced-motion:reduce) {
  .form-control {
    transition: none
  }}

.form-control::-ms-expand {
  background-color: transparent;
  border: 0
}

.form-control:focus {
  color: hsla(0,0%,100%,.8);
  background-color: #fff;
  border-color: rgba(50,151,211,.25);
  outline: 0;
  box-shadow: none,none
}

.form-control::placeholder {
  color: #adb5bd;
  opacity: 1
}

.form-control:disabled,.form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1
}

select.form-control:focus::-ms-value {
  color: hsla(0,0%,100%,.8);
  background-color: transparent
}

.form-control-file,.form-control-range {
  display: block;
  width: 100%
}

.col-form-label {
  padding-top: calc(.5rem + 1px);
  padding-bottom: calc(.5rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5
}

.col-form-label-lg {
  padding-top: calc(.875rem + 1px);
  padding-bottom: calc(.875rem + 1px);
  font-size: .99925rem;
  line-height: 1.35
}

.col-form-label-sm {
  padding-top: calc(.25rem + 1px);
  padding-bottom: calc(.25rem + 1px);
  font-size: .75rem;
  line-height: 1.35
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding-top: .5rem;
  padding-bottom: .5rem;
  margin-bottom: 0;
  line-height: 1.428571;
  color: #525f7f;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0
}

.form-control-plaintext.form-control-lg,.form-control-plaintext.form-control-sm {
  padding-right: 0;
  padding-left: 0
}

.form-control-sm {
  height: calc(1.5125rem + 2px);
  padding: .25rem .5rem;
  font-size: .75rem;
  line-height: 1.35;
  border-radius: .2857rem
}

.form-control-lg {
  height: calc(3.098987rem + 2px);
  padding: .875rem 1rem;
  font-size: .99925rem;
  line-height: 1.35;
  border-radius: .4285rem
}

select.form-control[multiple],select.form-control[size],textarea.form-control {
  height: auto
}

.form-group {
  margin-bottom: 1rem
}

.form-text {
  display: block;
  margin-top: .25rem
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px
}

.form-row>.col,.form-row>[class*=col-] {
  padding-right: 5px;
  padding-left: 5px
}

.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem
}

.form-check-input {
  position: absolute;
  margin-top: .3rem;
  margin-left: -1.25rem
}

.form-check-input:disabled~.form-check-label {
  color: #6c757d
}

.form-check-label {
  margin-bottom: 0
}

.form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: .75rem
}

.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: .3125rem;
  margin-left: 0
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #00f2c3
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: .25rem .5rem;
  margin-top: .1rem;
  font-size: .75rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(0,242,195,.9);
  border-radius: .25rem
}

.custom-select.is-valid,.form-control.is-valid,.was-validated .custom-select:valid,.was-validated .form-control:valid {
  border-color: #00f2c3
}

.custom-select.is-valid:focus,.form-control.is-valid:focus,.was-validated .custom-select:valid:focus,.was-validated .form-control:valid:focus {
  border-color: #00f2c3;
  box-shadow: 0 0 0 0 rgba(0,242,195,.25)
}

.custom-select.is-valid~.valid-feedback,.custom-select.is-valid~.valid-tooltip,.form-control-file.is-valid~.valid-feedback,.form-control-file.is-valid~.valid-tooltip,.form-control.is-valid~.valid-feedback,.form-control.is-valid~.valid-tooltip,.was-validated .custom-select:valid~.valid-feedback,.was-validated .custom-select:valid~.valid-tooltip,.was-validated .form-control-file:valid~.valid-feedback,.was-validated .form-control-file:valid~.valid-tooltip,.was-validated .form-control:valid~.valid-feedback,.was-validated .form-control:valid~.valid-tooltip {
  display: block
}

.form-check-input.is-valid~.form-check-label,.was-validated .form-check-input:valid~.form-check-label {
  color: #00f2c3
}

.form-check-input.is-valid~.valid-feedback,.form-check-input.is-valid~.valid-tooltip,.was-validated .form-check-input:valid~.valid-feedback,.was-validated .form-check-input:valid~.valid-tooltip {
  display: block
}

.custom-control-input.is-valid~.custom-control-label,.was-validated .custom-control-input:valid~.custom-control-label {
  color: #00f2c3
}

.custom-control-input.is-valid~.custom-control-label:before,.was-validated .custom-control-input:valid~.custom-control-label:before {
  background-color: #73ffe4
}

.custom-control-input.is-valid~.valid-feedback,.custom-control-input.is-valid~.valid-tooltip,.was-validated .custom-control-input:valid~.valid-feedback,.was-validated .custom-control-input:valid~.valid-tooltip {
  display: block
}

.custom-control-input.is-valid:checked~.custom-control-label:before,.was-validated .custom-control-input:valid:checked~.custom-control-label:before {
  background-color: #26ffd5
}

.custom-control-input.is-valid:focus~.custom-control-label:before,.was-validated .custom-control-input:valid:focus~.custom-control-label:before {
  box-shadow: 0 0 0 1px #1e1e2f,0 0 0 0 rgba(0,242,195,.25)
}

.custom-file-input.is-valid~.custom-file-label,.was-validated .custom-file-input:valid~.custom-file-label {
  border-color: #00f2c3
}

.custom-file-input.is-valid~.custom-file-label:after,.was-validated .custom-file-input:valid~.custom-file-label:after {
  border-color: inherit
}

.custom-file-input.is-valid~.valid-feedback,.custom-file-input.is-valid~.valid-tooltip,.was-validated .custom-file-input:valid~.valid-feedback,.was-validated .custom-file-input:valid~.valid-tooltip {
  display: block
}

.custom-file-input.is-valid:focus~.custom-file-label,.was-validated .custom-file-input:valid:focus~.custom-file-label {
  box-shadow: 0 0 0 0 rgba(0,242,195,.25)
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #ff8d72
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: .25rem .5rem;
  margin-top: .1rem;
  font-size: .75rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(255,141,114,.9);
  border-radius: .25rem
}

.custom-select.is-invalid,.form-control.is-invalid,.was-validated .custom-select:invalid,.was-validated .form-control:invalid {
  border-color: #ff8d72
}

.custom-select.is-invalid:focus,.form-control.is-invalid:focus,.was-validated .custom-select:invalid:focus,.was-validated .form-control:invalid:focus {
  border-color: #ff8d72;
  box-shadow: 0 0 0 0 rgba(255,141,114,.25)
}

.custom-select.is-invalid~.invalid-feedback,.custom-select.is-invalid~.invalid-tooltip,.form-control-file.is-invalid~.invalid-feedback,.form-control-file.is-invalid~.invalid-tooltip,.form-control.is-invalid~.invalid-feedback,.form-control.is-invalid~.invalid-tooltip,.was-validated .custom-select:invalid~.invalid-feedback,.was-validated .custom-select:invalid~.invalid-tooltip,.was-validated .form-control-file:invalid~.invalid-feedback,.was-validated .form-control-file:invalid~.invalid-tooltip,.was-validated .form-control:invalid~.invalid-feedback,.was-validated .form-control:invalid~.invalid-tooltip {
  display: block
}

.form-check-input.is-invalid~.form-check-label,.was-validated .form-check-input:invalid~.form-check-label {
  color: #ff8d72
}

.form-check-input.is-invalid~.invalid-feedback,.form-check-input.is-invalid~.invalid-tooltip,.was-validated .form-check-input:invalid~.invalid-feedback,.was-validated .form-check-input:invalid~.invalid-tooltip {
  display: block
}

.custom-control-input.is-invalid~.custom-control-label,.was-validated .custom-control-input:invalid~.custom-control-label {
  color: #ff8d72
}

.custom-control-input.is-invalid~.custom-control-label:before,.was-validated .custom-control-input:invalid~.custom-control-label:before {
  background-color: #fff4f2
}

.custom-control-input.is-invalid~.invalid-feedback,.custom-control-input.is-invalid~.invalid-tooltip,.was-validated .custom-control-input:invalid~.invalid-feedback,.was-validated .custom-control-input:invalid~.invalid-tooltip {
  display: block
}

.custom-control-input.is-invalid:checked~.custom-control-label:before,.was-validated .custom-control-input:invalid:checked~.custom-control-label:before {
  background-color: #ffb6a5
}

.custom-control-input.is-invalid:focus~.custom-control-label:before,.was-validated .custom-control-input:invalid:focus~.custom-control-label:before {
  box-shadow: 0 0 0 1px #1e1e2f,0 0 0 0 rgba(255,141,114,.25)
}

.custom-file-input.is-invalid~.custom-file-label,.was-validated .custom-file-input:invalid~.custom-file-label {
  border-color: #ff8d72
}

.custom-file-input.is-invalid~.custom-file-label:after,.was-validated .custom-file-input:invalid~.custom-file-label:after {
  border-color: inherit
}

.custom-file-input.is-invalid~.invalid-feedback,.custom-file-input.is-invalid~.invalid-tooltip,.was-validated .custom-file-input:invalid~.invalid-feedback,.was-validated .custom-file-input:invalid~.invalid-tooltip {
  display: block
}

.custom-file-input.is-invalid:focus~.custom-file-label,.was-validated .custom-file-input:invalid:focus~.custom-file-label {
  box-shadow: 0 0 0 0 rgba(255,141,114,.25)
}

.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center
}

.form-inline .form-check {
  width: 100%
}

@media (min-width:576px) {
  .form-inline label {
    justify-content: center
  }

  .form-inline .form-group,.form-inline label {
    display: flex;
    align-items: center;
    margin-bottom: 0
  }

  .form-inline .form-group {
    flex: 0 0 auto;
    flex-flow: row wrap
  }

  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle
  }

  .form-inline .form-control-plaintext {
    display: inline-block
  }

  .form-inline .custom-select,.form-inline .input-group {
    width: auto
  }

  .form-inline .form-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding-left: 0
  }

  .form-inline .form-check-input {
    position: relative;
    margin-top: 0;
    margin-right: .25rem;
    margin-left: 0
  }

  .form-inline .custom-control {
    align-items: center;
    justify-content: center
  }

  .form-inline .custom-control-label {
    margin-bottom: 0
  }}

.btn {
  display: inline-block;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 11px 40px;
  font-size: .875rem;
  line-height: 1.35em;
  border-radius: .25rem;
  transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
}

@media screen and (prefers-reduced-motion:reduce) {
  .btn {
    transition: none
  }}

.btn:focus,.btn:hover {
  text-decoration: none
}

.btn.focus,.btn:focus {
  outline: 0;
  box-shadow: 0 7px 14px rgba(50,50,93,.1),0 3px 6px rgba(0,0,0,.08)
}

.btn.disabled,.btn:disabled {
  opacity: .65;
  box-shadow: none
}

.btn:not(:disabled):not(.disabled) {
  cursor: pointer
}

.btn:not(:disabled):not(.disabled).active,.btn:not(:disabled):not(.disabled):active {
  box-shadow: none
}

.btn:not(:disabled):not(.disabled).active:focus,.btn:not(:disabled):not(.disabled):active:focus {
  box-shadow: 0 7px 14px rgba(50,50,93,.1),0 3px 6px rgba(0,0,0,.08),none
}

a.btn.disabled,fieldset:disabled a.btn {
  pointer-events: none
}

.btn-primary {
  border-color: #e14eca;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-primary:hover {
  color: #fff;
  background-color: #db2dc0;
  border-color: #d725bb
}

.btn-primary.focus,.btn-primary:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(225,78,202,.5)
}

.btn-primary.disabled,.btn-primary:disabled {
  color: #fff;
  background-color: #e14eca;
  border-color: #e14eca
}

.btn-primary:not(:disabled):not(.disabled).active,.btn-primary:not(:disabled):not(.disabled):active,.show>.btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #d725bb;
  border-color: #cd23b2
}

.btn-primary:not(:disabled):not(.disabled).active:focus,.btn-primary:not(:disabled):not(.disabled):active:focus,.show>.btn-primary.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(225,78,202,.5)
}

.btn-secondary {
  color: #212529;
  background-color: #f4f5f7;
  border-color: #f4f5f7;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-secondary:hover {
  color: #212529;
  background-color: #dee1e7;
  border-color: #d6dae2
}

.btn-secondary.focus,.btn-secondary:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(244,245,247,.5)
}

.btn-secondary.disabled,.btn-secondary:disabled {
  color: #212529;
  background-color: #f4f5f7;
  border-color: #f4f5f7
}

.btn-secondary:not(:disabled):not(.disabled).active,.btn-secondary:not(:disabled):not(.disabled):active,.show>.btn-secondary.dropdown-toggle {
  color: #212529;
  background-color: #d6dae2;
  border-color: #cfd3dc
}

.btn-secondary:not(:disabled):not(.disabled).active:focus,.btn-secondary:not(:disabled):not(.disabled):active:focus,.show>.btn-secondary.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(244,245,247,.5)
}

.btn-success {
  border-color: #00f2c3;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-success:hover {
  color: #fff;
  background-color: #00cca4;
  border-color: #00bf9a
}

.btn-success.focus,.btn-success:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(0,242,195,.5)
}

.btn-success.disabled,.btn-success:disabled {
  color: #fff;
  background-color: #00f2c3;
  border-color: #00f2c3
}

.btn-success:not(:disabled):not(.disabled).active,.btn-success:not(:disabled):not(.disabled):active,.show>.btn-success.dropdown-toggle {
  color: #fff;
  background-color: #00bf9a;
  border-color: #00b290
}

.btn-success:not(:disabled):not(.disabled).active:focus,.btn-success:not(:disabled):not(.disabled):active:focus,.show>.btn-success.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(0,242,195,.5)
}

.btn-info {
  border-color: #1d8cf8;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-info:hover {
  color: #fff;
  background-color: #0779e8;
  border-color: #0772db
}

.btn-info.focus,.btn-info:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(29,140,248,.5)
}

.btn-info.disabled,.btn-info:disabled {
  color: #fff;
  background-color: #1d8cf8;
  border-color: #1d8cf8
}

.btn-info:not(:disabled):not(.disabled).active,.btn-info:not(:disabled):not(.disabled):active,.show>.btn-info.dropdown-toggle {
  color: #fff;
  background-color: #0772db;
  border-color: #066ccf
}

.btn-info:not(:disabled):not(.disabled).active:focus,.btn-info:not(:disabled):not(.disabled):active:focus,.show>.btn-info.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(29,140,248,.5)
}

.btn-warning {
  border-color: #ff8d72;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-warning:hover {
  color: #fff;
  background-color: #ff6e4c;
  border-color: #ff643f
}

.btn-warning.focus,.btn-warning:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(255,141,114,.5)
}

.btn-warning.disabled,.btn-warning:disabled {
  color: #fff;
  background-color: #ff8d72;
  border-color: #ff8d72
}

.btn-warning:not(:disabled):not(.disabled).active,.btn-warning:not(:disabled):not(.disabled):active,.show>.btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #ff643f;
  border-color: #ff5932
}

.btn-warning:not(:disabled):not(.disabled).active:focus,.btn-warning:not(:disabled):not(.disabled):active:focus,.show>.btn-warning.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(255,141,114,.5)
}

.btn-danger {
  border-color: #fd5d93;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-danger:hover {
  color: #fff;
  background-color: #fd377a;
  border-color: #fc2b71
}

.btn-danger.focus,.btn-danger:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(253,93,147,.5)
}

.btn-danger.disabled,.btn-danger:disabled {
  color: #fff;
  background-color: #fd5d93;
  border-color: #fd5d93
}

.btn-danger:not(:disabled):not(.disabled).active,.btn-danger:not(:disabled):not(.disabled):active,.show>.btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #fc2b71;
  border-color: #fc1e69
}

.btn-danger:not(:disabled):not(.disabled).active:focus,.btn-danger:not(:disabled):not(.disabled):active:focus,.show>.btn-danger.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(253,93,147,.5)
}

.btn-light {
  color: #fff;
  background-color: #adb5bd;
  border-color: #adb5bd;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-light:hover {
  color: #fff;
  background-color: #98a2ac;
  border-color: #919ca6
}

.btn-light.focus,.btn-light:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(173,181,189,.5)
}

.btn-light.disabled,.btn-light:disabled {
  color: #fff;
  background-color: #adb5bd;
  border-color: #adb5bd
}

.btn-light:not(:disabled):not(.disabled).active,.btn-light:not(:disabled):not(.disabled):active,.show>.btn-light.dropdown-toggle {
  color: #fff;
  background-color: #919ca6;
  border-color: #8a95a1
}

.btn-light:not(:disabled):not(.disabled).active:focus,.btn-light:not(:disabled):not(.disabled):active:focus,.show>.btn-light.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(173,181,189,.5)
}

.btn-dark {
  color: #fff;
  background-color: #212529;
  border-color: #212529;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-dark:hover {
  color: #fff;
  background-color: #101214;
  border-color: #0a0c0d
}

.btn-dark.focus,.btn-dark:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(33,37,41,.5)
}

.btn-dark.disabled,.btn-dark:disabled {
  color: #fff;
  background-color: #212529;
  border-color: #212529
}

.btn-dark:not(:disabled):not(.disabled).active,.btn-dark:not(:disabled):not(.disabled):active,.show>.btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #0a0c0d;
  border-color: #050506
}

.btn-dark:not(:disabled):not(.disabled).active:focus,.btn-dark:not(:disabled):not(.disabled):active:focus,.show>.btn-dark.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(33,37,41,.5)
}

.btn-default {
  color: #fff;
  background-color: #344675;
  border-color: #344675;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-default:hover {
  color: #fff;
  background-color: #28365b;
  border-color: #243152
}

.btn-default.focus,.btn-default:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(52,70,117,.5)
}

.btn-default.disabled,.btn-default:disabled {
  color: #fff;
  background-color: #344675;
  border-color: #344675
}

.btn-default:not(:disabled):not(.disabled).active,.btn-default:not(:disabled):not(.disabled):active,.show>.btn-default.dropdown-toggle {
  color: #fff;
  background-color: #243152;
  border-color: #202c49
}

.btn-default:not(:disabled):not(.disabled).active:focus,.btn-default:not(:disabled):not(.disabled):active:focus,.show>.btn-default.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(52,70,117,.5)
}

.btn-white {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-white:hover {
  color: #212529;
  background-color: #ececec;
  border-color: #e6e6e6
}

.btn-white.focus,.btn-white:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 hsla(0,0%,100%,.5)
}

.btn-white.disabled,.btn-white:disabled {
  color: #212529;
  background-color: #fff;
  border-color: #fff
}

.btn-white:not(:disabled):not(.disabled).active,.btn-white:not(:disabled):not(.disabled):active,.show>.btn-white.dropdown-toggle {
  color: #212529;
  background-color: #e6e6e6;
  border-color: #dfdfdf
}

.btn-white:not(:disabled):not(.disabled).active:focus,.btn-white:not(:disabled):not(.disabled):active:focus,.show>.btn-white.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 hsla(0,0%,100%,.5)
}

.btn-neutral {
  color: #212529;
  border-color: #fff;
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-neutral:hover {
  color: #212529;
  background-color: #ececec;
  border-color: #e6e6e6
}

.btn-neutral.focus,.btn-neutral:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 hsla(0,0%,100%,.5)
}

.btn-neutral.disabled,.btn-neutral:disabled {
  color: #212529;
  background-color: #fff;
  border-color: #fff
}

.btn-neutral:not(:disabled):not(.disabled).active,.btn-neutral:not(:disabled):not(.disabled):active,.show>.btn-neutral.dropdown-toggle {
  color: #212529;
  background-color: #e6e6e6;
  border-color: #dfdfdf
}

.btn-neutral:not(:disabled):not(.disabled).active:focus,.btn-neutral:not(:disabled):not(.disabled):active:focus,.show>.btn-neutral.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 hsla(0,0%,100%,.5)
}

.btn-darker {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.btn-darker,.btn-darker:hover {
  color: #fff;
  background-color: #000;
  border-color: #000
}

.btn-darker.focus,.btn-darker:focus {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08),0 0 0 0 rgba(0,0,0,.5)
}

.btn-darker.disabled,.btn-darker:disabled,.btn-darker:not(:disabled):not(.disabled).active,.btn-darker:not(:disabled):not(.disabled):active,.show>.btn-darker.dropdown-toggle {
  color: #fff;
  background-color: #000;
  border-color: #000
}

.btn-darker:not(:disabled):not(.disabled).active:focus,.btn-darker:not(:disabled):not(.disabled):active:focus,.show>.btn-darker.dropdown-toggle:focus {
  box-shadow: none,0 0 0 0 rgba(0,0,0,.5)
}

.btn-outline-primary {
  color: #e14eca;
  background-color: transparent;
  background-image: none;
  border-color: #e14eca
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #e14eca;
  border-color: #e14eca
}

.btn-outline-primary.focus,.btn-outline-primary:focus {
  box-shadow: 0 0 0 0 rgba(225,78,202,.5)
}

.btn-outline-primary.disabled,.btn-outline-primary:disabled {
  color: #e14eca;
  background-color: transparent
}

.btn-outline-primary:not(:disabled):not(.disabled).active,.btn-outline-primary:not(:disabled):not(.disabled):active,.show>.btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #e14eca;
  border-color: #e14eca
}

.btn-outline-primary:not(:disabled):not(.disabled).active:focus,.btn-outline-primary:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(225,78,202,.5)
}

.btn-outline-secondary {
  color: #f4f5f7;
  background-color: transparent;
  background-image: none;
  border-color: #f4f5f7
}

.btn-outline-secondary:hover {
  color: #212529;
  background-color: #f4f5f7;
  border-color: #f4f5f7
}

.btn-outline-secondary.focus,.btn-outline-secondary:focus {
  box-shadow: 0 0 0 0 rgba(244,245,247,.5)
}

.btn-outline-secondary.disabled,.btn-outline-secondary:disabled {
  color: #f4f5f7;
  background-color: transparent
}

.btn-outline-secondary:not(:disabled):not(.disabled).active,.btn-outline-secondary:not(:disabled):not(.disabled):active,.show>.btn-outline-secondary.dropdown-toggle {
  color: #212529;
  background-color: #f4f5f7;
  border-color: #f4f5f7
}

.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(244,245,247,.5)
}

.btn-outline-success {
  color: #00f2c3;
  background-color: transparent;
  background-image: none;
  border-color: #00f2c3
}

.btn-outline-success:hover {
  color: #fff;
  background-color: #00f2c3;
  border-color: #00f2c3
}

.btn-outline-success.focus,.btn-outline-success:focus {
  box-shadow: 0 0 0 0 rgba(0,242,195,.5)
}

.btn-outline-success.disabled,.btn-outline-success:disabled {
  color: #00f2c3;
  background-color: transparent
}

.btn-outline-success:not(:disabled):not(.disabled).active,.btn-outline-success:not(:disabled):not(.disabled):active,.show>.btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #00f2c3;
  border-color: #00f2c3
}

.btn-outline-success:not(:disabled):not(.disabled).active:focus,.btn-outline-success:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(0,242,195,.5)
}

.btn-outline-info {
  color: #1d8cf8;
  background-color: transparent;
  background-image: none;
  border-color: #1d8cf8
}

.btn-outline-info:hover {
  color: #fff;
  background-color: #1d8cf8;
  border-color: #1d8cf8
}

.btn-outline-info.focus,.btn-outline-info:focus {
  box-shadow: 0 0 0 0 rgba(29,140,248,.5)
}

.btn-outline-info.disabled,.btn-outline-info:disabled {
  color: #1d8cf8;
  background-color: transparent
}

.btn-outline-info:not(:disabled):not(.disabled).active,.btn-outline-info:not(:disabled):not(.disabled):active,.show>.btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #1d8cf8;
  border-color: #1d8cf8
}

.btn-outline-info:not(:disabled):not(.disabled).active:focus,.btn-outline-info:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(29,140,248,.5)
}

.btn-outline-warning {
  color: #ff8d72;
  background-color: transparent;
  background-image: none;
  border-color: #ff8d72
}

.btn-outline-warning:hover {
  color: #fff;
  background-color: #ff8d72;
  border-color: #ff8d72
}

.btn-outline-warning.focus,.btn-outline-warning:focus {
  box-shadow: 0 0 0 0 rgba(255,141,114,.5)
}

.btn-outline-warning.disabled,.btn-outline-warning:disabled {
  color: #ff8d72;
  background-color: transparent
}

.btn-outline-warning:not(:disabled):not(.disabled).active,.btn-outline-warning:not(:disabled):not(.disabled):active,.show>.btn-outline-warning.dropdown-toggle {
  color: #fff;
  background-color: #ff8d72;
  border-color: #ff8d72
}

.btn-outline-warning:not(:disabled):not(.disabled).active:focus,.btn-outline-warning:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(255,141,114,.5)
}

.btn-outline-danger {
  color: #fd5d93;
  background-color: transparent;
  background-image: none;
  border-color: #fd5d93
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #fd5d93;
  border-color: #fd5d93
}

.btn-outline-danger.focus,.btn-outline-danger:focus {
  box-shadow: 0 0 0 0 rgba(253,93,147,.5)
}

.btn-outline-danger.disabled,.btn-outline-danger:disabled {
  color: #fd5d93;
  background-color: transparent
}

.btn-outline-danger:not(:disabled):not(.disabled).active,.btn-outline-danger:not(:disabled):not(.disabled):active,.show>.btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #fd5d93;
  border-color: #fd5d93
}

.btn-outline-danger:not(:disabled):not(.disabled).active:focus,.btn-outline-danger:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(253,93,147,.5)
}

.btn-outline-light {
  color: #adb5bd;
  background-color: transparent;
  background-image: none;
  border-color: #adb5bd
}

.btn-outline-light:hover {
  color: #fff;
  background-color: #adb5bd;
  border-color: #adb5bd
}

.btn-outline-light.focus,.btn-outline-light:focus {
  box-shadow: 0 0 0 0 rgba(173,181,189,.5)
}

.btn-outline-light.disabled,.btn-outline-light:disabled {
  color: #adb5bd;
  background-color: transparent
}

.btn-outline-light:not(:disabled):not(.disabled).active,.btn-outline-light:not(:disabled):not(.disabled):active,.show>.btn-outline-light.dropdown-toggle {
  color: #fff;
  background-color: #adb5bd;
  border-color: #adb5bd
}

.btn-outline-light:not(:disabled):not(.disabled).active:focus,.btn-outline-light:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(173,181,189,.5)
}

.btn-outline-dark {
  color: #212529;
  background-color: transparent;
  background-image: none;
  border-color: #212529
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #212529;
  border-color: #212529
}

.btn-outline-dark.focus,.btn-outline-dark:focus {
  box-shadow: 0 0 0 0 rgba(33,37,41,.5)
}

.btn-outline-dark.disabled,.btn-outline-dark:disabled {
  color: #212529;
  background-color: transparent
}

.btn-outline-dark:not(:disabled):not(.disabled).active,.btn-outline-dark:not(:disabled):not(.disabled):active,.show>.btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #212529;
  border-color: #212529
}

.btn-outline-dark:not(:disabled):not(.disabled).active:focus,.btn-outline-dark:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(33,37,41,.5)
}

.btn-outline-default {
  color: #344675;
  background-color: transparent;
  background-image: none;
  border-color: #344675
}

.btn-outline-default:hover {
  color: #fff;
  background-color: #344675;
  border-color: #344675
}

.btn-outline-default.focus,.btn-outline-default:focus {
  box-shadow: 0 0 0 0 rgba(52,70,117,.5)
}

.btn-outline-default.disabled,.btn-outline-default:disabled {
  color: #344675;
  background-color: transparent
}

.btn-outline-default:not(:disabled):not(.disabled).active,.btn-outline-default:not(:disabled):not(.disabled):active,.show>.btn-outline-default.dropdown-toggle {
  color: #fff;
  background-color: #344675;
  border-color: #344675
}

.btn-outline-default:not(:disabled):not(.disabled).active:focus,.btn-outline-default:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-default.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(52,70,117,.5)
}

.btn-outline-white {
  color: #fff;
  background-color: transparent;
  background-image: none;
  border-color: #fff
}

.btn-outline-white:hover {
  color: #212529;
  background-color: #fff;
  border-color: #fff
}

.btn-outline-white.focus,.btn-outline-white:focus {
  box-shadow: 0 0 0 0 hsla(0,0%,100%,.5)
}

.btn-outline-white.disabled,.btn-outline-white:disabled {
  color: #fff;
  background-color: transparent
}

.btn-outline-white:not(:disabled):not(.disabled).active,.btn-outline-white:not(:disabled):not(.disabled):active,.show>.btn-outline-white.dropdown-toggle {
  color: #212529;
  background-color: #fff;
  border-color: #fff
}

.btn-outline-white:not(:disabled):not(.disabled).active:focus,.btn-outline-white:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-white.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 hsla(0,0%,100%,.5)
}

.btn-outline-neutral {
  color: #fff;
  background-color: transparent;
  background-image: none;
  border-color: #fff
}

.btn-outline-neutral:hover {
  color: #212529;
  background-color: #fff;
  border-color: #fff
}

.btn-outline-neutral.focus,.btn-outline-neutral:focus {
  box-shadow: 0 0 0 0 hsla(0,0%,100%,.5)
}

.btn-outline-neutral.disabled,.btn-outline-neutral:disabled {
  color: #fff;
  background-color: transparent
}

.btn-outline-neutral:not(:disabled):not(.disabled).active,.btn-outline-neutral:not(:disabled):not(.disabled):active,.show>.btn-outline-neutral.dropdown-toggle {
  color: #212529;
  background-color: #fff;
  border-color: #fff
}

.btn-outline-neutral:not(:disabled):not(.disabled).active:focus,.btn-outline-neutral:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-neutral.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 hsla(0,0%,100%,.5)
}

.btn-outline-darker {
  color: #000;
  background-color: transparent;
  background-image: none;
  border-color: #000
}

.btn-outline-darker:hover {
  color: #fff;
  background-color: #000;
  border-color: #000
}

.btn-outline-darker.focus,.btn-outline-darker:focus {
  box-shadow: 0 0 0 0 rgba(0,0,0,.5)
}

.btn-outline-darker.disabled,.btn-outline-darker:disabled {
  color: #000;
  background-color: transparent
}

.btn-outline-darker:not(:disabled):not(.disabled).active,.btn-outline-darker:not(:disabled):not(.disabled):active,.show>.btn-outline-darker.dropdown-toggle {
  color: #fff;
  background-color: #000;
  border-color: #000
}

.btn-outline-darker:not(:disabled):not(.disabled).active:focus,.btn-outline-darker:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-darker.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 rgba(0,0,0,.5)
}

.btn-link {
  font-weight: 400;
  color: #e14eca;
  background-color: transparent
}

.btn-link:hover {
  color: #c221a9;
  text-decoration: none;
  background-color: transparent;
  border-color: transparent
}

.btn-link.focus,.btn-link:focus {
  text-decoration: none;
  border-color: transparent;
  box-shadow: none
}

.btn-link.disabled,.btn-link:disabled {
  color: #6c757d;
  pointer-events: none
}

.btn-group-lg>.btn,.btn-lg {
  font-size: .99925rem;
  line-height: 1.35
}

.btn-group-sm>.btn,.btn-sm {
  font-size: .75rem;
  line-height: 1.35;
  border-radius: .25rem
}

.btn-block {
  display: block;
  width: 100%
}

.btn-block+.btn-block {
  margin-top: .5rem
}

input[type=button].btn-block,input[type=reset].btn-block,input[type=submit].btn-block {
  width: 100%
}

.fade {
  transition: opacity .15s linear
}

@media screen and (prefers-reduced-motion:reduce) {
  .fade {
    transition: none
  }}

.fade:not(.show) {
  opacity: 0
}

.collapse:not(.show) {
  display: none
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height .35s ease
}

@media screen and (prefers-reduced-motion:reduce) {
  .collapsing {
    transition: none
  }}

.dropdown,.dropleft,.dropright,.dropup {
  position: relative
}

.dropdown-toggle:after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border-top: .3em solid;
  border-right: .3em solid transparent;
  border-bottom: 0;
  border-left: .3em solid transparent
}

.dropdown-toggle:empty:after {
  margin-left: 0
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: .5rem 0;
  margin: .125rem 0 0;
  font-size: .875rem;
  color: #525f7f;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 0 solid rgba(34,42,66,.15);
  box-shadow: 0 50px 100px rgba(50,50,93,.1),0 15px 35px rgba(50,50,93,.15),0 5px 15px rgba(0,0,0,.1)
}

.dropdown-menu-right {
  right: 0;
  left: auto
}

.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: .125rem
}

.dropup .dropdown-toggle:after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border-top: 0;
  border-right: .3em solid transparent;
  border-bottom: .3em solid;
  border-left: .3em solid transparent
}

.dropup .dropdown-toggle:empty:after {
  margin-left: 0
}

.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: .125rem
}

.dropright .dropdown-toggle:after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border-top: .3em solid transparent;
  border-right: 0;
  border-bottom: .3em solid transparent;
  border-left: .3em solid
}

.dropright .dropdown-toggle:empty:after {
  margin-left: 0
}

.dropright .dropdown-toggle:after {
  vertical-align: 0
}

.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: .125rem
}

.dropleft .dropdown-toggle:after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  display: none
}

.dropleft .dropdown-toggle:before {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: .255em;
  vertical-align: .255em;
  content: "";
  border-top: .3em solid transparent;
  border-right: .3em solid;
  border-bottom: .3em solid transparent
}

.dropleft .dropdown-toggle:empty:after {
  margin-left: 0
}

.dropleft .dropdown-toggle:before {
  vertical-align: 0
}

.dropdown-menu[x-placement^=bottom],.dropdown-menu[x-placement^=left],.dropdown-menu[x-placement^=right],.dropdown-menu[x-placement^=top] {
  right: auto;
  bottom: auto
}

.dropdown-divider {
  height: 0;
  margin: .5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: .25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0
}

.dropdown-item:focus,.dropdown-item:hover {
  color: #16181b;
  text-decoration: none;
  background-color: #f6f9fc
}

.dropdown-item.active,.dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #e14eca
}

.dropdown-item.disabled,.dropdown-item:disabled {
  color: #6c757d;
  background-color: transparent
}

.dropdown-menu.show {
  display: block
}

.dropdown-header {
  display: block;
  padding: .5rem 1.5rem;
  margin-bottom: 0;
  font-size: .75rem;
  color: #6c757d;
  white-space: nowrap
}

.dropdown-item-text {
  display: block;
  padding: .25rem 1.5rem;
  color: #212529
}

.btn-group,.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle
}

.btn-group-vertical>.btn,.btn-group>.btn {
  position: relative;
  flex: 0 1 auto
}

.btn-group-vertical>.btn.active,.btn-group-vertical>.btn:active,.btn-group-vertical>.btn:focus,.btn-group-vertical>.btn:hover,.btn-group>.btn.active,.btn-group>.btn:active,.btn-group>.btn:focus,.btn-group>.btn:hover {
  z-index: 1
}

.btn-group-vertical .btn+.btn,.btn-group-vertical .btn+.btn-group,.btn-group-vertical .btn-group+.btn,.btn-group-vertical .btn-group+.btn-group,.btn-group .btn+.btn,.btn-group .btn+.btn-group,.btn-group .btn-group+.btn,.btn-group .btn-group+.btn-group {
  margin-left: -1px
}

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start
}

.btn-toolbar .input-group {
  width: auto
}

.btn-group>.btn:first-child {
  margin-left: 0
}

.btn-group>.btn-group:not(:last-child)>.btn,.btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.btn-group>.btn-group:not(:first-child)>.btn,.btn-group>.btn:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.dropdown-toggle-split {
  padding-right: 30px;
  padding-left: 30px
}

.dropdown-toggle-split:after,.dropright .dropdown-toggle-split:after,.dropup .dropdown-toggle-split:after {
  margin-left: 0
}

.dropleft .dropdown-toggle-split:before {
  margin-right: 0
}

.btn-group-sm>.btn+.dropdown-toggle-split,.btn-sm+.dropdown-toggle-split {
  padding-right: 11.25px;
  padding-left: 11.25px
}

.btn-group-lg>.btn+.dropdown-toggle-split,.btn-lg+.dropdown-toggle-split {
  padding-right: 36px;
  padding-left: 36px
}

.btn-group.show .dropdown-toggle,.btn-group.show .dropdown-toggle.btn-link {
  box-shadow: none
}

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center
}

.btn-group-vertical .btn,.btn-group-vertical .btn-group {
  width: 100%
}

.btn-group-vertical>.btn+.btn,.btn-group-vertical>.btn+.btn-group,.btn-group-vertical>.btn-group+.btn,.btn-group-vertical>.btn-group+.btn-group {
  margin-top: -1px;
  margin-left: 0
}

.btn-group-vertical>.btn-group:not(:last-child)>.btn,.btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:not(:first-child)>.btn,.btn-group-vertical>.btn:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0
}

.btn-group-toggle>.btn,.btn-group-toggle>.btn-group>.btn {
  margin-bottom: 0
}

.btn-group-toggle>.btn-group>.btn input[type=checkbox],.btn-group-toggle>.btn-group>.btn input[type=radio],.btn-group-toggle>.btn input[type=checkbox],.btn-group-toggle>.btn input[type=radio] {
  position: absolute;
  clip: rect(0,0,0,0);
  pointer-events: none
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%
}

.input-group>.custom-file,.input-group>.custom-select,.input-group>.form-control {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0
}

.input-group>.custom-file+.custom-file,.input-group>.custom-file+.custom-select,.input-group>.custom-file+.form-control,.input-group>.custom-select+.custom-file,.input-group>.custom-select+.custom-select,.input-group>.custom-select+.form-control,.input-group>.form-control+.custom-file,.input-group>.form-control+.custom-select,.input-group>.form-control+.form-control {
  margin-left: -1px
}

.input-group>.custom-file .custom-file-input:focus~.custom-file-label,.input-group>.custom-select:focus,.input-group>.form-control:focus {
  z-index: 3
}

.input-group>.custom-file .custom-file-input:focus {
  z-index: 4
}

.input-group>.custom-select:not(:last-child),.input-group>.form-control:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.input-group>.custom-select:not(:first-child),.input-group>.form-control:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.input-group>.custom-file {
  display: flex;
  align-items: center
}

.input-group>.custom-file:not(:last-child) .custom-file-label,.input-group>.custom-file:not(:last-child) .custom-file-label:after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.input-group>.custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.input-group-append,.input-group-prepend {
  display: flex
}

.input-group-append .btn,.input-group-prepend .btn {
  position: relative;
  z-index: 2
}

.input-group-append .btn+.btn,.input-group-append .btn+.input-group-text,.input-group-append .input-group-text+.btn,.input-group-append .input-group-text+.input-group-text,.input-group-prepend .btn+.btn,.input-group-prepend .btn+.input-group-text,.input-group-prepend .input-group-text+.btn,.input-group-prepend .input-group-text+.input-group-text {
  margin-left: -1px
}

.input-group-prepend {
  margin-right: -1px
}

.input-group-append {
  margin-left: -1px
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: .5rem .7rem;
  margin-bottom: 0;
  font-size: .875rem;
  font-weight: 400;
  line-height: 1.428571;
  color: #adb5bd;
  text-align: center;
  white-space: nowrap;
  background-color: transparent;
  border: 1px solid #cad1d7;
  border-radius: .25rem
}

.input-group-text input[type=checkbox],.input-group-text input[type=radio] {
  margin-top: 0
}

.input-group-lg>.form-control,.input-group-lg>.input-group-append>.btn,.input-group-lg>.input-group-append>.input-group-text,.input-group-lg>.input-group-prepend>.btn,.input-group-lg>.input-group-prepend>.input-group-text {
  height: calc(3.098987rem + 2px);
  padding: .875rem 1rem;
  font-size: .99925rem;
  line-height: 1.35;
  border-radius: .4285rem
}

.input-group-sm>.form-control,.input-group-sm>.input-group-append>.btn,.input-group-sm>.input-group-append>.input-group-text,.input-group-sm>.input-group-prepend>.btn,.input-group-sm>.input-group-prepend>.input-group-text {
  height: calc(1.5125rem + 2px);
  padding: .25rem .5rem;
  font-size: .75rem;
  line-height: 1.35;
  border-radius: .2857rem
}

.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group>.input-group-append:last-child>.input-group-text:not(:last-child),.input-group>.input-group-append:not(:last-child)>.btn,.input-group>.input-group-append:not(:last-child)>.input-group-text,.input-group>.input-group-prepend>.btn,.input-group>.input-group-prepend>.input-group-text {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.input-group>.input-group-append>.btn,.input-group>.input-group-append>.input-group-text,.input-group>.input-group-prepend:first-child>.btn:not(:first-child),.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child),.input-group>.input-group-prepend:not(:first-child)>.btn,.input-group>.input-group-prepend:not(:first-child)>.input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.custom-control {
  position: relative;
  display: block;
  min-height: 1.3125rem;
  padding-left: 1.75rem
}

.custom-control-inline {
  display: inline-flex;
  margin-right: 1rem
}

.custom-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0
}

.custom-control-input:checked~.custom-control-label:before {
  color: #fff;
  background-color: #e14eca;
  box-shadow: none
}

.custom-control-input:focus~.custom-control-label:before {
  box-shadow: none
}

.custom-control-input:active~.custom-control-label:before {
  color: #fff;
  background-color: #e14eca;
  box-shadow: none
}

.custom-control-input:disabled~.custom-control-label {
  color: #6c757d
}

.custom-control-input:disabled~.custom-control-label:before {
  background-color: #e9ecef
}

.custom-control-label {
  position: relative;
  margin-bottom: 0
}

.custom-control-label:before {
  pointer-events: none;
  user-select: none;
  background-color: transparent;
  box-shadow: none
}

.custom-control-label:after,.custom-control-label:before {
  position: absolute;
  top: .03125rem;
  left: -1.75rem;
  display: block;
  width: 1.25rem;
  height: 1.25rem;
  content: ""
}

.custom-control-label:after {
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 50% 50%
}

.custom-checkbox .custom-control-label:before {
  border-radius: .2857rem
}

.custom-checkbox .custom-control-input:checked~.custom-control-label:before {
  background-color: #e14eca
}

.custom-checkbox .custom-control-input:checked~.custom-control-label:after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23ffffff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E")
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-label:before {
  background-color: #e14eca;
  box-shadow: none
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-label:after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23ffffff' d='M0 2h4'/%3E%3C/svg%3E")
}

.custom-checkbox .custom-control-input:disabled:checked~.custom-control-label:before {
  background-color: rgba(225,78,202,.5)
}

.custom-checkbox .custom-control-input:disabled:indeterminate~.custom-control-label:before {
  background-color: rgba(225,78,202,.5)
}

.custom-radio .custom-control-label:before {
  border-radius: 50%
}

.custom-radio .custom-control-input:checked~.custom-control-label:before {
  background-color: #e14eca
}

.custom-radio .custom-control-input:checked~.custom-control-label:after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23ffffff'/%3E%3C/svg%3E")
}

.custom-radio .custom-control-input:disabled:checked~.custom-control-label:before {
  background-color: rgba(225,78,202,.5)
}

.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  padding: .375rem 1.75rem .375rem .75rem;
  line-height: 1.428571;
  color: hsla(0,0%,100%,.8);
  vertical-align: middle;
  background: transparent url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%2332325d' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center;
  background-size: 8px 10px;
  border: 1px solid #cad1d7;
  border-radius: .25rem;
  box-shadow: inset 0 1px 2px rgba(34,42,66,.075);
  appearance: none
}

.custom-select:focus {
  border-color: rgba(50,151,211,.25);
  outline: 0;
  box-shadow: inset 0 1px 2px rgba(34,42,66,.075),0 0 0 0 rgba(50,151,211,.5)
}

.custom-select:focus::-ms-value {
  color: hsla(0,0%,100%,.8);
  background-color: transparent
}

.custom-select[multiple],.custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: .75rem;
  background-image: none
}

.custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef
}

.custom-select::-ms-expand {
  opacity: 0
}

.custom-select-sm {
  height: calc(1.5125rem + 2px);
  font-size: 75%
}

.custom-select-lg,.custom-select-sm {
  padding-top: .375rem;
  padding-bottom: .375rem
}

.custom-select-lg {
  height: calc(3.098987rem + 2px);
  font-size: 125%
}

.custom-file {
  display: inline-block;
  margin-bottom: 0
}

.custom-file,.custom-file-input {
  position: relative;
  width: 100%;
  height: calc(2.25rem + 2px)
}

.custom-file-input {
  z-index: 2;
  margin: 0;
  opacity: 0
}

.custom-file-input:focus~.custom-file-label {
  border-color: rgba(50,151,211,.25);
  box-shadow: none
}

.custom-file-input:focus~.custom-file-label:after {
  border-color: rgba(50,151,211,.25)
}

.custom-file-input:disabled~.custom-file-label {
  background-color: #e9ecef
}

.custom-file-input:lang(en)~.custom-file-label:after {
  content: "Browse"
}

.custom-file-label {
  left: 0;
  z-index: 1;
  height: calc(2.25rem + 2px);
  border: 1px solid #cad1d7;
  border-radius: .25rem;
  box-shadow: none
}

.custom-file-label,.custom-file-label:after {
  position: absolute;
  top: 0;
  right: 0;
  padding: .5rem .7rem;
  line-height: 1.428571;
  color: hsla(0,0%,100%,.8);
  background-color: transparent
}

.custom-file-label:after {
  bottom: 0;
  z-index: 3;
  display: block;
  height: 2.25rem;
  content: "Browse";
  border-left: 1px solid #cad1d7;
  border-radius: 0 .25rem .25rem 0
}

.custom-range {
  width: 100%;
  padding-left: 0;
  background-color: transparent;
  appearance: none
}

.custom-range:focus {
  outline: none
}

.custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #1e1e2f,none
}

.custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #1e1e2f,none
}

.custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #1e1e2f,none
}

.custom-range::-moz-focus-outer {
  border: 0
}

.custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -.25rem;
  background-color: #e14eca;
  border: 0;
  border-radius: 1rem;
  box-shadow: 0 .1rem .25rem rgba(34,42,66,.1);
  transition: background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  appearance: none
}

@media screen and (prefers-reduced-motion:reduce) {
  .custom-range::-webkit-slider-thumb {
    transition: none
  }}

.custom-range::-webkit-slider-thumb:active {
  background-color: #fbe7f8
}

.custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: .5rem;
  color: transparent;
  cursor: pointer;
  background-color: #e3e3e3;
  border-color: transparent;
  border-radius: 1rem;
  box-shadow: inset 0 .25rem .25rem rgba(34,42,66,.1)
}

.custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #e14eca;
  border: 0;
  border-radius: 1rem;
  box-shadow: 0 .1rem .25rem rgba(34,42,66,.1);
  transition: background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  appearance: none
}

@media screen and (prefers-reduced-motion:reduce) {
  .custom-range::-moz-range-thumb {
    transition: none
  }}

.custom-range::-moz-range-thumb:active {
  background-color: #fbe7f8
}

.custom-range::-moz-range-track {
  width: 100%;
  height: .5rem;
  color: transparent;
  cursor: pointer;
  background-color: #e3e3e3;
  border-color: transparent;
  border-radius: 1rem;
  box-shadow: inset 0 .25rem .25rem rgba(34,42,66,.1)
}

.custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0;
  margin-left: 0;
  background-color: #e14eca;
  border: 0;
  border-radius: 1rem;
  box-shadow: 0 .1rem .25rem rgba(34,42,66,.1);
  transition: background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  appearance: none
}

@media screen and (prefers-reduced-motion:reduce) {
  .custom-range::-ms-thumb {
    transition: none
  }}

.custom-range::-ms-thumb:active {
  background-color: #fbe7f8
}

.custom-range::-ms-track {
  width: 100%;
  height: .5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: .5rem;
  box-shadow: inset 0 .25rem .25rem rgba(34,42,66,.1)
}

.custom-range::-ms-fill-lower,.custom-range::-ms-fill-upper {
  background-color: #e3e3e3;
  border-radius: 1rem
}

.custom-range::-ms-fill-upper {
  margin-right: 15px
}

.custom-control-label:before,.custom-file-label,.custom-select {
  transition: background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
}

@media screen and (prefers-reduced-motion:reduce) {
  .custom-control-label:before,.custom-file-label,.custom-select {
    transition: none
  }}

.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none
}

.nav-link {
  display: block;
  padding: .5rem 1rem
}

.nav-link:focus,.nav-link:hover {
  text-decoration: none
}

.nav-link.disabled {
  color: #6c757d
}

.nav-tabs {
  border-bottom: .0625rem solid #e3e3e3
}

.nav-tabs .nav-item {
  margin-bottom: -.0625rem
}

.nav-tabs .nav-link {
  border: .0625rem solid transparent;
  border-top-left-radius: .25rem;
  border-top-right-radius: .25rem
}

.nav-tabs .nav-link:focus,.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #e3e3e3
}

.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent
}

.nav-tabs .nav-item.show .nav-link,.nav-tabs .nav-link.active {
  color: #525f7f;
  background-color: #1e1e2f;
  border-color: #e3e3e3 #e3e3e3 #1e1e2f
}

.nav-tabs .dropdown-menu {
  margin-top: -.0625rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0
}

.nav-pills .nav-link {
  border-radius: .25rem
}

.nav-pills .nav-link.active,.nav-pills .show>.nav-link {
  color: #fff;
  background-color: #e14eca
}

.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center
}

.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center
}

.tab-content>.tab-pane {
  display: none
}

.tab-content>.active {
  display: block
}

.navbar {
  position: relative;
  padding: .625rem .9375rem
}

.navbar,.navbar>.container,.navbar>.container-fluid {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between
}

.navbar-brand {
  display: inline-block;
  padding-top: .406813rem;
  padding-bottom: .406813rem;
  margin-right: .9375rem;
  font-size: .99925rem;
  line-height: inherit;
  white-space: nowrap
}

.navbar-brand:focus,.navbar-brand:hover {
  text-decoration: none
}

.navbar-nav {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none
}

.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0
}

.navbar-nav .dropdown-menu {
  position: static;
  float: none
}

.navbar-text {
  display: inline-block;
  padding-top: .5rem;
  padding-bottom: .5rem
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center
}

.navbar-toggler {
  padding: .25rem .75rem;
  font-size: .99925rem;
  line-height: 1;
  background-color: transparent;
  border: .0625rem solid transparent;
  border-radius: .25rem
}

.navbar-toggler:focus,.navbar-toggler:hover {
  text-decoration: none
}

.navbar-toggler:not(:disabled):not(.disabled) {
  cursor: pointer
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: no-repeat 50%;
  background-size: 100% 100%
}

@media (max-width:575.98px) {
  .navbar-expand-sm>.container,.navbar-expand-sm>.container-fluid {
    padding-right: 0;
    padding-left: 0
  }}

@media (min-width:576px) {
  .navbar-expand-sm {
    flex-flow: rownowrap;
    justify-content: flex-start
  }

  .navbar-expand-sm .navbar-nav {
    flex-direction: row
  }

  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute
  }

  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 1rem;
    padding-left: 1rem
  }

  .navbar-expand-sm>.container,.navbar-expand-sm>.container-fluid {
    flex-wrap: nowrap
  }

  .navbar-expand-sm .navbar-collapse {
    display: flex!important;
    flex-basis: auto
  }

  .navbar-expand-sm .navbar-toggler {
    display: none
  }}

@media (max-width:767.98px) {
  .navbar-expand-md>.container,.navbar-expand-md>.container-fluid {
    padding-right: 0;
    padding-left: 0
  }}

@media (min-width:768px) {
  .navbar-expand-md {
    flex-flow: rownowrap;
    justify-content: flex-start
  }

  .navbar-expand-md .navbar-nav {
    flex-direction: row
  }

  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute
  }

  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 1rem;
    padding-left: 1rem
  }

  .navbar-expand-md>.container,.navbar-expand-md>.container-fluid {
    flex-wrap: nowrap
  }

  .navbar-expand-md .navbar-collapse {
    display: flex!important;
    flex-basis: auto
  }

  .navbar-expand-md .navbar-toggler {
    display: none
  }}

@media (max-width:991.98px) {
  .navbar-expand-lg>.container,.navbar-expand-lg>.container-fluid {
    padding-right: 0;
    padding-left: 0
  }}

@media (min-width:992px) {
  .navbar-expand-lg {
    flex-flow: rownowrap;
    justify-content: flex-start
  }

  .navbar-expand-lg .navbar-nav {
    flex-direction: row
  }

  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute
  }

  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 1rem;
    padding-left: 1rem
  }

  .navbar-expand-lg>.container,.navbar-expand-lg>.container-fluid {
    flex-wrap: nowrap
  }

  .navbar-expand-lg .navbar-collapse {
    display: flex!important;
    flex-basis: auto
  }

  .navbar-expand-lg .navbar-toggler {
    display: none
  }}

@media (max-width:1199.98px) {
  .navbar-expand-xl>.container,.navbar-expand-xl>.container-fluid {
    padding-right: 0;
    padding-left: 0
  }}

@media (min-width:1200px) {
  .navbar-expand-xl {
    flex-flow: rownowrap;
    justify-content: flex-start
  }

  .navbar-expand-xl .navbar-nav {
    flex-direction: row
  }

  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute
  }

  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 1rem;
    padding-left: 1rem
  }

  .navbar-expand-xl>.container,.navbar-expand-xl>.container-fluid {
    flex-wrap: nowrap
  }

  .navbar-expand-xl .navbar-collapse {
    display: flex!important;
    flex-basis: auto
  }

  .navbar-expand-xl .navbar-toggler {
    display: none
  }}

.navbar-expand {
  flex-flow: rownowrap;
  justify-content: flex-start
}

.navbar-expand>.container,.navbar-expand>.container-fluid {
  padding-right: 0;
  padding-left: 0
}

.navbar-expand .navbar-nav {
  flex-direction: row
}

.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute
}

.navbar-expand .navbar-nav .nav-link {
  padding-right: 1rem;
  padding-left: 1rem
}

.navbar-expand>.container,.navbar-expand>.container-fluid {
  flex-wrap: nowrap
}

.navbar-expand .navbar-collapse {
  display: flex!important;
  flex-basis: auto
}

.navbar-expand .navbar-toggler {
  display: none
}

.navbar-light .navbar-brand,.navbar-light .navbar-brand:focus,.navbar-light .navbar-brand:hover {
  color: rgba(34,42,66,.9)
}

.navbar-light .navbar-nav .nav-link {
  color: rgba(34,42,66,.5)
}

.navbar-light .navbar-nav .nav-link:focus,.navbar-light .navbar-nav .nav-link:hover {
  color: rgba(34,42,66,.7)
}

.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(34,42,66,.3)
}

.navbar-light .navbar-nav .active>.nav-link,.navbar-light .navbar-nav .nav-link.active,.navbar-light .navbar-nav .nav-link.show,.navbar-light .navbar-nav .show>.nav-link {
  color: rgba(34,42,66,.9)
}

.navbar-light .navbar-toggler {
  color: rgba(34,42,66,.5);
  border-color: transparent
}

.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml !default;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(34, 42, 66, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E")}.navbar-light .navbar-text{color:rgba(34,42,66,.5)}.navbar-light .navbar-text a,.navbar-light .navbar-text a:focus,.navbar-light .navbar-text a:hover{color:rgba(34,42,66,.9)}.navbar-dark .navbar-brand,.navbar-dark .navbar-brand:focus,.navbar-dark .navbar-brand:hover{color:hsla(0,0%,100%,.65)}.navbar-dark .navbar-nav .nav-link{color:hsla(0,0%,100%,.95)}.navbar-dark .navbar-nav .nav-link:focus,.navbar-dark .navbar-nav .nav-link:hover{color:hsla(0,0%,100%,.65)}.navbar-dark .navbar-nav .nav-link.disabled{color:hsla(0,0%,100%,.25)}.navbar-dark .navbar-nav .active>.nav-link,.navbar-dark .navbar-nav .nav-link.active,.navbar-dark .navbar-nav .nav-link.show,.navbar-dark .navbar-nav .show>.nav-link{color:hsla(0,0%,100%,.65)}.navbar-dark .navbar-toggler{color:hsla(0,0%,100%,.95);border-color:transparent}.navbar-dark .navbar-toggler-icon{background-image:url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http: //www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.95)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E")}.navbar-dark .navbar-text{color:hsla(0,0%,100%,.95)}.navbar-dark .navbar-text a,.navbar-dark .navbar-text a:focus,.navbar-dark .navbar-text a:hover{color:hsla(0,0%,100%,.65)}.card{display:flex;flex-direction:column;min-width:0;word-wrap:break-word;background-color:#fff;background-clip:border-box;border:.0625rem solid rgba(34,42,66,.05);border-radius:.2857rem}.card>hr{margin-right:0;margin-left:0}.card>.list-group:first-child .list-group-item:first-child{border-top-left-radius:.2857rem;border-top-right-radius:.2857rem}.card>.list-group:last-child .list-group-item:last-child{border-bottom-right-radius:.2857rem;border-bottom-left-radius:.2857rem}.card-body{flex:1 1 auto;padding:1.5rem}.card-title{margin-bottom:1.25rem}.card-subtitle{margin-top:-.625rem}.card-subtitle,.card-text:last-child{margin-bottom:0}.card-link:hover{text-decoration:none}.card-link+.card-link{margin-left:1.5rem}.card-header{padding:1.25rem 1.5rem;margin-bottom:0;background-color:#f6f9fc;border-bottom:.0625rem solid rgba(34,42,66,.05)}.card-header:first-child{border-radius:0.2232rem 0.2232rem 0 0}.card-header+.list-group .list-group-item:first-child{border-top:0}.card-footer{padding:1.25rem 1.5rem;background-color:#f6f9fc;border-top:.0625rem solid rgba(34,42,66,.05)}.card-footer:last-child{border-radius:0 0 0.2232rem 0.2232rem}.card-header-tabs{margin-bottom:-1.25rem;border-bottom:0}.card-header-pills,.card-header-tabs{margin-right:-.75rem;margin-left:-.75rem}.card-img-overlay{position:absolute;top:0;right:0;bottom:0;left:0;padding:1.25rem}.card-img{width:100%;border-radius:0.2232rem}.card-img-top{width:100%;border-top-left-radius:0.2232rem;border-top-right-radius:0.2232rem}.card-img-bottom{width:100%;border-bottom-right-radius:0.2232rem;border-bottom-left-radius:0.2232rem}.card-deck{display:flex;flex-direction:column}.card-deck .card{margin-bottom:15px}@media (min-width:576px){.card-deck{flex-flow:row wrap;margin-right:-15px;margin-left:-15px}.card-deck .card{display:flex;flex:1 0 0%;flex-direction:column;margin-right:15px;margin-bottom:0;margin-left:15px}}.card-group{display:flex;flex-direction:column}.card-group>.card{margin-bottom:15px}@media (min-width:576px){.card-group{flex-flow:row wrap}.card-group>.card{flex:1 0 0%;margin-bottom:0}.card-group>.card+.card{margin-left:0;border-left:0}.card-group>.card:first-child{border-top-right-radius:0;border-bottom-right-radius:0}.card-group>.card:first-child .card-header,.card-group>.card:first-child .card-img-top{border-top-right-radius:0}.card-group>.card:first-child .card-footer,.card-group>.card:first-child .card-img-bottom{border-bottom-right-radius:0}.card-group>.card:last-child{border-top-left-radius:0;border-bottom-left-radius:0}.card-group>.card:last-child .card-header,.card-group>.card:last-child .card-img-top{border-top-left-radius:0}.card-group>.card:last-child .card-footer,.card-group>.card:last-child .card-img-bottom{border-bottom-left-radius:0}.card-group>.card:only-child{border-radius:.2857rem}.card-group>.card:only-child .card-header,.card-group>.card:only-child .card-img-top{border-top-left-radius:.2857rem;border-top-right-radius:.2857rem}.card-group>.card:only-child .card-footer,.card-group>.card:only-child .card-img-bottom{border-bottom-right-radius:.2857rem;border-bottom-left-radius:.2857rem}.card-group>.card:not(:first-child):not(:last-child):not(:only-child),.card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-footer,.card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-header,.card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-img-bottom,.card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-img-top{border-radius:0}}.card-columns .card{margin-bottom:1.25rem}@media (min-width:576px){.card-columns{column-count:3;column-gap:1.25rem;orphans:1;widows:1}.card-columns .card{display:inline-block;width:100%}}.accordion .card:not(:first-of-type):not(:last-of-type){border-bottom:0;border-radius:0}.accordion .card:not(:first-of-type) .card-header:first-child{border-radius:0}.accordion .card:first-of-type{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.accordion .card:last-of-type{border-top-left-radius:0;border-top-right-radius:0}.breadcrumb{display:flex;flex-wrap:wrap;padding:.75rem 1rem;margin-bottom:1rem;list-style:none;background-color:#1d253b;border-radius:.25rem}.breadcrumb-item+.breadcrumb-item{padding-left:.5rem}.breadcrumb-item+.breadcrumb-item:before{display:inline-block;padding-right:.5rem;color:#fff;content:"/"}.breadcrumb-item+.breadcrumb-item:hover:before{text-decoration:underline;text-decoration:none}.breadcrumb-item.active{color:#fff}.pagination{display:flex;padding-left:0;list-style:none;border-radius:.25rem}.page-link{position:relative;display:block;padding:0 .6875rem;margin-left:-.0625rem;line-height:1.25;color:#fff;background-color:transparent;border:.0625rem solid #e3e3e3}.page-link:hover{z-index:2;color:#6c757d;text-decoration:none;background-color:#e3e3e3;border-color:#e3e3e3}.page-link:focus{z-index:2;outline:0;box-shadow:none}.page-link:not(:disabled):not(.disabled){cursor:pointer}.page-item:first-child .page-link{margin-left:0;border-top-left-radius:.25rem;border-bottom-left-radius:.25rem}.page-item:last-child .page-link{border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}.page-item.active .page-link{z-index:1;color:#fff;background-color:#e14eca;border-color:#e14eca}.page-item.disabled .page-link{color:#6c757d;pointer-events:none;cursor:auto;background-color:transparent;border-color:#e3e3e3}.pagination-lg .page-link{padding:.75rem 1.5rem;font-size:.99925rem;line-height:1.625rem}.pagination-lg .page-item:first-child .page-link{border-top-left-radius:.4285rem;border-bottom-left-radius:.4285rem}.pagination-lg .page-item:last-child .page-link{border-top-right-radius:.4285rem;border-bottom-right-radius:.4285rem}.pagination-sm .page-link{padding:.25rem .5rem;font-size:.75rem;line-height:1.5}.pagination-sm .page-item:first-child .page-link{border-top-left-radius:.2857rem;border-bottom-left-radius:.2857rem}.pagination-sm .page-item:last-child .page-link{border-top-right-radius:.2857rem;border-bottom-right-radius:.2857rem}.badge{display:inline-block;padding:.25rem .5rem;font-size:.62475rem;font-weight:700;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25rem}.badge:empty{display:none}.btn .badge{position:relative;top:-1px}.badge-pill{padding-right:.875em;padding-left:.875em;border-radius:.875rem}.badge-primary{color:#fff;background-color:#e14eca}.badge-primary[href]:focus,.badge-primary[href]:hover{color:#fff;text-decoration:none;background-color:#d725bb}.badge-secondary{color:#212529;background-color:#f4f5f7}.badge-secondary[href]:focus,.badge-secondary[href]:hover{color:#212529;text-decoration:none;background-color:#d6dae2}.badge-success{color:#fff;background-color:#00f2c3}.badge-success[href]:focus,.badge-success[href]:hover{color:#fff;text-decoration:none;background-color:#00bf9a}.badge-info{color:#fff;background-color:#1d8cf8}.badge-info[href]:focus,.badge-info[href]:hover{color:#fff;text-decoration:none;background-color:#0772db}.badge-warning{color:#fff;background-color:#ff8d72}.badge-warning[href]:focus,.badge-warning[href]:hover{color:#fff;text-decoration:none;background-color:#ff643f}.badge-danger{color:#fff;background-color:#fd5d93}.badge-danger[href]:focus,.badge-danger[href]:hover{color:#fff;text-decoration:none;background-color:#fc2b71}.badge-light{color:#fff;background-color:#adb5bd}.badge-light[href]:focus,.badge-light[href]:hover{color:#fff;text-decoration:none;background-color:#919ca6}.badge-dark{color:#fff;background-color:#212529}.badge-dark[href]:focus,.badge-dark[href]:hover{color:#fff;text-decoration:none;background-color:#0a0c0d}.badge-default{color:#fff;background-color:#344675}.badge-default[href]:focus,.badge-default[href]:hover{color:#fff;text-decoration:none;background-color:#243152}.badge-white{color:#212529;background-color:#fff}.badge-white[href]:focus,.badge-white[href]:hover{color:#212529;text-decoration:none;background-color:#e6e6e6}.badge-neutral{color:#212529;background-color:#fff}.badge-neutral[href]:focus,.badge-neutral[href]:hover{color:#212529;text-decoration:none;background-color:#e6e6e6}.badge-darker{color:#fff;background-color:#000}.badge-darker[href]:focus,.badge-darker[href]:hover{color:#fff;text-decoration:none;background-color:#000}.jumbotron{padding:2rem 1rem;margin-bottom:2rem;background-color:#e9ecef;border-radius:.4285rem}@media (min-width:576px){.jumbotron{padding:4rem 2rem}}.jumbotron-fluid{padding-right:0;padding-left:0;border-radius:0}.alert{position:relative;padding:.9rem 1.25rem;margin-bottom:1rem;border:.0625rem solid transparent;border-radius:.2857rem}.alert-heading{color:inherit}.alert-link{font-weight:600}.alert-dismissible{padding-right:3.8125rem}.alert-dismissible .close{position:absolute;top:0;right:0;padding:.9rem 1.25rem;color:inherit}.alert-primary{color:#e14eca;background-color:#e66ad2;border-color:#e66ad2}.alert-primary hr{border-top-color:#e254cb}.alert-primary .alert-link{color:#d725bb}.alert-secondary{color:#f4f5f7;background-color:#f6f7f8;border-color:#f6f7f8}.alert-secondary hr{border-top-color:#e8eaed}.alert-secondary .alert-link{color:#d6dae2}.alert-success{color:#00f2c3;background-color:#29f4cd;border-color:#29f4cd}.alert-success hr{border-top-color:#11f3c7}.alert-success .alert-link{color:#00bf9a}.alert-info{color:#1d8cf8;background-color:#419ef9;border-color:#419ef9}.alert-info hr{border-top-color:#2891f8}.alert-info .alert-link{color:#0772db}.alert-warning{color:#ff8d72;background-color:#ff9f89;border-color:#ff9f89}.alert-warning hr{border-top-color:#ff8a70}.alert-warning .alert-link{color:#ff643f}.alert-danger{color:#fd5d93;background-color:#fd77a4;border-color:#fd77a4}.alert-danger hr{border-top-color:#fd5e93}.alert-danger .alert-link{color:#fc2b71}.alert-light{color:#adb5bd;background-color:#bac1c8;border-color:#bac1c8}.alert-light hr{border-top-color:#acb4bd}.alert-light .alert-link{color:#919ca6}.alert-dark{color:#212529;background-color:#45484b;border-color:#45484b}.alert-dark hr{border-top-color:#393b3e}.alert-dark .alert-link{color:#0a0c0d}.alert-default{color:#344675;background-color:#54648b;border-color:#54648b}.alert-default hr{border-top-color:#4a597b}.alert-default .alert-link{color:#243152}.alert-white{color:#fff;background-color:#fff;border-color:#fff}.alert-white hr{border-top-color:#f2f2f2}.alert-white .alert-link{color:#e6e6e6}.alert-neutral{color:#fff;background-color:#fff;border-color:#fff}.alert-neutral hr{border-top-color:#f2f2f2}.alert-neutral .alert-link{color:#e6e6e6}.alert-darker{color:#000;background-color:#292929;border-color:#292929}.alert-darker hr{border-top-color:#1c1c1c}.alert-darker .alert-link{color:#000}@keyframes a{0%{background-position:.5rem 0}to{background-position:0 0}}.progress{display:flex;height:.5rem;overflow:hidden;font-size:.65625rem;background-color:rgba(0,0,0,.3);border-radius:.875rem;box-shadow:0 0 0 3px rgba(0,0,0,.3)}.progress-bar{display:flex;flex-direction:column;justify-content:center;color:#fff;text-align:center;white-space:nowrap;background-color:#e14eca;transition:width .6s ease}@media screen and (prefers-reduced-motion:reduce){.progress-bar{transition:none}}.progress-bar-striped{background-image:linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent);background-size:.5rem .5rem}.progress-bar-animated{animation:a 1s linear infinite}.media{display:flex;align-items:flex-start}.media-body{flex:1}.list-group{display:flex;flex-direction:column;padding-left:0;margin-bottom:0}.list-group-item-action{width:100%;color:#525f7f;text-align:inherit}.list-group-item-action:focus,.list-group-item-action:hover{color:#525f7f;text-decoration:none;background-color:#f6f9fc}.list-group-item-action:active{color:#525f7f;background-color:#e9ecef}.list-group-item{position:relative;display:block;padding:1rem;margin-bottom:-.0625rem;background-color:#fff;border:.0625rem solid #e9ecef}.list-group-item:first-child{border-top-left-radius:.25rem;border-top-right-radius:.25rem}.list-group-item:last-child{margin-bottom:0;border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.list-group-item:focus,.list-group-item:hover{z-index:1;text-decoration:none}.list-group-item.disabled,.list-group-item:disabled{color:#6c757d;background-color:#fff}.list-group-item.active{z-index:2;color:#fff;background-color:#e14eca;border-color:#e14eca}.list-group-flush .list-group-item{border-right:0;border-left:0;border-radius:0}.list-group-flush:first-child .list-group-item:first-child{border-top:0}.list-group-flush:last-child .list-group-item:last-child{border-bottom:0}.list-group-item-primary{color:#853d89;background-color:#f7cdf0}.list-group-item-primary.list-group-item-action:focus,.list-group-item-primary.list-group-item-action:hover{color:#853d89;background-color:#f3b7e9}.list-group-item-primary.list-group-item-action.active{color:#fff;background-color:#853d89;border-color:#853d89}.list-group-item-secondary{color:#8f94a0;background-color:#fcfcfd}.list-group-item-secondary.list-group-item-action:focus,.list-group-item-secondary.list-group-item-action:hover{color:#8f94a0;background-color:#ededf3}.list-group-item-secondary.list-group-item-action.active{color:#fff;background-color:#8f94a0;border-color:#8f94a0}.list-group-item-success{color:#109285;background-color:#b8fbee}.list-group-item-success.list-group-item-action:focus,.list-group-item-success.list-group-item-action:hover{color:#109285;background-color:#a0fae8}.list-group-item-success.list-group-item-action.active{color:#fff;background-color:#109285;border-color:#109285}.list-group-item-info{color:#1f5da1;background-color:#c0dffd}.list-group-item-info.list-group-item-action:focus,.list-group-item-info.list-group-item-action:hover{color:#1f5da1;background-color:#a7d2fc}.list-group-item-info.list-group-item-action.active{color:#fff;background-color:#1f5da1;border-color:#1f5da1}.list-group-item-warning{color:#955d5b;background-color:#ffdfd8}.list-group-item-warning.list-group-item-action:focus,.list-group-item-warning.list-group-item-action:hover{color:#955d5b;background-color:#ffcabf}.list-group-item-warning.list-group-item-action.active{color:#fff;background-color:#955d5b;border-color:#955d5b}.list-group-item-danger{color:#94456c;background-color:#fed2e1}.list-group-item-danger.list-group-item-action:focus,.list-group-item-danger.list-group-item-action:hover{color:#94456c;background-color:#fdb9d0}.list-group-item-danger.list-group-item-action.active{color:#fff;background-color:#94456c;border-color:#94456c}.list-group-item-light{color:#6a7282;background-color:#e8eaed}.list-group-item-light.list-group-item-action:focus,.list-group-item-light.list-group-item-action:hover{color:#6a7282;background-color:#dadde2}.list-group-item-light.list-group-item-action.active{color:#fff;background-color:#6a7282;border-color:#6a7282}.list-group-item-dark{color:#212735;background-color:#c1c2c3}.list-group-item-dark.list-group-item-action:focus,.list-group-item-dark.list-group-item-action:hover{color:#212735;background-color:#b4b5b6}.list-group-item-dark.list-group-item-action.active{color:#fff;background-color:#212735;border-color:#212735}.list-group-item-default{color:#2b395d;background-color:#c6cbd8}.list-group-item-default.list-group-item-action:focus,.list-group-item-default.list-group-item-action:hover{color:#2b395d;background-color:#b7bdce}.list-group-item-default.list-group-item-action.active{color:#fff;background-color:#2b395d;border-color:#2b395d}.list-group-item-white{color:#9599a4;background-color:#fff}.list-group-item-white.list-group-item-action:focus,.list-group-item-white.list-group-item-action:hover{color:#9599a4;background-color:#f2f2f2}.list-group-item-white.list-group-item-action.active{color:#fff;background-color:#9599a4;border-color:#9599a4}.list-group-item-neutral{color:#9599a4;background-color:#fff}.list-group-item-neutral.list-group-item-action:focus,.list-group-item-neutral.list-group-item-action:hover{color:#9599a4;background-color:#f2f2f2}.list-group-item-neutral.list-group-item-action.active{color:#fff;background-color:#9599a4;border-color:#9599a4}.list-group-item-darker{color:#101420;background-color:#b8b8b8}.list-group-item-darker.list-group-item-action:focus,.list-group-item-darker.list-group-item-action:hover{color:#101420;background-color:#ababab}.list-group-item-darker.list-group-item-action.active{color:#fff;background-color:#101420;border-color:#101420}.close{float:right;font-size:1.3125rem;font-weight:600;line-height:1;color:rgba(0,0,0,.6);text-shadow:none;opacity:.5}.close:not(:disabled):not(.disabled){cursor:pointer}.close:not(:disabled):not(.disabled):focus,.close:not(:disabled):not(.disabled):hover{color:rgba(0,0,0,.6);text-decoration:none;opacity:.75}button.close{padding:0;background-color:transparent;border:0;-webkit-appearance:none}.modal-open{overflow:hidden}.modal-open .modal{overflow-x:hidden;overflow-y:auto}.modal{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;display:none;overflow:hidden;outline:0}.modal-dialog{position:relative;width:auto;margin:.5rem;pointer-events:none}.modal.fade .modal-dialog{transition:transform .3s ease-out;transform:translateY(-25%)}@media screen and (prefers-reduced-motion:reduce){.modal.fade .modal-dialog{transition:none}}.modal.show .modal-dialog{transform:translate(0)}.modal-dialog-centered{display:flex;align-items:center;min-height:calc(100% - 1rem)}.modal-dialog-centered:before{display:block;height:calc(100vh - 1rem);content:""}.modal-content{position:relative;display:flex;flex-direction:column;width:100%;pointer-events:auto;background-color:#fff;background-clip:padding-box;border:1px solid rgba(34,42,66,.2);border-radius:.2857rem;box-shadow:0 10px 50px 0 rgba(0,0,0,.5);outline:0}.modal-backdrop{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1040;background-color:#222a42}.modal-backdrop.fade{opacity:0}.modal-backdrop.show{opacity:.16}.modal-header{display:flex;align-items:flex-start;justify-content:space-between;padding:24px 24px 0;border-bottom:1px solid #e9ecef;border-top-left-radius:.2857rem;border-top-right-radius:.2857rem}.modal-header .close{padding:24px 24px 0;margin:-24px 24px 0}.modal-title{margin-bottom:0;line-height:1.1}.modal-body{position:relative;flex:1 1 auto;padding:24px 24px 16px}.modal-footer{display:flex;align-items:center;justify-content:flex-end;padding:24px 24px 16px;border-top:1px solid #e9ecef}.modal-footer>:not(:first-child){margin-left:.25rem}.modal-footer>:not(:last-child){margin-right:.25rem}.modal-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}@media (min-width:576px){.modal-dialog{max-width:500px;margin:1.75rem auto}.modal-dialog-centered{min-height:calc(100% - 3.5rem)}.modal-dialog-centered:before{height:calc(100vh - 3.5rem)}.modal-content{box-shadow:0 15px 35px rgba(50,50,93,.2),0 5px 15px rgba(0,0,0,.17)}.modal-sm{max-width:380px}}@media (min-width:992px){.modal-lg{max-width:800px}}.tooltip{position:absolute;z-index:1070;display:block;margin:0;font-family:Poppins,sans-serif;font-style:normal;font-weight:400;line-height:1.5;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;white-space:normal;line-break:auto;font-size:.75rem;word-wrap:break-word;opacity:0}.tooltip.show{opacity:.9}.tooltip .arrow{position:absolute;display:block;width:.8rem;height:.4rem}.tooltip .arrow:before{position:absolute;content:"";border-color:transparent;border-style:solid}.bs-tooltip-auto[x-placement^=top],.bs-tooltip-top{padding:.4rem 0}.bs-tooltip-auto[x-placement^=top] .arrow,.bs-tooltip-top .arrow{bottom:0}.bs-tooltip-auto[x-placement^=top] .arrow:before,.bs-tooltip-top .arrow:before{top:0;border-width:.4rem .4rem 0;border-top-color:#fff}.bs-tooltip-auto[x-placement^=right],.bs-tooltip-right{padding:0 .4rem}.bs-tooltip-auto[x-placement^=right] .arrow,.bs-tooltip-right .arrow{left:0;width:.4rem;height:.8rem}.bs-tooltip-auto[x-placement^=right] .arrow:before,.bs-tooltip-right .arrow:before{right:0;border-width:.4rem .4rem .4rem 0;border-right-color:#fff}.bs-tooltip-auto[x-placement^=bottom],.bs-tooltip-bottom{padding:.4rem 0}.bs-tooltip-auto[x-placement^=bottom] .arrow,.bs-tooltip-bottom .arrow{top:0}.bs-tooltip-auto[x-placement^=bottom] .arrow:before,.bs-tooltip-bottom .arrow:before{bottom:0;border-width:0 .4rem .4rem;border-bottom-color:#fff}.bs-tooltip-auto[x-placement^=left],.bs-tooltip-left{padding:0 .4rem}.bs-tooltip-auto[x-placement^=left] .arrow,.bs-tooltip-left .arrow{right:0;width:.4rem;height:.8rem}.bs-tooltip-auto[x-placement^=left] .arrow:before,.bs-tooltip-left .arrow:before{left:0;border-width:.4rem 0 .4rem .4rem;border-left-color:#fff}.tooltip-inner{max-width:200px;padding:.25rem .5rem;color:#222a42;text-align:center;background-color:#fff;border-radius:.25rem}.popover{top:0;left:0;z-index:1060;max-width:276px;font-family:Poppins,sans-serif;font-style:normal;font-weight:400;line-height:1.5;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;white-space:normal;line-break:auto;font-size:.75rem;word-wrap:break-word;background-color:#fff;background-clip:padding-box;border:1px solid rgba(34,42,66,.05);border-radius:.4285rem;box-shadow:0 .5rem 2rem 0 rgba(34,42,66,.2)}.popover,.popover .arrow{position:absolute;display:block}.popover .arrow{width:1.5rem;height:.75rem;margin:0 .4285rem}.popover .arrow:after,.popover .arrow:before{position:absolute;display:block;content:"";border-color:transparent;border-style:solid}.bs-popover-auto[x-placement^=top],.bs-popover-top{margin-bottom:.75rem}.bs-popover-auto[x-placement^=top] .arrow,.bs-popover-top .arrow{bottom:calc((.75rem + 1px) * -1)}.bs-popover-auto[x-placement^=top] .arrow:after,.bs-popover-auto[x-placement^=top] .arrow:before,.bs-popover-top .arrow:after,.bs-popover-top .arrow:before{border-width:.75rem .75rem 0}.bs-popover-auto[x-placement^=top] .arrow:before,.bs-popover-top .arrow:before{bottom:0;border-top-color:transparent}.bs-popover-auto[x-placement^=top] .arrow:after,.bs-popover-top .arrow:after{bottom:1px;border-top-color:#fff}.bs-popover-auto[x-placement^=right],.bs-popover-right{margin-left:.75rem}.bs-popover-auto[x-placement^=right] .arrow,.bs-popover-right .arrow{left:calc((.75rem + 1px) * -1);width:.75rem;height:1.5rem;margin:.4285rem 0}.bs-popover-auto[x-placement^=right] .arrow:after,.bs-popover-auto[x-placement^=right] .arrow:before,.bs-popover-right .arrow:after,.bs-popover-right .arrow:before{border-width:.75rem .75rem .75rem 0}.bs-popover-auto[x-placement^=right] .arrow:before,.bs-popover-right .arrow:before{left:0;border-right-color:transparent}.bs-popover-auto[x-placement^=right] .arrow:after,.bs-popover-right .arrow:after{left:1px;border-right-color:#fff}.bs-popover-auto[x-placement^=bottom],.bs-popover-bottom{margin-top:.75rem}.bs-popover-auto[x-placement^=bottom] .arrow,.bs-popover-bottom .arrow{top:calc((.75rem + 1px) * -1)}.bs-popover-auto[x-placement^=bottom] .arrow:after,.bs-popover-auto[x-placement^=bottom] .arrow:before,.bs-popover-bottom .arrow:after,.bs-popover-bottom .arrow:before{border-width:0 .75rem .75rem}.bs-popover-auto[x-placement^=bottom] .arrow:before,.bs-popover-bottom .arrow:before{top:0;border-bottom-color:transparent}.bs-popover-auto[x-placement^=bottom] .arrow:after,.bs-popover-bottom .arrow:after{top:1px;border-bottom-color:#fff}.bs-popover-auto[x-placement^=bottom] .popover-header:before,.bs-popover-bottom .popover-header:before{position:absolute;top:0;left:50%;display:block;width:1.5rem;margin-left:-.75rem;content:"";border-bottom:1px solid #fff}.bs-popover-auto[x-placement^=left],.bs-popover-left{margin-right:.75rem}.bs-popover-auto[x-placement^=left] .arrow,.bs-popover-left .arrow{right:calc((.75rem + 1px) * -1);width:.75rem;height:1.5rem;margin:.4285rem 0}.bs-popover-auto[x-placement^=left] .arrow:after,.bs-popover-auto[x-placement^=left] .arrow:before,.bs-popover-left .arrow:after,.bs-popover-left .arrow:before{border-width:.75rem 0 .75rem .75rem}.bs-popover-auto[x-placement^=left] .arrow:before,.bs-popover-left .arrow:before{right:0;border-left-color:transparent}.bs-popover-auto[x-placement^=left] .arrow:after,.bs-popover-left .arrow:after{right:1px;border-left-color:#fff}.popover-header{padding:.75rem;margin-bottom:0;font-size:.875rem;color:#32325d;background-color:#fff;border-bottom:1px solid #f2f2f2;border-top-left-radius:calc(.4285rem - 1px);border-top-right-radius:calc(.4285rem - 1px)}.popover-header:empty{display:none}.popover-body{padding:.75rem;color:#525f7f}.carousel{position:relative}.carousel-inner{position:relative;width:100%;overflow:hidden}.carousel-item{position:relative;display:none;align-items:center;width:100%;backface-visibility:hidden;perspective:1000px}.carousel-item-next,.carousel-item-prev,.carousel-item.active{display:block;transition:transform .6s ease}@media screen and (prefers-reduced-motion:reduce){.carousel-item-next,.carousel-item-prev,.carousel-item.active{transition:none}}.carousel-item-next,.carousel-item-prev{position:absolute;top:0}.carousel-item-next.carousel-item-left,.carousel-item-prev.carousel-item-right{transform:translateX(0)}@supports (transform-style:preserve-3d){.carousel-item-next.carousel-item-left,.carousel-item-prev.carousel-item-right{transform:translateZ(0)}}.active.carousel-item-right,.carousel-item-next{transform:translateX(100%)}@supports (transform-style:preserve-3d){.active.carousel-item-right,.carousel-item-next{transform:translate3d(100%,0,0)}}.active.carousel-item-left,.carousel-item-prev{transform:translateX(-100%)}@supports (transform-style:preserve-3d){.active.carousel-item-left,.carousel-item-prev{transform:translate3d(-100%,0,0)}}.carousel-fade .carousel-item{opacity:0;transition-duration:.6s;transition-property:opacity}.carousel-fade .carousel-item-next.carousel-item-left,.carousel-fade .carousel-item-prev.carousel-item-right,.carousel-fade .carousel-item.active{opacity:1}.carousel-fade .active.carousel-item-left,.carousel-fade .active.carousel-item-right{opacity:0}.carousel-fade .active.carousel-item-left,.carousel-fade .active.carousel-item-prev,.carousel-fade .carousel-item-next,.carousel-fade .carousel-item-prev,.carousel-fade .carousel-item.active{transform:translateX(0)}@supports (transform-style:preserve-3d){.carousel-fade .active.carousel-item-left,.carousel-fade .active.carousel-item-prev,.carousel-fade .carousel-item-next,.carousel-fade .carousel-item-prev,.carousel-fade .carousel-item.active{transform:translateZ(0)}}.carousel-control-next,.carousel-control-prev{position:absolute;top:0;bottom:0;display:flex;align-items:center;justify-content:center;width:15%;color:#fff;text-align:center;opacity:.5}.carousel-control-next:focus,.carousel-control-next:hover,.carousel-control-prev:focus,.carousel-control-prev:hover{color:#fff;text-decoration:none;outline:0;opacity:.9}.carousel-control-prev{left:0}.carousel-control-next{right:0}.carousel-control-next-icon,.carousel-control-prev-icon{display:inline-block;width:20px;height:20px;background:transparent no-repeat 50%;background-size:100% 100%}.carousel-control-prev-icon{background-image:url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http: //www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E")}.carousel-control-next-icon{background-image:url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http: //www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E")}.carousel-indicators{position:absolute;right:0;bottom:10px;left:0;z-index:15;display:flex;justify-content:center;padding-left:0;margin-right:15%;margin-left:15%;list-style:none}.carousel-indicators li{position:relative;flex:0 1 auto;width:30px;height:3px;margin-right:3px;margin-left:3px;text-indent:-999px;cursor:pointer;background-color:hsla(0,0%,100%,.5)}.carousel-indicators li:before{top:-10px}.carousel-indicators li:after,.carousel-indicators li:before{position:absolute;left:0;display:inline-block;width:100%;height:10px;content:""}.carousel-indicators li:after{bottom:-10px}.carousel-indicators .active{background-color:#fff}.carousel-caption{position:absolute;right:15%;bottom:20px;left:15%;z-index:10;padding-top:20px;padding-bottom:20px;color:#fff;text-align:center}.align-baseline{vertical-align:baseline!important}.align-top{vertical-align:top!important}.align-middle{vertical-align:middle!important}.align-bottom{vertical-align:bottom!important}.align-text-bottom{vertical-align:text-bottom!important}.align-text-top{vertical-align:text-top!important}.bg-primary{background-color:#e14eca!important}a.bg-primary:focus,a.bg-primary:hover,button.bg-primary:focus,button.bg-primary:hover{background-color:#d725bb!important}.bg-secondary{background-color:#f4f5f7!important}a.bg-secondary:focus,a.bg-secondary:hover,button.bg-secondary:focus,button.bg-secondary:hover{background-color:#d6dae2!important}.bg-success{background-color:#00f2c3!important}a.bg-success:focus,a.bg-success:hover,button.bg-success:focus,button.bg-success:hover{background-color:#00bf9a!important}.bg-info{background-color:#1d8cf8!important}a.bg-info:focus,a.bg-info:hover,button.bg-info:focus,button.bg-info:hover{background-color:#0772db!important}.bg-warning{background-color:#ff8d72!important}a.bg-warning:focus,a.bg-warning:hover,button.bg-warning:focus,button.bg-warning:hover{background-color:#ff643f!important}.bg-danger{background-color:#fd5d93!important}a.bg-danger:focus,a.bg-danger:hover,button.bg-danger:focus,button.bg-danger:hover{background-color:#fc2b71!important}.bg-light{background-color:#adb5bd!important}a.bg-light:focus,a.bg-light:hover,button.bg-light:focus,button.bg-light:hover{background-color:#919ca6!important}.bg-dark{background-color:#212529!important}a.bg-dark:focus,a.bg-dark:hover,button.bg-dark:focus,button.bg-dark:hover{background-color:#0a0c0d!important}.bg-default{background-color:#344675!important}a.bg-default:focus,a.bg-default:hover,button.bg-default:focus,button.bg-default:hover{background-color:#243152!important}a.bg-white:focus,a.bg-white:hover,button.bg-white:focus,button.bg-white:hover{background-color:#e6e6e6!important}.bg-neutral{background-color:#fff!important}a.bg-neutral:focus,a.bg-neutral:hover,button.bg-neutral:focus,button.bg-neutral:hover{background-color:#e6e6e6!important}.bg-darker,a.bg-darker:focus,a.bg-darker:hover,button.bg-darker:focus,button.bg-darker:hover{background-color:#000!important}.bg-white{background-color:#fff!important}.bg-transparent{background-color:transparent!important}.border{border:.0625rem solid #e9ecef!important}.border-top{border-top:.0625rem solid #e9ecef!important}.border-right{border-right:.0625rem solid #e9ecef!important}.border-bottom{border-bottom:.0625rem solid #e9ecef!important}.border-left{border-left:.0625rem solid #e9ecef!important}.border-0{border:0!important}.border-top-0{border-top:0!important}.border-right-0{border-right:0!important}.border-bottom-0{border-bottom:0!important}.border-left-0{border-left:0!important}.border-primary{border-color:#e14eca!important}.border-secondary{border-color:#f4f5f7!important}.border-success{border-color:#00f2c3!important}.border-info{border-color:#1d8cf8!important}.border-warning{border-color:#ff8d72!important}.border-danger{border-color:#fd5d93!important}.border-light{border-color:#adb5bd!important}.border-dark{border-color:#212529!important}.border-default{border-color:#344675!important}.border-neutral{border-color:#fff!important}.border-darker{border-color:#000!important}.border-white{border-color:#fff!important}.rounded{border-radius:.25rem!important}.rounded-top{border-top-left-radius:.25rem!important}.rounded-right,.rounded-top{border-top-right-radius:.25rem!important}.rounded-bottom,.rounded-right{border-bottom-right-radius:.25rem!important}.rounded-bottom,.rounded-left{border-bottom-left-radius:.25rem!important}.rounded-left{border-top-left-radius:.25rem!important}.rounded-circle{border-radius:50%!important}.rounded-0{border-radius:0!important}.clearfix:after{display:block;clear:both;content:""}.d-none{display:none!important}.d-inline{display:inline!important}.d-inline-block{display:inline-block!important}.d-block{display:block!important}.d-table{display:table!important}.d-table-row{display:table-row!important}.d-table-cell{display:table-cell!important}.d-flex{display:flex!important}.d-inline-flex{display:inline-flex!important}@media (min-width:576px){.d-sm-none{display:none!important}.d-sm-inline{display:inline!important}.d-sm-inline-block{display:inline-block!important}.d-sm-block{display:block!important}.d-sm-table{display:table!important}.d-sm-table-row{display:table-row!important}.d-sm-table-cell{display:table-cell!important}.d-sm-flex{display:flex!important}.d-sm-inline-flex{display:inline-flex!important}}@media (min-width:768px){.d-md-none{display:none!important}.d-md-inline{display:inline!important}.d-md-inline-block{display:inline-block!important}.d-md-block{display:block!important}.d-md-table{display:table!important}.d-md-table-row{display:table-row!important}.d-md-table-cell{display:table-cell!important}.d-md-flex{display:flex!important}.d-md-inline-flex{display:inline-flex!important}}@media (min-width:992px){.d-lg-none{display:none!important}.d-lg-inline{display:inline!important}.d-lg-inline-block{display:inline-block!important}.d-lg-block{display:block!important}.d-lg-table{display:table!important}.d-lg-table-row{display:table-row!important}.d-lg-table-cell{display:table-cell!important}.d-lg-flex{display:flex!important}.d-lg-inline-flex{display:inline-flex!important}}@media (min-width:1200px){.d-xl-none{display:none!important}.d-xl-inline{display:inline!important}.d-xl-inline-block{display:inline-block!important}.d-xl-block{display:block!important}.d-xl-table{display:table!important}.d-xl-table-row{display:table-row!important}.d-xl-table-cell{display:table-cell!important}.d-xl-flex{display:flex!important}.d-xl-inline-flex{display:inline-flex!important}}@media print{.d-print-none{display:none!important}.d-print-inline{display:inline!important}.d-print-inline-block{display:inline-block!important}.d-print-block{display:block!important}.d-print-table{display:table!important}.d-print-table-row{display:table-row!important}.d-print-table-cell{display:table-cell!important}.d-print-flex{display:flex!important}.d-print-inline-flex{display:inline-flex!important}}.embed-responsive{position:relative;display:block;width:100%;padding:0;overflow:hidden}.embed-responsive:before{display:block;content:""}.embed-responsive .embed-responsive-item,.embed-responsive embed,.embed-responsive iframe,.embed-responsive object,.embed-responsive video{position:absolute;top:0;bottom:0;left:0;width:100%;height:100%;border:0}.embed-responsive-21by9:before{padding-top:42.857143%}.embed-responsive-16by9:before{padding-top:56.25%}.embed-responsive-4by3:before{padding-top:75%}.embed-responsive-1by1:before{padding-top:100%}.flex-row{flex-direction:row!important}.flex-column{flex-direction:column!important}.flex-row-reverse{flex-direction:row-reverse!important}.flex-column-reverse{flex-direction:column-reverse!important}.flex-wrap{flex-wrap:wrap!important}.flex-nowrap{flex-wrap:nowrap!important}.flex-wrap-reverse{flex-wrap:wrap-reverse!important}.flex-fill{flex:1 1 auto!important}.flex-grow-0{flex-grow:0!important}.flex-grow-1{flex-grow:1!important}.flex-shrink-0{flex-shrink:0!important}.flex-shrink-1{flex-shrink:1!important}.justify-content-start{justify-content:flex-start!important}.justify-content-end{justify-content:flex-end!important}.justify-content-center{justify-content:center!important}.justify-content-between{justify-content:space-between!important}.justify-content-around{justify-content:space-around!important}.align-items-start{align-items:flex-start!important}.align-items-end{align-items:flex-end!important}.align-items-center{align-items:center!important}.align-items-baseline{align-items:baseline!important}.align-items-stretch{align-items:stretch!important}.align-content-start{align-content:flex-start!important}.align-content-end{align-content:flex-end!important}.align-content-center{align-content:center!important}.align-content-between{align-content:space-between!important}.align-content-around{align-content:space-around!important}.align-content-stretch{align-content:stretch!important}.align-self-auto{align-self:auto!important}.align-self-start{align-self:flex-start!important}.align-self-end{align-self:flex-end!important}.align-self-center{align-self:center!important}.align-self-baseline{align-self:baseline!important}.align-self-stretch{align-self:stretch!important}@media (min-width:576px){.flex-sm-row{flex-direction:row!important}.flex-sm-column{flex-direction:column!important}.flex-sm-row-reverse{flex-direction:row-reverse!important}.flex-sm-column-reverse{flex-direction:column-reverse!important}.flex-sm-wrap{flex-wrap:wrap!important}.flex-sm-nowrap{flex-wrap:nowrap!important}.flex-sm-wrap-reverse{flex-wrap:wrap-reverse!important}.flex-sm-fill{flex:1 1 auto!important}.flex-sm-grow-0{flex-grow:0!important}.flex-sm-grow-1{flex-grow:1!important}.flex-sm-shrink-0{flex-shrink:0!important}.flex-sm-shrink-1{flex-shrink:1!important}.justify-content-sm-start{justify-content:flex-start!important}.justify-content-sm-end{justify-content:flex-end!important}.justify-content-sm-center{justify-content:center!important}.justify-content-sm-between{justify-content:space-between!important}.justify-content-sm-around{justify-content:space-around!important}.align-items-sm-start{align-items:flex-start!important}.align-items-sm-end{align-items:flex-end!important}.align-items-sm-center{align-items:center!important}.align-items-sm-baseline{align-items:baseline!important}.align-items-sm-stretch{align-items:stretch!important}.align-content-sm-start{align-content:flex-start!important}.align-content-sm-end{align-content:flex-end!important}.align-content-sm-center{align-content:center!important}.align-content-sm-between{align-content:space-between!important}.align-content-sm-around{align-content:space-around!important}.align-content-sm-stretch{align-content:stretch!important}.align-self-sm-auto{align-self:auto!important}.align-self-sm-start{align-self:flex-start!important}.align-self-sm-end{align-self:flex-end!important}.align-self-sm-center{align-self:center!important}.align-self-sm-baseline{align-self:baseline!important}.align-self-sm-stretch{align-self:stretch!important}}@media (min-width:768px){.flex-md-row{flex-direction:row!important}.flex-md-column{flex-direction:column!important}.flex-md-row-reverse{flex-direction:row-reverse!important}.flex-md-column-reverse{flex-direction:column-reverse!important}.flex-md-wrap{flex-wrap:wrap!important}.flex-md-nowrap{flex-wrap:nowrap!important}.flex-md-wrap-reverse{flex-wrap:wrap-reverse!important}.flex-md-fill{flex:1 1 auto!important}.flex-md-grow-0{flex-grow:0!important}.flex-md-grow-1{flex-grow:1!important}.flex-md-shrink-0{flex-shrink:0!important}.flex-md-shrink-1{flex-shrink:1!important}.justify-content-md-start{justify-content:flex-start!important}.justify-content-md-end{justify-content:flex-end!important}.justify-content-md-center{justify-content:center!important}.justify-content-md-between{justify-content:space-between!important}.justify-content-md-around{justify-content:space-around!important}.align-items-md-start{align-items:flex-start!important}.align-items-md-end{align-items:flex-end!important}.align-items-md-center{align-items:center!important}.align-items-md-baseline{align-items:baseline!important}.align-items-md-stretch{align-items:stretch!important}.align-content-md-start{align-content:flex-start!important}.align-content-md-end{align-content:flex-end!important}.align-content-md-center{align-content:center!important}.align-content-md-between{align-content:space-between!important}.align-content-md-around{align-content:space-around!important}.align-content-md-stretch{align-content:stretch!important}.align-self-md-auto{align-self:auto!important}.align-self-md-start{align-self:flex-start!important}.align-self-md-end{align-self:flex-end!important}.align-self-md-center{align-self:center!important}.align-self-md-baseline{align-self:baseline!important}.align-self-md-stretch{align-self:stretch!important}}@media (min-width:992px){.flex-lg-row{flex-direction:row!important}.flex-lg-column{flex-direction:column!important}.flex-lg-row-reverse{flex-direction:row-reverse!important}.flex-lg-column-reverse{flex-direction:column-reverse!important}.flex-lg-wrap{flex-wrap:wrap!important}.flex-lg-nowrap{flex-wrap:nowrap!important}.flex-lg-wrap-reverse{flex-wrap:wrap-reverse!important}.flex-lg-fill{flex:1 1 auto!important}.flex-lg-grow-0{flex-grow:0!important}.flex-lg-grow-1{flex-grow:1!important}.flex-lg-shrink-0{flex-shrink:0!important}.flex-lg-shrink-1{flex-shrink:1!important}.justify-content-lg-start{justify-content:flex-start!important}.justify-content-lg-end{justify-content:flex-end!important}.justify-content-lg-center{justify-content:center!important}.justify-content-lg-between{justify-content:space-between!important}.justify-content-lg-around{justify-content:space-around!important}.align-items-lg-start{align-items:flex-start!important}.align-items-lg-end{align-items:flex-end!important}.align-items-lg-center{align-items:center!important}.align-items-lg-baseline{align-items:baseline!important}.align-items-lg-stretch{align-items:stretch!important}.align-content-lg-start{align-content:flex-start!important}.align-content-lg-end{align-content:flex-end!important}.align-content-lg-center{align-content:center!important}.align-content-lg-between{align-content:space-between!important}.align-content-lg-around{align-content:space-around!important}.align-content-lg-stretch{align-content:stretch!important}.align-self-lg-auto{align-self:auto!important}.align-self-lg-start{align-self:flex-start!important}.align-self-lg-end{align-self:flex-end!important}.align-self-lg-center{align-self:center!important}.align-self-lg-baseline{align-self:baseline!important}.align-self-lg-stretch{align-self:stretch!important}}@media (min-width:1200px){.flex-xl-row{flex-direction:row!important}.flex-xl-column{flex-direction:column!important}.flex-xl-row-reverse{flex-direction:row-reverse!important}.flex-xl-column-reverse{flex-direction:column-reverse!important}.flex-xl-wrap{flex-wrap:wrap!important}.flex-xl-nowrap{flex-wrap:nowrap!important}.flex-xl-wrap-reverse{flex-wrap:wrap-reverse!important}.flex-xl-fill{flex:1 1 auto!important}.flex-xl-grow-0{flex-grow:0!important}.flex-xl-grow-1{flex-grow:1!important}.flex-xl-shrink-0{flex-shrink:0!important}.flex-xl-shrink-1{flex-shrink:1!important}.justify-content-xl-start{justify-content:flex-start!important}.justify-content-xl-end{justify-content:flex-end!important}.justify-content-xl-center{justify-content:center!important}.justify-content-xl-between{justify-content:space-between!important}.justify-content-xl-around{justify-content:space-around!important}.align-items-xl-start{align-items:flex-start!important}.align-items-xl-end{align-items:flex-end!important}.align-items-xl-center{align-items:center!important}.align-items-xl-baseline{align-items:baseline!important}.align-items-xl-stretch{align-items:stretch!important}.align-content-xl-start{align-content:flex-start!important}.align-content-xl-end{align-content:flex-end!important}.align-content-xl-center{align-content:center!important}.align-content-xl-between{align-content:space-between!important}.align-content-xl-around{align-content:space-around!important}.align-content-xl-stretch{align-content:stretch!important}.align-self-xl-auto{align-self:auto!important}.align-self-xl-start{align-self:flex-start!important}.align-self-xl-end{align-self:flex-end!important}.align-self-xl-center{align-self:center!important}.align-self-xl-baseline{align-self:baseline!important}.align-self-xl-stretch{align-self:stretch!important}}.float-left{float:left!important}.float-right{float:right!important}.float-none{float:none!important}@media (min-width:576px){.float-sm-left{float:left!important}.float-sm-right{float:right!important}.float-sm-none{float:none!important}}@media (min-width:768px){.float-md-left{float:left!important}.float-md-right{float:right!important}.float-md-none{float:none!important}}@media (min-width:992px){.float-lg-left{float:left!important}.float-lg-right{float:right!important}.float-lg-none{float:none!important}}@media (min-width:1200px){.float-xl-left{float:left!important}.float-xl-right{float:right!important}.float-xl-none{float:none!important}}.position-static{position:static!important}.position-relative{position:relative!important}.position-absolute{position:absolute!important}.position-fixed{position:fixed!important}.position-sticky{position:sticky!important}.fixed-top{top:0}.fixed-bottom,.fixed-top{position:fixed;right:0;left:0;z-index:1030}.fixed-bottom{bottom:0}@supports (position:sticky){.sticky-top{position:sticky;top:0;z-index:1020}}.sr-only{position:absolute;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;overflow:visible;clip:auto;white-space:normal}.shadow-sm{box-shadow:0 .125rem .25rem rgba(34,42,66,.075)!important}.shadow{box-shadow:0 1px 20px 0 rgba(0,0,0,.1)!important}.shadow-lg{box-shadow:0 1rem 3rem rgba(34,42,66,.175)!important}.shadow-none{box-shadow:none!important}.w-25{width:25%!important}.w-50{width:50%!important}.w-75{width:75%!important}.w-100{width:100%!important}.w-auto{width:auto!important}.h-25{height:25%!important}.h-50{height:50%!important}.h-75{height:75%!important}.h-100{height:100%!important}.h-auto{height:auto!important}.mw-100{max-width:100%!important}.mh-100{max-height:100%!important}.m-0{margin:0!important}.mt-0,.my-0{margin-top:0!important}.mr-0,.mx-0{margin-right:0!important}.mb-0,.my-0{margin-bottom:0!important}.ml-0,.mx-0{margin-left:0!important}.m-1{margin:.25rem!important}.mt-1,.my-1{margin-top:.25rem!important}.mr-1,.mx-1{margin-right:.25rem!important}.mb-1,.my-1{margin-bottom:.25rem!important}.ml-1,.mx-1{margin-left:.25rem!important}.m-2{margin:.5rem!important}.mt-2,.my-2{margin-top:.5rem!important}.mr-2,.mx-2{margin-right:.5rem!important}.mb-2,.my-2{margin-bottom:.5rem!important}.ml-2,.mx-2{margin-left:.5rem!important}.m-3{margin:1rem!important}.mt-3,.my-3{margin-top:1rem!important}.mr-3,.mx-3{margin-right:1rem!important}.mb-3,.my-3{margin-bottom:1rem!important}.ml-3,.mx-3{margin-left:1rem!important}.m-4{margin:1.5rem!important}.mt-4,.my-4{margin-top:1.5rem!important}.mr-4,.mx-4{margin-right:1.5rem!important}.mb-4,.my-4{margin-bottom:1.5rem!important}.ml-4,.mx-4{margin-left:1.5rem!important}.m-5{margin:3rem!important}.mt-5,.my-5{margin-top:3rem!important}.mr-5,.mx-5{margin-right:3rem!important}.mb-5,.my-5{margin-bottom:3rem!important}.ml-5,.mx-5{margin-left:3rem!important}.m-sm{margin:2rem!important}.mt-sm,.my-sm{margin-top:2rem!important}.mr-sm,.mx-sm{margin-right:2rem!important}.mb-sm,.my-sm{margin-bottom:2rem!important}.ml-sm,.mx-sm{margin-left:2rem!important}.m-md{margin:4rem!important}.mt-md,.my-md{margin-top:4rem!important}.mr-md,.mx-md{margin-right:4rem!important}.mb-md,.my-md{margin-bottom:4rem!important}.ml-md,.mx-md{margin-left:4rem!important}.m-lg{margin:6rem!important}.mt-lg,.my-lg{margin-top:6rem!important}.mr-lg,.mx-lg{margin-right:6rem!important}.mb-lg,.my-lg{margin-bottom:6rem!important}.ml-lg,.mx-lg{margin-left:6rem!important}.m-xl{margin:8rem!important}.mt-xl,.my-xl{margin-top:8rem!important}.mr-xl,.mx-xl{margin-right:8rem!important}.mb-xl,.my-xl{margin-bottom:8rem!important}.ml-xl,.mx-xl{margin-left:8rem!important}.p-0{padding:0!important}.pt-0,.py-0{padding-top:0!important}.pr-0,.px-0{padding-right:0!important}.pb-0,.py-0{padding-bottom:0!important}.pl-0,.px-0{padding-left:0!important}.p-1{padding:.25rem!important}.pt-1,.py-1{padding-top:.25rem!important}.pr-1,.px-1{padding-right:.25rem!important}.pb-1,.py-1{padding-bottom:.25rem!important}.pl-1,.px-1{padding-left:.25rem!important}.p-2{padding:.5rem!important}.pt-2,.py-2{padding-top:.5rem!important}.pr-2,.px-2{padding-right:.5rem!important}.pb-2,.py-2{padding-bottom:.5rem!important}.pl-2,.px-2{padding-left:.5rem!important}.p-3{padding:1rem!important}.pt-3,.py-3{padding-top:1rem!important}.pr-3,.px-3{padding-right:1rem!important}.pb-3,.py-3{padding-bottom:1rem!important}.pl-3,.px-3{padding-left:1rem!important}.p-4{padding:1.5rem!important}.pt-4,.py-4{padding-top:1.5rem!important}.pr-4,.px-4{padding-right:1.5rem!important}.pb-4,.py-4{padding-bottom:1.5rem!important}.pl-4,.px-4{padding-left:1.5rem!important}.p-5{padding:3rem!important}.pt-5,.py-5{padding-top:3rem!important}.pr-5,.px-5{padding-right:3rem!important}.pb-5,.py-5{padding-bottom:3rem!important}.pl-5,.px-5{padding-left:3rem!important}.p-sm{padding:2rem!important}.pt-sm,.py-sm{padding-top:2rem!important}.pr-sm,.px-sm{padding-right:2rem!important}.pb-sm,.py-sm{padding-bottom:2rem!important}.pl-sm,.px-sm{padding-left:2rem!important}.p-md{padding:4rem!important}.pt-md,.py-md{padding-top:4rem!important}.pr-md,.px-md{padding-right:4rem!important}.pb-md,.py-md{padding-bottom:4rem!important}.pl-md,.px-md{padding-left:4rem!important}.p-lg{padding:6rem!important}.pt-lg,.py-lg{padding-top:6rem!important}.pr-lg,.px-lg{padding-right:6rem!important}.pb-lg,.py-lg{padding-bottom:6rem!important}.pl-lg,.px-lg{padding-left:6rem!important}.p-xl{padding:8rem!important}.pt-xl,.py-xl{padding-top:8rem!important}.pr-xl,.px-xl{padding-right:8rem!important}.pb-xl,.py-xl{padding-bottom:8rem!important}.pl-xl,.px-xl{padding-left:8rem!important}.m-auto{margin:auto!important}.mt-auto,.my-auto{margin-top:auto!important}.mr-auto,.mx-auto{margin-right:auto!important}.mb-auto,.my-auto{margin-bottom:auto!important}.ml-auto,.mx-auto{margin-left:auto!important}@media (min-width:576px){.m-sm-0{margin:0!important}.mt-sm-0,.my-sm-0{margin-top:0!important}.mr-sm-0,.mx-sm-0{margin-right:0!important}.mb-sm-0,.my-sm-0{margin-bottom:0!important}.ml-sm-0,.mx-sm-0{margin-left:0!important}.m-sm-1{margin:.25rem!important}.mt-sm-1,.my-sm-1{margin-top:.25rem!important}.mr-sm-1,.mx-sm-1{margin-right:.25rem!important}.mb-sm-1,.my-sm-1{margin-bottom:.25rem!important}.ml-sm-1,.mx-sm-1{margin-left:.25rem!important}.m-sm-2{margin:.5rem!important}.mt-sm-2,.my-sm-2{margin-top:.5rem!important}.mr-sm-2,.mx-sm-2{margin-right:.5rem!important}.mb-sm-2,.my-sm-2{margin-bottom:.5rem!important}.ml-sm-2,.mx-sm-2{margin-left:.5rem!important}.m-sm-3{margin:1rem!important}.mt-sm-3,.my-sm-3{margin-top:1rem!important}.mr-sm-3,.mx-sm-3{margin-right:1rem!important}.mb-sm-3,.my-sm-3{margin-bottom:1rem!important}.ml-sm-3,.mx-sm-3{margin-left:1rem!important}.m-sm-4{margin:1.5rem!important}.mt-sm-4,.my-sm-4{margin-top:1.5rem!important}.mr-sm-4,.mx-sm-4{margin-right:1.5rem!important}.mb-sm-4,.my-sm-4{margin-bottom:1.5rem!important}.ml-sm-4,.mx-sm-4{margin-left:1.5rem!important}.m-sm-5{margin:3rem!important}.mt-sm-5,.my-sm-5{margin-top:3rem!important}.mr-sm-5,.mx-sm-5{margin-right:3rem!important}.mb-sm-5,.my-sm-5{margin-bottom:3rem!important}.ml-sm-5,.mx-sm-5{margin-left:3rem!important}.m-sm-sm{margin:2rem!important}.mt-sm-sm,.my-sm-sm{margin-top:2rem!important}.mr-sm-sm,.mx-sm-sm{margin-right:2rem!important}.mb-sm-sm,.my-sm-sm{margin-bottom:2rem!important}.ml-sm-sm,.mx-sm-sm{margin-left:2rem!important}.m-sm-md{margin:4rem!important}.mt-sm-md,.my-sm-md{margin-top:4rem!important}.mr-sm-md,.mx-sm-md{margin-right:4rem!important}.mb-sm-md,.my-sm-md{margin-bottom:4rem!important}.ml-sm-md,.mx-sm-md{margin-left:4rem!important}.m-sm-lg{margin:6rem!important}.mt-sm-lg,.my-sm-lg{margin-top:6rem!important}.mr-sm-lg,.mx-sm-lg{margin-right:6rem!important}.mb-sm-lg,.my-sm-lg{margin-bottom:6rem!important}.ml-sm-lg,.mx-sm-lg{margin-left:6rem!important}.m-sm-xl{margin:8rem!important}.mt-sm-xl,.my-sm-xl{margin-top:8rem!important}.mr-sm-xl,.mx-sm-xl{margin-right:8rem!important}.mb-sm-xl,.my-sm-xl{margin-bottom:8rem!important}.ml-sm-xl,.mx-sm-xl{margin-left:8rem!important}.p-sm-0{padding:0!important}.pt-sm-0,.py-sm-0{padding-top:0!important}.pr-sm-0,.px-sm-0{padding-right:0!important}.pb-sm-0,.py-sm-0{padding-bottom:0!important}.pl-sm-0,.px-sm-0{padding-left:0!important}.p-sm-1{padding:.25rem!important}.pt-sm-1,.py-sm-1{padding-top:.25rem!important}.pr-sm-1,.px-sm-1{padding-right:.25rem!important}.pb-sm-1,.py-sm-1{padding-bottom:.25rem!important}.pl-sm-1,.px-sm-1{padding-left:.25rem!important}.p-sm-2{padding:.5rem!important}.pt-sm-2,.py-sm-2{padding-top:.5rem!important}.pr-sm-2,.px-sm-2{padding-right:.5rem!important}.pb-sm-2,.py-sm-2{padding-bottom:.5rem!important}.pl-sm-2,.px-sm-2{padding-left:.5rem!important}.p-sm-3{padding:1rem!important}.pt-sm-3,.py-sm-3{padding-top:1rem!important}.pr-sm-3,.px-sm-3{padding-right:1rem!important}.pb-sm-3,.py-sm-3{padding-bottom:1rem!important}.pl-sm-3,.px-sm-3{padding-left:1rem!important}.p-sm-4{padding:1.5rem!important}.pt-sm-4,.py-sm-4{padding-top:1.5rem!important}.pr-sm-4,.px-sm-4{padding-right:1.5rem!important}.pb-sm-4,.py-sm-4{padding-bottom:1.5rem!important}.pl-sm-4,.px-sm-4{padding-left:1.5rem!important}.p-sm-5{padding:3rem!important}.pt-sm-5,.py-sm-5{padding-top:3rem!important}.pr-sm-5,.px-sm-5{padding-right:3rem!important}.pb-sm-5,.py-sm-5{padding-bottom:3rem!important}.pl-sm-5,.px-sm-5{padding-left:3rem!important}.p-sm-sm{padding:2rem!important}.pt-sm-sm,.py-sm-sm{padding-top:2rem!important}.pr-sm-sm,.px-sm-sm{padding-right:2rem!important}.pb-sm-sm,.py-sm-sm{padding-bottom:2rem!important}.pl-sm-sm,.px-sm-sm{padding-left:2rem!important}.p-sm-md{padding:4rem!important}.pt-sm-md,.py-sm-md{padding-top:4rem!important}.pr-sm-md,.px-sm-md{padding-right:4rem!important}.pb-sm-md,.py-sm-md{padding-bottom:4rem!important}.pl-sm-md,.px-sm-md{padding-left:4rem!important}.p-sm-lg{padding:6rem!important}.pt-sm-lg,.py-sm-lg{padding-top:6rem!important}.pr-sm-lg,.px-sm-lg{padding-right:6rem!important}.pb-sm-lg,.py-sm-lg{padding-bottom:6rem!important}.pl-sm-lg,.px-sm-lg{padding-left:6rem!important}.p-sm-xl{padding:8rem!important}.pt-sm-xl,.py-sm-xl{padding-top:8rem!important}.pr-sm-xl,.px-sm-xl{padding-right:8rem!important}.pb-sm-xl,.py-sm-xl{padding-bottom:8rem!important}.pl-sm-xl,.px-sm-xl{padding-left:8rem!important}.m-sm-auto{margin:auto!important}.mt-sm-auto,.my-sm-auto{margin-top:auto!important}.mr-sm-auto,.mx-sm-auto{margin-right:auto!important}.mb-sm-auto,.my-sm-auto{margin-bottom:auto!important}.ml-sm-auto,.mx-sm-auto{margin-left:auto!important}}@media (min-width:768px){.m-md-0{margin:0!important}.mt-md-0,.my-md-0{margin-top:0!important}.mr-md-0,.mx-md-0{margin-right:0!important}.mb-md-0,.my-md-0{margin-bottom:0!important}.ml-md-0,.mx-md-0{margin-left:0!important}.m-md-1{margin:.25rem!important}.mt-md-1,.my-md-1{margin-top:.25rem!important}.mr-md-1,.mx-md-1{margin-right:.25rem!important}.mb-md-1,.my-md-1{margin-bottom:.25rem!important}.ml-md-1,.mx-md-1{margin-left:.25rem!important}.m-md-2{margin:.5rem!important}.mt-md-2,.my-md-2{margin-top:.5rem!important}.mr-md-2,.mx-md-2{margin-right:.5rem!important}.mb-md-2,.my-md-2{margin-bottom:.5rem!important}.ml-md-2,.mx-md-2{margin-left:.5rem!important}.m-md-3{margin:1rem!important}.mt-md-3,.my-md-3{margin-top:1rem!important}.mr-md-3,.mx-md-3{margin-right:1rem!important}.mb-md-3,.my-md-3{margin-bottom:1rem!important}.ml-md-3,.mx-md-3{margin-left:1rem!important}.m-md-4{margin:1.5rem!important}.mt-md-4,.my-md-4{margin-top:1.5rem!important}.mr-md-4,.mx-md-4{margin-right:1.5rem!important}.mb-md-4,.my-md-4{margin-bottom:1.5rem!important}.ml-md-4,.mx-md-4{margin-left:1.5rem!important}.m-md-5{margin:3rem!important}.mt-md-5,.my-md-5{margin-top:3rem!important}.mr-md-5,.mx-md-5{margin-right:3rem!important}.mb-md-5,.my-md-5{margin-bottom:3rem!important}.ml-md-5,.mx-md-5{margin-left:3rem!important}.m-md-sm{margin:2rem!important}.mt-md-sm,.my-md-sm{margin-top:2rem!important}.mr-md-sm,.mx-md-sm{margin-right:2rem!important}.mb-md-sm,.my-md-sm{margin-bottom:2rem!important}.ml-md-sm,.mx-md-sm{margin-left:2rem!important}.m-md-md{margin:4rem!important}.mt-md-md,.my-md-md{margin-top:4rem!important}.mr-md-md,.mx-md-md{margin-right:4rem!important}.mb-md-md,.my-md-md{margin-bottom:4rem!important}.ml-md-md,.mx-md-md{margin-left:4rem!important}.m-md-lg{margin:6rem!important}.mt-md-lg,.my-md-lg{margin-top:6rem!important}.mr-md-lg,.mx-md-lg{margin-right:6rem!important}.mb-md-lg,.my-md-lg{margin-bottom:6rem!important}.ml-md-lg,.mx-md-lg{margin-left:6rem!important}.m-md-xl{margin:8rem!important}.mt-md-xl,.my-md-xl{margin-top:8rem!important}.mr-md-xl,.mx-md-xl{margin-right:8rem!important}.mb-md-xl,.my-md-xl{margin-bottom:8rem!important}.ml-md-xl,.mx-md-xl{margin-left:8rem!important}.p-md-0{padding:0!important}.pt-md-0,.py-md-0{padding-top:0!important}.pr-md-0,.px-md-0{padding-right:0!important}.pb-md-0,.py-md-0{padding-bottom:0!important}.pl-md-0,.px-md-0{padding-left:0!important}.p-md-1{padding:.25rem!important}.pt-md-1,.py-md-1{padding-top:.25rem!important}.pr-md-1,.px-md-1{padding-right:.25rem!important}.pb-md-1,.py-md-1{padding-bottom:.25rem!important}.pl-md-1,.px-md-1{padding-left:.25rem!important}.p-md-2{padding:.5rem!important}.pt-md-2,.py-md-2{padding-top:.5rem!important}.pr-md-2,.px-md-2{padding-right:.5rem!important}.pb-md-2,.py-md-2{padding-bottom:.5rem!important}.pl-md-2,.px-md-2{padding-left:.5rem!important}.p-md-3{padding:1rem!important}.pt-md-3,.py-md-3{padding-top:1rem!important}.pr-md-3,.px-md-3{padding-right:1rem!important}.pb-md-3,.py-md-3{padding-bottom:1rem!important}.pl-md-3,.px-md-3{padding-left:1rem!important}.p-md-4{padding:1.5rem!important}.pt-md-4,.py-md-4{padding-top:1.5rem!important}.pr-md-4,.px-md-4{padding-right:1.5rem!important}.pb-md-4,.py-md-4{padding-bottom:1.5rem!important}.pl-md-4,.px-md-4{padding-left:1.5rem!important}.p-md-5{padding:3rem!important}.pt-md-5,.py-md-5{padding-top:3rem!important}.pr-md-5,.px-md-5{padding-right:3rem!important}.pb-md-5,.py-md-5{padding-bottom:3rem!important}.pl-md-5,.px-md-5{padding-left:3rem!important}.p-md-sm{padding:2rem!important}.pt-md-sm,.py-md-sm{padding-top:2rem!important}.pr-md-sm,.px-md-sm{padding-right:2rem!important}.pb-md-sm,.py-md-sm{padding-bottom:2rem!important}.pl-md-sm,.px-md-sm{padding-left:2rem!important}.p-md-md{padding:4rem!important}.pt-md-md,.py-md-md{padding-top:4rem!important}.pr-md-md,.px-md-md{padding-right:4rem!important}.pb-md-md,.py-md-md{padding-bottom:4rem!important}.pl-md-md,.px-md-md{padding-left:4rem!important}.p-md-lg{padding:6rem!important}.pt-md-lg,.py-md-lg{padding-top:6rem!important}.pr-md-lg,.px-md-lg{padding-right:6rem!important}.pb-md-lg,.py-md-lg{padding-bottom:6rem!important}.pl-md-lg,.px-md-lg{padding-left:6rem!important}.p-md-xl{padding:8rem!important}.pt-md-xl,.py-md-xl{padding-top:8rem!important}.pr-md-xl,.px-md-xl{padding-right:8rem!important}.pb-md-xl,.py-md-xl{padding-bottom:8rem!important}.pl-md-xl,.px-md-xl{padding-left:8rem!important}.m-md-auto{margin:auto!important}.mt-md-auto,.my-md-auto{margin-top:auto!important}.mr-md-auto,.mx-md-auto{margin-right:auto!important}.mb-md-auto,.my-md-auto{margin-bottom:auto!important}.ml-md-auto,.mx-md-auto{margin-left:auto!important}}@media (min-width:992px){.m-lg-0{margin:0!important}.mt-lg-0,.my-lg-0{margin-top:0!important}.mr-lg-0,.mx-lg-0{margin-right:0!important}.mb-lg-0,.my-lg-0{margin-bottom:0!important}.ml-lg-0,.mx-lg-0{margin-left:0!important}.m-lg-1{margin:.25rem!important}.mt-lg-1,.my-lg-1{margin-top:.25rem!important}.mr-lg-1,.mx-lg-1{margin-right:.25rem!important}.mb-lg-1,.my-lg-1{margin-bottom:.25rem!important}.ml-lg-1,.mx-lg-1{margin-left:.25rem!important}.m-lg-2{margin:.5rem!important}.mt-lg-2,.my-lg-2{margin-top:.5rem!important}.mr-lg-2,.mx-lg-2{margin-right:.5rem!important}.mb-lg-2,.my-lg-2{margin-bottom:.5rem!important}.ml-lg-2,.mx-lg-2{margin-left:.5rem!important}.m-lg-3{margin:1rem!important}.mt-lg-3,.my-lg-3{margin-top:1rem!important}.mr-lg-3,.mx-lg-3{margin-right:1rem!important}.mb-lg-3,.my-lg-3{margin-bottom:1rem!important}.ml-lg-3,.mx-lg-3{margin-left:1rem!important}.m-lg-4{margin:1.5rem!important}.mt-lg-4,.my-lg-4{margin-top:1.5rem!important}.mr-lg-4,.mx-lg-4{margin-right:1.5rem!important}.mb-lg-4,.my-lg-4{margin-bottom:1.5rem!important}.ml-lg-4,.mx-lg-4{margin-left:1.5rem!important}.m-lg-5{margin:3rem!important}.mt-lg-5,.my-lg-5{margin-top:3rem!important}.mr-lg-5,.mx-lg-5{margin-right:3rem!important}.mb-lg-5,.my-lg-5{margin-bottom:3rem!important}.ml-lg-5,.mx-lg-5{margin-left:3rem!important}.m-lg-sm{margin:2rem!important}.mt-lg-sm,.my-lg-sm{margin-top:2rem!important}.mr-lg-sm,.mx-lg-sm{margin-right:2rem!important}.mb-lg-sm,.my-lg-sm{margin-bottom:2rem!important}.ml-lg-sm,.mx-lg-sm{margin-left:2rem!important}.m-lg-md{margin:4rem!important}.mt-lg-md,.my-lg-md{margin-top:4rem!important}.mr-lg-md,.mx-lg-md{margin-right:4rem!important}.mb-lg-md,.my-lg-md{margin-bottom:4rem!important}.ml-lg-md,.mx-lg-md{margin-left:4rem!important}.m-lg-lg{margin:6rem!important}.mt-lg-lg,.my-lg-lg{margin-top:6rem!important}.mr-lg-lg,.mx-lg-lg{margin-right:6rem!important}.mb-lg-lg,.my-lg-lg{margin-bottom:6rem!important}.ml-lg-lg,.mx-lg-lg{margin-left:6rem!important}.m-lg-xl{margin:8rem!important}.mt-lg-xl,.my-lg-xl{margin-top:8rem!important}.mr-lg-xl,.mx-lg-xl{margin-right:8rem!important}.mb-lg-xl,.my-lg-xl{margin-bottom:8rem!important}.ml-lg-xl,.mx-lg-xl{margin-left:8rem!important}.p-lg-0{padding:0!important}.pt-lg-0,.py-lg-0{padding-top:0!important}.pr-lg-0,.px-lg-0{padding-right:0!important}.pb-lg-0,.py-lg-0{padding-bottom:0!important}.pl-lg-0,.px-lg-0{padding-left:0!important}.p-lg-1{padding:.25rem!important}.pt-lg-1,.py-lg-1{padding-top:.25rem!important}.pr-lg-1,.px-lg-1{padding-right:.25rem!important}.pb-lg-1,.py-lg-1{padding-bottom:.25rem!important}.pl-lg-1,.px-lg-1{padding-left:.25rem!important}.p-lg-2{padding:.5rem!important}.pt-lg-2,.py-lg-2{padding-top:.5rem!important}.pr-lg-2,.px-lg-2{padding-right:.5rem!important}.pb-lg-2,.py-lg-2{padding-bottom:.5rem!important}.pl-lg-2,.px-lg-2{padding-left:.5rem!important}.p-lg-3{padding:1rem!important}.pt-lg-3,.py-lg-3{padding-top:1rem!important}.pr-lg-3,.px-lg-3{padding-right:1rem!important}.pb-lg-3,.py-lg-3{padding-bottom:1rem!important}.pl-lg-3,.px-lg-3{padding-left:1rem!important}.p-lg-4{padding:1.5rem!important}.pt-lg-4,.py-lg-4{padding-top:1.5rem!important}.pr-lg-4,.px-lg-4{padding-right:1.5rem!important}.pb-lg-4,.py-lg-4{padding-bottom:1.5rem!important}.pl-lg-4,.px-lg-4{padding-left:1.5rem!important}.p-lg-5{padding:3rem!important}.pt-lg-5,.py-lg-5{padding-top:3rem!important}.pr-lg-5,.px-lg-5{padding-right:3rem!important}.pb-lg-5,.py-lg-5{padding-bottom:3rem!important}.pl-lg-5,.px-lg-5{padding-left:3rem!important}.p-lg-sm{padding:2rem!important}.pt-lg-sm,.py-lg-sm{padding-top:2rem!important}.pr-lg-sm,.px-lg-sm{padding-right:2rem!important}.pb-lg-sm,.py-lg-sm{padding-bottom:2rem!important}.pl-lg-sm,.px-lg-sm{padding-left:2rem!important}.p-lg-md{padding:4rem!important}.pt-lg-md,.py-lg-md{padding-top:4rem!important}.pr-lg-md,.px-lg-md{padding-right:4rem!important}.pb-lg-md,.py-lg-md{padding-bottom:4rem!important}.pl-lg-md,.px-lg-md{padding-left:4rem!important}.p-lg-lg{padding:6rem!important}.pt-lg-lg,.py-lg-lg{padding-top:6rem!important}.pr-lg-lg,.px-lg-lg{padding-right:6rem!important}.pb-lg-lg,.py-lg-lg{padding-bottom:6rem!important}.pl-lg-lg,.px-lg-lg{padding-left:6rem!important}.p-lg-xl{padding:8rem!important}.pt-lg-xl,.py-lg-xl{padding-top:8rem!important}.pr-lg-xl,.px-lg-xl{padding-right:8rem!important}.pb-lg-xl,.py-lg-xl{padding-bottom:8rem!important}.pl-lg-xl,.px-lg-xl{padding-left:8rem!important}.m-lg-auto{margin:auto!important}.mt-lg-auto,.my-lg-auto{margin-top:auto!important}.mr-lg-auto,.mx-lg-auto{margin-right:auto!important}.mb-lg-auto,.my-lg-auto{margin-bottom:auto!important}.ml-lg-auto,.mx-lg-auto{margin-left:auto!important}}@media (min-width:1200px){.m-xl-0{margin:0!important}.mt-xl-0,.my-xl-0{margin-top:0!important}.mr-xl-0,.mx-xl-0{margin-right:0!important}.mb-xl-0,.my-xl-0{margin-bottom:0!important}.ml-xl-0,.mx-xl-0{margin-left:0!important}.m-xl-1{margin:.25rem!important}.mt-xl-1,.my-xl-1{margin-top:.25rem!important}.mr-xl-1,.mx-xl-1{margin-right:.25rem!important}.mb-xl-1,.my-xl-1{margin-bottom:.25rem!important}.ml-xl-1,.mx-xl-1{margin-left:.25rem!important}.m-xl-2{margin:.5rem!important}.mt-xl-2,.my-xl-2{margin-top:.5rem!important}.mr-xl-2,.mx-xl-2{margin-right:.5rem!important}.mb-xl-2,.my-xl-2{margin-bottom:.5rem!important}.ml-xl-2,.mx-xl-2{margin-left:.5rem!important}.m-xl-3{margin:1rem!important}.mt-xl-3,.my-xl-3{margin-top:1rem!important}.mr-xl-3,.mx-xl-3{margin-right:1rem!important}.mb-xl-3,.my-xl-3{margin-bottom:1rem!important}.ml-xl-3,.mx-xl-3{margin-left:1rem!important}.m-xl-4{margin:1.5rem!important}.mt-xl-4,.my-xl-4{margin-top:1.5rem!important}.mr-xl-4,.mx-xl-4{margin-right:1.5rem!important}.mb-xl-4,.my-xl-4{margin-bottom:1.5rem!important}.ml-xl-4,.mx-xl-4{margin-left:1.5rem!important}.m-xl-5{margin:3rem!important}.mt-xl-5,.my-xl-5{margin-top:3rem!important}.mr-xl-5,.mx-xl-5{margin-right:3rem!important}.mb-xl-5,.my-xl-5{margin-bottom:3rem!important}.ml-xl-5,.mx-xl-5{margin-left:3rem!important}.m-xl-sm{margin:2rem!important}.mt-xl-sm,.my-xl-sm{margin-top:2rem!important}.mr-xl-sm,.mx-xl-sm{margin-right:2rem!important}.mb-xl-sm,.my-xl-sm{margin-bottom:2rem!important}.ml-xl-sm,.mx-xl-sm{margin-left:2rem!important}.m-xl-md{margin:4rem!important}.mt-xl-md,.my-xl-md{margin-top:4rem!important}.mr-xl-md,.mx-xl-md{margin-right:4rem!important}.mb-xl-md,.my-xl-md{margin-bottom:4rem!important}.ml-xl-md,.mx-xl-md{margin-left:4rem!important}.m-xl-lg{margin:6rem!important}.mt-xl-lg,.my-xl-lg{margin-top:6rem!important}.mr-xl-lg,.mx-xl-lg{margin-right:6rem!important}.mb-xl-lg,.my-xl-lg{margin-bottom:6rem!important}.ml-xl-lg,.mx-xl-lg{margin-left:6rem!important}.m-xl-xl{margin:8rem!important}.mt-xl-xl,.my-xl-xl{margin-top:8rem!important}.mr-xl-xl,.mx-xl-xl{margin-right:8rem!important}.mb-xl-xl,.my-xl-xl{margin-bottom:8rem!important}.ml-xl-xl,.mx-xl-xl{margin-left:8rem!important}.p-xl-0{padding:0!important}.pt-xl-0,.py-xl-0{padding-top:0!important}.pr-xl-0,.px-xl-0{padding-right:0!important}.pb-xl-0,.py-xl-0{padding-bottom:0!important}.pl-xl-0,.px-xl-0{padding-left:0!important}.p-xl-1{padding:.25rem!important}.pt-xl-1,.py-xl-1{padding-top:.25rem!important}.pr-xl-1,.px-xl-1{padding-right:.25rem!important}.pb-xl-1,.py-xl-1{padding-bottom:.25rem!important}.pl-xl-1,.px-xl-1{padding-left:.25rem!important}.p-xl-2{padding:.5rem!important}.pt-xl-2,.py-xl-2{padding-top:.5rem!important}.pr-xl-2,.px-xl-2{padding-right:.5rem!important}.pb-xl-2,.py-xl-2{padding-bottom:.5rem!important}.pl-xl-2,.px-xl-2{padding-left:.5rem!important}.p-xl-3{padding:1rem!important}.pt-xl-3,.py-xl-3{padding-top:1rem!important}.pr-xl-3,.px-xl-3{padding-right:1rem!important}.pb-xl-3,.py-xl-3{padding-bottom:1rem!important}.pl-xl-3,.px-xl-3{padding-left:1rem!important}.p-xl-4{padding:1.5rem!important}.pt-xl-4,.py-xl-4{padding-top:1.5rem!important}.pr-xl-4,.px-xl-4{padding-right:1.5rem!important}.pb-xl-4,.py-xl-4{padding-bottom:1.5rem!important}.pl-xl-4,.px-xl-4{padding-left:1.5rem!important}.p-xl-5{padding:3rem!important}.pt-xl-5,.py-xl-5{padding-top:3rem!important}.pr-xl-5,.px-xl-5{padding-right:3rem!important}.pb-xl-5,.py-xl-5{padding-bottom:3rem!important}.pl-xl-5,.px-xl-5{padding-left:3rem!important}.p-xl-sm{padding:2rem!important}.pt-xl-sm,.py-xl-sm{padding-top:2rem!important}.pr-xl-sm,.px-xl-sm{padding-right:2rem!important}.pb-xl-sm,.py-xl-sm{padding-bottom:2rem!important}.pl-xl-sm,.px-xl-sm{padding-left:2rem!important}.p-xl-md{padding:4rem!important}.pt-xl-md,.py-xl-md{padding-top:4rem!important}.pr-xl-md,.px-xl-md{padding-right:4rem!important}.pb-xl-md,.py-xl-md{padding-bottom:4rem!important}.pl-xl-md,.px-xl-md{padding-left:4rem!important}.p-xl-lg{padding:6rem!important}.pt-xl-lg,.py-xl-lg{padding-top:6rem!important}.pr-xl-lg,.px-xl-lg{padding-right:6rem!important}.pb-xl-lg,.py-xl-lg{padding-bottom:6rem!important}.pl-xl-lg,.px-xl-lg{padding-left:6rem!important}.p-xl-xl{padding:8rem!important}.pt-xl-xl,.py-xl-xl{padding-top:8rem!important}.pr-xl-xl,.px-xl-xl{padding-right:8rem!important}.pb-xl-xl,.py-xl-xl{padding-bottom:8rem!important}.pl-xl-xl,.px-xl-xl{padding-left:8rem!important}.m-xl-auto{margin:auto!important}.mt-xl-auto,.my-xl-auto{margin-top:auto!important}.mr-xl-auto,.mx-xl-auto{margin-right:auto!important}.mb-xl-auto,.my-xl-auto{margin-bottom:auto!important}.ml-xl-auto,.mx-xl-auto{margin-left:auto!important}}.text-monospace{font-family:SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.text-justify{text-align:justify!important}.text-nowrap{white-space:nowrap!important}.text-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.text-left{text-align:left!important}.text-right{text-align:right!important}.text-center{text-align:center!important}@media (min-width:576px){.text-sm-left{text-align:left!important}.text-sm-right{text-align:right!important}.text-sm-center{text-align:center!important}}@media (min-width:768px){.text-md-left{text-align:left!important}.text-md-right{text-align:right!important}.text-md-center{text-align:center!important}}@media (min-width:992px){.text-lg-left{text-align:left!important}.text-lg-right{text-align:right!important}.text-lg-center{text-align:center!important}}@media (min-width:1200px){.text-xl-left{text-align:left!important}.text-xl-right{text-align:right!important}.text-xl-center{text-align:center!important}}.text-lowercase{text-transform:lowercase!important}.text-uppercase{text-transform:uppercase!important}.text-capitalize{text-transform:capitalize!important}.font-weight-light{font-weight:300!important}.font-weight-normal{font-weight:400!important}.font-weight-bold{font-weight:600!important}.font-italic{font-style:italic!important}.text-primary{color:#e14eca!important}a.text-primary:focus,a.text-primary:hover{color:#d725bb!important}.text-secondary{color:#f4f5f7!important}a.text-secondary:focus,a.text-secondary:hover{color:#d6dae2!important}.text-success{color:#00f2c3!important}a.text-success:focus,a.text-success:hover{color:#00bf9a!important}.text-info{color:#1d8cf8!important}a.text-info:focus,a.text-info:hover{color:#0772db!important}.text-warning{color:#ff8d72!important}a.text-warning:focus,a.text-warning:hover{color:#ff643f!important}.text-danger{color:#fd5d93!important}a.text-danger:focus,a.text-danger:hover{color:#fc2b71!important}.text-light{color:#adb5bd!important}a.text-light:focus,a.text-light:hover{color:#919ca6!important}.text-dark{color:#212529!important}a.text-dark:focus,a.text-dark:hover{color:#0a0c0d!important}.text-default{color:#344675!important}a.text-default:focus,a.text-default:hover{color:#243152!important}.text-white{color:#fff!important}a.text-white:focus,a.text-white:hover{color:#e6e6e6!important}.text-neutral{color:#fff!important}a.text-neutral:focus,a.text-neutral:hover{color:#e6e6e6!important}.text-darker,a.text-darker:focus,a.text-darker:hover{color:#000!important}.text-body{color:#525f7f!important}.text-muted{color:#6c757d!important}.text-black-50{color:rgba(34,42,66,.5)!important}.text-white-50{color:hsla(0,0%,100%,.5)!important}.text-hide{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}.visible{visibility:visible!important}.invisible{visibility:hidden!important}@media print{*,:after,:before{text-shadow:none!important;box-shadow:none!important}a:not(.btn){text-decoration:underline}abbr[title]:after{content:" (" attr(title) ")"}pre{white-space:pre-wrap!important}blockquote,pre{border:.0625rem solid #adb5bd;page-break-inside:avoid}thead{display:table-header-group}img,tr{page-break-inside:avoid}h2,h3,p{orphans:3;widows:3}h2,h3{page-break-after:avoid}@page{size:a3}.container,body{min-width:992px!important}.navbar{display:none}.badge{border:.0625rem solid #222a42}.table{border-collapse:collapse!important}.table td,.table th{background-color:#fff!important}.table-bordered td,.table-bordered th{border:1px solid #e3e3e3!important}.table-dark{color:inherit}.table-dark tbody+tbody,.table-dark td,.table-dark th,.table-dark thead th{border-color:#e3e3e3}.table .thead-dark th{color:inherit;border-color:#e3e3e3}}.alert{border:0}.alert,.alert .alert-link{color:#fff}.alert.alert-success{background-color:#00bf9a}.alert i.fa,.alert i.tim-icons{font-size:1rem}.alert .close{color:#fff;opacity:.9;text-shadow:none;line-height:0;outline:0}.alert span[data-notify=icon]{font-size:22px;display:block;left:19px;position:absolute;top:50%;margin-top:-11px}.alert button.close{position:absolute;right:15px;top:50%;margin-top:-13px;width:25px;height:25px;padding:3px}.alert .close~span{display:block;max-width:89%}.alert.alert-with-icon{padding-left:65px}.alert-dismissible .close{top:50%;right:1.25rem;padding:0;transform:translateY(-50%);color:hsla(0,0%,100%,.6);opacity:1}.alert-dismissible .close:focus,.alert-dismissible .close:hover{color:hsla(0,0%,100%,.9);opacity:1!important}@media (max-width:575.98px){.alert-dismissible .close{top:1rem;right:.5rem}}.alert-dismissible .close>span:not(.sr-only){font-size:1.5rem;background-color:transparent;color:hsla(0,0%,100%,.6)}.alert-dismissible .close:focus>span:not(.sr-only),.alert-dismissible .close:hover>span:not(.sr-only){background-color:transparent;color:hsla(0,0%,100%,.9)}.btn,.navbar .navbar-nav>a.btn{border-width:2px;border:none;position:relative;overflow:hidden;margin:4px 1px;border-radius:.4285rem;cursor:pointer;background:#344675;background-image:linear-gradient(to bottom left,#344675,#263148,#344675);background-size:210% 210%;background-position:100% 0;background-color:#344675;transition:all .15s ease;box-shadow:none;color:#fff}.btn.animation-on-hover:hover,.navbar .navbar-nav>a.btn.animation-on-hover:hover{background-position:0 100%;transition:.3s ease-in-out}.btn.active,.btn.active:focus,.btn.active:hover,.btn:active,.btn:active:focus,.btn:active:hover,.btn:focus,.btn:hover,.navbar .navbar-nav>a.btn.active,.navbar .navbar-nav>a.btn.active:focus,.navbar .navbar-nav>a.btn.active:hover,.navbar .navbar-nav>a.btn:active,.navbar .navbar-nav>a.btn:active:focus,.navbar .navbar-nav>a.btn:active:hover,.navbar .navbar-nav>a.btn:focus,.navbar .navbar-nav>a.btn:hover{background-color:#263148!important;background-image:linear-gradient(to bottom left,#344675,#263148,#344675)!important;background-image:-moz-linear-gradient(to bottom left,#344675,#263148,#344675)!important;color:#fff;box-shadow:none}.btn:active,.navbar .navbar-nav>a.btn:active{box-shadow:none!important;transform:translateY(1px)!important;transition:all .15s ease}.btn:not([data-action]):hover,.navbar .navbar-nav>a.btn:not([data-action]):hover{box-shadow:2px 2px 6px rgba(0,0,0,.4);transform:translateY(-1px);-webkit-transform:translateY(-1px)}.btn.disabled,.btn.disabled.active,.btn.disabled.focus,.btn.disabled:active,.btn.disabled:focus,.btn.disabled:hover,.btn:disabled,.btn:disabled.active,.btn:disabled.focus,.btn:disabled:active,.btn:disabled:focus,.btn:disabled:hover,.btn[disabled],.btn[disabled].active,.btn[disabled].focus,.btn[disabled]:active,.btn[disabled]:focus,.btn[disabled]:hover,.navbar .navbar-nav>a.btn.disabled,.navbar .navbar-nav>a.btn.disabled.active,.navbar .navbar-nav>a.btn.disabled.focus,.navbar .navbar-nav>a.btn.disabled:active,.navbar .navbar-nav>a.btn.disabled:focus,.navbar .navbar-nav>a.btn.disabled:hover,.navbar .navbar-nav>a.btn:disabled,.navbar .navbar-nav>a.btn:disabled.active,.navbar .navbar-nav>a.btn:disabled.focus,.navbar .navbar-nav>a.btn:disabled:active,.navbar .navbar-nav>a.btn:disabled:focus,.navbar .navbar-nav>a.btn:disabled:hover,.navbar .navbar-nav>a.btn[disabled],.navbar .navbar-nav>a.btn[disabled].active,.navbar .navbar-nav>a.btn[disabled].focus,.navbar .navbar-nav>a.btn[disabled]:active,.navbar .navbar-nav>a.btn[disabled]:focus,.navbar .navbar-nav>a.btn[disabled]:hover,fieldset[disabled] .btn,fieldset[disabled] .btn.active,fieldset[disabled] .btn.focus,fieldset[disabled] .btn:active,fieldset[disabled] .btn:focus,fieldset[disabled] .btn:hover,fieldset[disabled] .navbar .navbar-nav>a.btn,fieldset[disabled] .navbar .navbar-nav>a.btn.active,fieldset[disabled] .navbar .navbar-nav>a.btn.focus,fieldset[disabled] .navbar .navbar-nav>a.btn:active,fieldset[disabled] .navbar .navbar-nav>a.btn:focus,fieldset[disabled] .navbar .navbar-nav>a.btn:hover{background-color:#344675;border-color:#344675}.btn.btn-simple,.navbar .navbar-nav>a.btn.btn-simple{color:#344675;border-color:#344675;background:transparent}.btn.btn-simple:active,.btn.btn-simple:focus,.btn.btn-simple:hover,.btn.btn-simple:not(:disabled):not(.disabled):active,.navbar .navbar-nav>a.btn.btn-simple:active,.navbar .navbar-nav>a.btn.btn-simple:focus,.navbar .navbar-nav>a.btn.btn-simple:hover,.navbar .navbar-nav>a.btn.btn-simple:not(:disabled):not(.disabled):active{color:#344675;border-color:#344675;background-color:transparent!important;background-image:none!important;box-shadow:none}.btn.btn-simple.active,.navbar .navbar-nav>a.btn.btn-simple.active{border-color:#344675!important}.btn.btn-simple.active:active,.btn.btn-simple.active:focus,.btn.btn-simple.active:hover,.btn.btn-simple.active:not(:disabled):not(.disabled):active,.navbar .navbar-nav>a.btn.btn-simple.active:active,.navbar .navbar-nav>a.btn.btn-simple.active:focus,.navbar .navbar-nav>a.btn.btn-simple.active:hover,.navbar .navbar-nav>a.btn.btn-simple.active:not(:disabled):not(.disabled):active{color:#fff;border-color:#344675;background-image:linear-gradient(to bottom left,#344675,#263148,#344675)!important;background-image:-moz-linear-gradient(to bottom left,#344675,#263148,#344675)!important;background-color:#263148!important;box-shadow:none}.btn.btn-link,.navbar .navbar-nav>a.btn.btn-link{color:#344675}.btn.btn-link:active,.btn.btn-link:focus,.btn.btn-link:hover,.navbar .navbar-nav>a.btn.btn-link:active,.navbar .navbar-nav>a.btn.btn-link:focus,.navbar .navbar-nav>a.btn.btn-link:hover{background-color:transparent!important;background-image:none!important;color:#fff!important;text-decoration:none;box-shadow:none}.btn:focus,.btn:hover,.navbar .navbar-nav>a.btn:focus,.navbar .navbar-nav>a.btn:hover{opacity:1;filter:alpha(opacity=100);outline:0!important}.btn.active,.btn:active,.navbar .navbar-nav>a.btn.active,.navbar .navbar-nav>a.btn:active,.open>.btn.dropdown-toggle,.open>.navbar .navbar-nav>a.btn.dropdown-toggle{box-shadow:none;outline:0!important}.btn .badge,.navbar .navbar-nav>a.btn .badge{margin:0}.btn.btn-icon,.navbar .navbar-nav>a.btn.btn-icon{height:2.375rem;min-width:2.375rem;width:2.375rem;padding:0;font-size:.9375rem;overflow:hidden;position:relative;line-height:normal}.btn.btn-icon.btn-simple,.navbar .navbar-nav>a.btn.btn-icon.btn-simple{padding:0}.btn-group-sm>.btn.btn-icon,.btn.btn-icon.btn-sm,.navbar .btn-group-sm.navbar-nav>a.btn.btn-icon,.navbar .navbar-nav>a.btn.btn-icon.btn-sm{height:1.875rem;min-width:1.875rem;width:1.875rem}.btn-group-sm>.btn.btn-icon .fa,.btn-group-sm>.btn.btn-icon .far,.btn-group-sm>.btn.btn-icon .fas,.btn-group-sm>.btn.btn-icon .tim-icons,.btn.btn-icon.btn-sm .fa,.btn.btn-icon.btn-sm .far,.btn.btn-icon.btn-sm .fas,.btn.btn-icon.btn-sm .tim-icons,.navbar .btn-group-sm.navbar-nav>a.btn.btn-icon .fa,.navbar .btn-group-sm.navbar-nav>a.btn.btn-icon .far,.navbar .btn-group-sm.navbar-nav>a.btn.btn-icon .fas,.navbar .btn-group-sm.navbar-nav>a.btn.btn-icon .tim-icons,.navbar .navbar-nav>a.btn.btn-icon.btn-sm .fa,.navbar .navbar-nav>a.btn.btn-icon.btn-sm .far,.navbar .navbar-nav>a.btn.btn-icon.btn-sm .fas,.navbar .navbar-nav>a.btn.btn-icon.btn-sm .tim-icons{font-size:.6875rem}.btn-group-lg>.btn.btn-icon,.btn.btn-icon.btn-lg,.navbar .btn-group-lg.navbar-nav>a.btn.btn-icon,.navbar .navbar-nav>a.btn.btn-icon.btn-lg{height:3.6rem;min-width:3.6rem;width:3.6rem}.btn-group-lg>.btn.btn-icon .fa,.btn-group-lg>.btn.btn-icon .far,.btn-group-lg>.btn.btn-icon .fas,.btn-group-lg>.btn.btn-icon .tim-icons,.btn.btn-icon.btn-lg .fa,.btn.btn-icon.btn-lg .far,.btn.btn-icon.btn-lg .fas,.btn.btn-icon.btn-lg .tim-icons,.navbar .btn-group-lg.navbar-nav>a.btn.btn-icon .fa,.navbar .btn-group-lg.navbar-nav>a.btn.btn-icon .far,.navbar .btn-group-lg.navbar-nav>a.btn.btn-icon .fas,.navbar .btn-group-lg.navbar-nav>a.btn.btn-icon .tim-icons,.navbar .navbar-nav>a.btn.btn-icon.btn-lg .fa,.navbar .navbar-nav>a.btn.btn-icon.btn-lg .far,.navbar .navbar-nav>a.btn.btn-icon.btn-lg .fas,.navbar .navbar-nav>a.btn.btn-icon.btn-lg .tim-icons{font-size:1.325rem}.btn.btn-icon:not(.btn-footer) .fa,.btn.btn-icon:not(.btn-footer) .far,.btn.btn-icon:not(.btn-footer) .fas,.btn.btn-icon:not(.btn-footer) .tim-icons,.navbar .navbar-nav>a.btn.btn-icon:not(.btn-footer) .fa,.navbar .navbar-nav>a.btn.btn-icon:not(.btn-footer) .far,.navbar .navbar-nav>a.btn.btn-icon:not(.btn-footer) .fas,.navbar .navbar-nav>a.btn.btn-icon:not(.btn-footer) .tim-icons{position:absolute;font-size:1em;top:50%;left:50%;transform:translate(-12px,-12px);line-height:1.5626rem;width:24px}.btn:not(.btn-icon) .tim-icons,.navbar .navbar-nav>a.btn:not(.btn-icon) .tim-icons{position:relative;top:1px}.btn span,.navbar .navbar-nav>a.btn span{position:relative;display:block}.btn.btn-link.dropdown-toggle,.navbar .navbar-nav>a.btn.btn-link.dropdown-toggle{color:#9a9a9a}.btn.dropdown-toggle:after,.navbar .navbar-nav>a.btn.dropdown-toggle:after{margin-left:30px!important}.btn-primary{background:#e14eca;background-image:linear-gradient(to bottom left,#e14eca,#ba54f5,#e14eca);background-size:210% 210%;background-position:100% 0;background-color:#e14eca;transition:all .15s ease;box-shadow:none;color:#fff}.btn-primary.animation-on-hover:hover{background-position:0 100%;transition:.3s ease-in-out}.btn-primary.active,.btn-primary.active:focus,.btn-primary.active:hover,.btn-primary:active,.btn-primary:active:focus,.btn-primary:active:hover,.btn-primary:focus,.btn-primary:hover{background-color:#ba54f5!important;background-image:linear-gradient(to bottom left,#e14eca,#ba54f5,#e14eca)!important;background-image:-moz-linear-gradient(to bottom left,#e14eca,#ba54f5,#e14eca)!important;color:#fff;box-shadow:none}.btn-primary:active{box-shadow:none!important;transform:translateY(1px)!important;transition:all .15s ease}.btn-primary:not([data-action]):hover{box-shadow:2px 2px 6px rgba(0,0,0,.4);transform:translateY(-1px);-webkit-transform:translateY(-1px)}.btn-primary.disabled,.btn-primary.disabled.active,.btn-primary.disabled.focus,.btn-primary.disabled:active,.btn-primary.disabled:focus,.btn-primary.disabled:hover,.btn-primary:disabled,.btn-primary:disabled.active,.btn-primary:disabled.focus,.btn-primary:disabled:active,.btn-primary:disabled:focus,.btn-primary:disabled:hover,.btn-primary[disabled],.btn-primary[disabled].active,.btn-primary[disabled].focus,.btn-primary[disabled]:active,.btn-primary[disabled]:focus,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary,fieldset[disabled] .btn-primary.active,fieldset[disabled] .btn-primary.focus,fieldset[disabled] .btn-primary:active,fieldset[disabled] .btn-primary:focus,fieldset[disabled] .btn-primary:hover{background-color:#e14eca;border-color:#e14eca}.btn-primary.btn-simple{color:#e14eca;border-color:#e14eca;background:transparent}.btn-primary.btn-simple:active,.btn-primary.btn-simple:focus,.btn-primary.btn-simple:hover,.btn-primary.btn-simple:not(:disabled):not(.disabled):active{color:#e14eca;border-color:#e14eca;background-color:transparent!important;background-image:none!important;box-shadow:none}.btn-primary.btn-simple.active{border-color:#e14eca!important}.btn-primary.btn-simple.active:active,.btn-primary.btn-simple.active:focus,.btn-primary.btn-simple.active:hover,.btn-primary.btn-simple.active:not(:disabled):not(.disabled):active{color:#fff;border-color:#e14eca;background-image:linear-gradient(to bottom left,#e14eca,#ba54f5,#e14eca)!important;background-image:-moz-linear-gradient(to bottom left,#e14eca,#ba54f5,#e14eca)!important;background-color:#ba54f5!important;box-shadow:none}.btn-primary.btn-link{color:#e14eca}.btn-primary.btn-link:active,.btn-primary.btn-link:focus,.btn-primary.btn-link:hover{background-color:transparent!important;background-image:none!important;color:#fff!important;text-decoration:none;box-shadow:none}.btn-success{background:#00f2c3;background-image:linear-gradient(to bottom left,#00f2c3,#0098f0,#00f2c3);background-size:210% 210%;background-position:100% 0;background-color:#00f2c3;transition:all .15s ease;box-shadow:none;color:#fff}.btn-success.animation-on-hover:hover{background-position:0 100%;transition:.3s ease-in-out}.btn-success.active,.btn-success.active:focus,.btn-success.active:hover,.btn-success:active,.btn-success:active:focus,.btn-success:active:hover,.btn-success:focus,.btn-success:hover{background-color:#0098f0!important;background-image:linear-gradient(to bottom left,#00f2c3,#0098f0,#00f2c3)!important;background-image:-moz-linear-gradient(to bottom left,#00f2c3,#0098f0,#00f2c3)!important;color:#fff;box-shadow:none}.btn-success:active{box-shadow:none!important;transform:translateY(1px)!important;transition:all .15s ease}.btn-success:not([data-action]):hover{box-shadow:2px 2px 6px rgba(0,0,0,.4);transform:translateY(-1px);-webkit-transform:translateY(-1px)}.btn-success.disabled,.btn-success.disabled.active,.btn-success.disabled.focus,.btn-success.disabled:active,.btn-success.disabled:focus,.btn-success.disabled:hover,.btn-success:disabled,.btn-success:disabled.active,.btn-success:disabled.focus,.btn-success:disabled:active,.btn-success:disabled:focus,.btn-success:disabled:hover,.btn-success[disabled],.btn-success[disabled].active,.btn-success[disabled].focus,.btn-success[disabled]:active,.btn-success[disabled]:focus,.btn-success[disabled]:hover,fieldset[disabled] .btn-success,fieldset[disabled] .btn-success.active,fieldset[disabled] .btn-success.focus,fieldset[disabled] .btn-success:active,fieldset[disabled] .btn-success:focus,fieldset[disabled] .btn-success:hover{background-color:#00f2c3;border-color:#00f2c3}.btn-success.btn-simple{color:#00f2c3;border-color:#00f2c3;background:transparent}.btn-success.btn-simple:active,.btn-success.btn-simple:focus,.btn-success.btn-simple:hover,.btn-success.btn-simple:not(:disabled):not(.disabled):active{color:#00f2c3;border-color:#00f2c3;background-color:transparent!important;background-image:none!important;box-shadow:none}.btn-success.btn-simple.active{border-color:#00f2c3!important}.btn-success.btn-simple.active:active,.btn-success.btn-simple.active:focus,.btn-success.btn-simple.active:hover,.btn-success.btn-simple.active:not(:disabled):not(.disabled):active{color:#fff;border-color:#00f2c3;background-image:linear-gradient(to bottom left,#00f2c3,#0098f0,#00f2c3)!important;background-image:-moz-linear-gradient(to bottom left,#00f2c3,#0098f0,#00f2c3)!important;background-color:#0098f0!important;box-shadow:none}.btn-success.btn-link{color:#00f2c3}.btn-success.btn-link:active,.btn-success.btn-link:focus,.btn-success.btn-link:hover{background-color:transparent!important;background-image:none!important;color:#fff!important;text-decoration:none;box-shadow:none}.btn-info{background:#1d8cf8;background-image:linear-gradient(to bottom left,#1d8cf8,#3358f4,#1d8cf8);background-size:210% 210%;background-position:100% 0;background-color:#1d8cf8;transition:all .15s ease;box-shadow:none;color:#fff}.btn-info.animation-on-hover:hover{background-position:0 100%;transition:.3s ease-in-out}.btn-info.active,.btn-info.active:focus,.btn-info.active:hover,.btn-info:active,.btn-info:active:focus,.btn-info:active:hover,.btn-info:focus,.btn-info:hover{background-color:#3358f4!important;background-image:linear-gradient(to bottom left,#1d8cf8,#3358f4,#1d8cf8)!important;background-image:-moz-linear-gradient(to bottom left,#1d8cf8,#3358f4,#1d8cf8)!important;color:#fff;box-shadow:none}.btn-info:active{box-shadow:none!important;transform:translateY(1px)!important;transition:all .15s ease}.btn-info:not([data-action]):hover{box-shadow:2px 2px 6px rgba(0,0,0,.4);transform:translateY(-1px);-webkit-transform:translateY(-1px)}.btn-info.disabled,.btn-info.disabled.active,.btn-info.disabled.focus,.btn-info.disabled:active,.btn-info.disabled:focus,.btn-info.disabled:hover,.btn-info:disabled,.btn-info:disabled.active,.btn-info:disabled.focus,.btn-info:disabled:active,.btn-info:disabled:focus,.btn-info:disabled:hover,.btn-info[disabled],.btn-info[disabled].active,.btn-info[disabled].focus,.btn-info[disabled]:active,.btn-info[disabled]:focus,.btn-info[disabled]:hover,fieldset[disabled] .btn-info,fieldset[disabled] .btn-info.active,fieldset[disabled] .btn-info.focus,fieldset[disabled] .btn-info:active,fieldset[disabled] .btn-info:focus,fieldset[disabled] .btn-info:hover{background-color:#1d8cf8;border-color:#1d8cf8}.btn-info.btn-simple{color:#1d8cf8;border-color:#1d8cf8;background:transparent}.btn-info.btn-simple:active,.btn-info.btn-simple:focus,.btn-info.btn-simple:hover,.btn-info.btn-simple:not(:disabled):not(.disabled):active{color:#1d8cf8;border-color:#1d8cf8;background-color:transparent!important;background-image:none!important;box-shadow:none}.btn-info.btn-simple.active{border-color:#1d8cf8!important}.btn-info.btn-simple.active:active,.btn-info.btn-simple.active:focus,.btn-info.btn-simple.active:hover,.btn-info.btn-simple.active:not(:disabled):not(.disabled):active{color:#fff;border-color:#1d8cf8;background-image:linear-gradient(to bottom left,#1d8cf8,#3358f4,#1d8cf8)!important;background-image:-moz-linear-gradient(to bottom left,#1d8cf8,#3358f4,#1d8cf8)!important;background-color:#3358f4!important;box-shadow:none}.btn-info.btn-link{color:#1d8cf8}.btn-info.btn-link:active,.btn-info.btn-link:focus,.btn-info.btn-link:hover{background-color:transparent!important;background-image:none!important;color:#fff!important;text-decoration:none;box-shadow:none}.btn-warning{background:#ff8d72;background-image:linear-gradient(to bottom left,#ff8d72,#ff6491,#ff8d72);background-size:210% 210%;background-position:100% 0;background-color:#ff8d72;transition:all .15s ease;box-shadow:none;color:#fff}.btn-warning.animation-on-hover:hover{background-position:0 100%;transition:.3s ease-in-out}.btn-warning.active,.btn-warning.active:focus,.btn-warning.active:hover,.btn-warning:active,.btn-warning:active:focus,.btn-warning:active:hover,.btn-warning:focus,.btn-warning:hover{background-color:#ff6491!important;background-image:linear-gradient(to bottom left,#ff8d72,#ff6491,#ff8d72)!important;background-image:-moz-linear-gradient(to bottom left,#ff8d72,#ff6491,#ff8d72)!important;color:#fff;box-shadow:none}.btn-warning:active{box-shadow:none!important;transform:translateY(1px)!important;transition:all .15s ease}.btn-warning:not([data-action]):hover{box-shadow:2px 2px 6px rgba(0,0,0,.4);transform:translateY(-1px);-webkit-transform:translateY(-1px)}.btn-warning.disabled,.btn-warning.disabled.active,.btn-warning.disabled.focus,.btn-warning.disabled:active,.btn-warning.disabled:focus,.btn-warning.disabled:hover,.btn-warning:disabled,.btn-warning:disabled.active,.btn-warning:disabled.focus,.btn-warning:disabled:active,.btn-warning:disabled:focus,.btn-warning:disabled:hover,.btn-warning[disabled],.btn-warning[disabled].active,.btn-warning[disabled].focus,.btn-warning[disabled]:active,.btn-warning[disabled]:focus,.btn-warning[disabled]:hover,fieldset[disabled] .btn-warning,fieldset[disabled] .btn-warning.active,fieldset[disabled] .btn-warning.focus,fieldset[disabled] .btn-warning:active,fieldset[disabled] .btn-warning:focus,fieldset[disabled] .btn-warning:hover{background-color:#ff8d72;border-color:#ff8d72}.btn-warning.btn-simple{color:#ff8d72;border-color:#ff8d72;background:transparent}.btn-warning.btn-simple:active,.btn-warning.btn-simple:focus,.btn-warning.btn-simple:hover,.btn-warning.btn-simple:not(:disabled):not(.disabled):active{color:#ff8d72;border-color:#ff8d72;background-color:transparent!important;background-image:none!important;box-shadow:none}.btn-warning.btn-simple.active{border-color:#ff8d72!important}.btn-warning.btn-simple.active:active,.btn-warning.btn-simple.active:focus,.btn-warning.btn-simple.active:hover,.btn-warning.btn-simple.active:not(:disabled):not(.disabled):active{color:#fff;border-color:#ff8d72;background-image:linear-gradient(to bottom left,#ff8d72,#ff6491,#ff8d72)!important;background-image:-moz-linear-gradient(to bottom left,#ff8d72,#ff6491,#ff8d72)!important;background-color:#ff6491!important;box-shadow:none}.btn-warning.btn-link{color:#ff8d72}.btn-warning.btn-link:active,.btn-warning.btn-link:focus,.btn-warning.btn-link:hover{background-color:transparent!important;background-image:none!important;color:#fff!important;text-decoration:none;box-shadow:none}.btn-warning:not(:disabled):not(.disabled):active{color:#fff}.btn-danger{background:#fd5d93;background-image:linear-gradient(to bottom left,#fd5d93,#ec250d,#fd5d93);background-size:210% 210%;background-position:100% 0;background-color:#fd5d93;transition:all .15s ease;box-shadow:none;color:#fff}.btn-danger.animation-on-hover:hover{background-position:0 100%;transition:.3s ease-in-out}.btn-danger.active,.btn-danger.active:focus,.btn-danger.active:hover,.btn-danger:active,.btn-danger:active:focus,.btn-danger:active:hover,.btn-danger:focus,.btn-danger:hover{background-color:#ec250d!important;background-image:linear-gradient(to bottom left,#fd5d93,#ec250d,#fd5d93)!important;background-image:-moz-linear-gradient(to bottom left,#fd5d93,#ec250d,#fd5d93)!important;color:#fff;box-shadow:none}.btn-danger:active{box-shadow:none!important;transform:translateY(1px)!important;transition:all .15s ease}.btn-danger:not([data-action]):hover{box-shadow:2px 2px 6px rgba(0,0,0,.4);transform:translateY(-1px);-webkit-transform:translateY(-1px)}.btn-danger.disabled,.btn-danger.disabled.active,.btn-danger.disabled.focus,.btn-danger.disabled:active,.btn-danger.disabled:focus,.btn-danger.disabled:hover,.btn-danger:disabled,.btn-danger:disabled.active,.btn-danger:disabled.focus,.btn-danger:disabled:active,.btn-danger:disabled:focus,.btn-danger:disabled:hover,.btn-danger[disabled],.btn-danger[disabled].active,.btn-danger[disabled].focus,.btn-danger[disabled]:active,.btn-danger[disabled]:focus,.btn-danger[disabled]:hover,fieldset[disabled] .btn-danger,fieldset[disabled] .btn-danger.active,fieldset[disabled] .btn-danger.focus,fieldset[disabled] .btn-danger:active,fieldset[disabled] .btn-danger:focus,fieldset[disabled] .btn-danger:hover{background-color:#fd5d93;border-color:#fd5d93}.btn-danger.btn-simple{color:#fd5d93;border-color:#fd5d93;background:transparent}.btn-danger.btn-simple:active,.btn-danger.btn-simple:focus,.btn-danger.btn-simple:hover,.btn-danger.btn-simple:not(:disabled):not(.disabled):active{color:#fd5d93;border-color:#fd5d93;background-color:transparent!important;background-image:none!important;box-shadow:none}.btn-danger.btn-simple.active{border-color:#fd5d93!important}.btn-danger.btn-simple.active:active,.btn-danger.btn-simple.active:focus,.btn-danger.btn-simple.active:hover,.btn-danger.btn-simple.active:not(:disabled):not(.disabled):active{color:#fff;border-color:#fd5d93;background-image:linear-gradient(to bottom left,#fd5d93,#ec250d,#fd5d93)!important;background-image:-moz-linear-gradient(to bottom left,#fd5d93,#ec250d,#fd5d93)!important;background-color:#ec250d!important;box-shadow:none}.btn-danger.btn-link{color:#fd5d93}.btn-danger.btn-link:active,.btn-danger.btn-link:focus,.btn-danger.btn-link:hover{background-color:transparent!important;background-image:none!important;color:#fff!important;text-decoration:none;box-shadow:none}.btn-neutral{background:#fff;background-image:linear-gradient(to bottom left,#fff,#fff,#fff);background-size:210% 210%;background-position:100% 0;background-color:#fff;transition:all .15s ease;box-shadow:none;color:#e14eca}.btn-neutral.animation-on-hover:hover{background-position:0 100%;transition:.3s ease-in-out}.btn-neutral.active,.btn-neutral.active:focus,.btn-neutral.active:hover,.btn-neutral:active,.btn-neutral:active:focus,.btn-neutral:active:hover,.btn-neutral:focus,.btn-neutral:hover{background-color:#fff!important;background-image:linear-gradient(to bottom left,#fff,#fff,#fff)!important;background-image:-moz-linear-gradient(to bottom left,#fff,#fff,#fff)!important;color:#fff;box-shadow:none}.btn-neutral:active{box-shadow:none!important;transform:translateY(1px)!important;transition:all .15s ease}.btn-neutral:not([data-action]):hover{box-shadow:2px 2px 6px rgba(0,0,0,.4);transform:translateY(-1px);-webkit-transform:translateY(-1px)}.btn-neutral.disabled,.btn-neutral.disabled.active,.btn-neutral.disabled.focus,.btn-neutral.disabled:active,.btn-neutral.disabled:focus,.btn-neutral.disabled:hover,.btn-neutral:disabled,.btn-neutral:disabled.active,.btn-neutral:disabled.focus,.btn-neutral:disabled:active,.btn-neutral:disabled:focus,.btn-neutral:disabled:hover,.btn-neutral[disabled],.btn-neutral[disabled].active,.btn-neutral[disabled].focus,.btn-neutral[disabled]:active,.btn-neutral[disabled]:focus,.btn-neutral[disabled]:hover,fieldset[disabled] .btn-neutral,fieldset[disabled] .btn-neutral.active,fieldset[disabled] .btn-neutral.focus,fieldset[disabled] .btn-neutral:active,fieldset[disabled] .btn-neutral:focus,fieldset[disabled] .btn-neutral:hover{background-color:#fff;border-color:#fff}.btn-neutral.btn-danger{color:#fd5d93}.btn-neutral.btn-danger:active,.btn-neutral.btn-danger:active:focus,.btn-neutral.btn-danger:focus,.btn-neutral.btn-danger:hover{color:#ec250d}.btn-neutral.btn-info{color:#1d8cf8}.btn-neutral.btn-info:active,.btn-neutral.btn-info:active:focus,.btn-neutral.btn-info:focus,.btn-neutral.btn-info:hover{color:#3358f4}.btn-neutral.btn-warning{color:#ff8d72}.btn-neutral.btn-warning:active,.btn-neutral.btn-warning:active:focus,.btn-neutral.btn-warning:focus,.btn-neutral.btn-warning:hover{color:#ff6491}.btn-neutral.btn-success{color:#00f2c3}.btn-neutral.btn-success:active,.btn-neutral.btn-success:active:focus,.btn-neutral.btn-success:focus,.btn-neutral.btn-success:hover{color:#0098f0}.btn-neutral.btn-default{color:#344675}.btn-neutral.btn-default:active,.btn-neutral.btn-default:active:focus,.btn-neutral.btn-default:focus,.btn-neutral.btn-default:hover{color:#263148}.btn-neutral.active,.btn-neutral.active:focus,.btn-neutral.active:hover,.btn-neutral:active,.btn-neutral:active:focus,.btn-neutral:active:hover,.show>.btn-neutral.dropdown-toggle,.show>.btn-neutral.dropdown-toggle:focus,.show>.btn-neutral.dropdown-toggle:hover{background-color:#fff;color:#ba54f5;box-shadow:none}.btn-neutral:focus,.btn-neutral:hover{color:#ba54f5}.btn-neutral:focus:not(.nav-link),.btn-neutral:hover:not(.nav-link){box-shadow:none}.btn-neutral.btn-simple{color:#fff;border-color:#fff;background:transparent}.btn-neutral.btn-simple:active,.btn-neutral.btn-simple:focus,.btn-neutral.btn-simple:hover,.btn-neutral.btn-simple:not(:disabled):not(.disabled):active{color:#fff;border-color:#fff;background-color:transparent!important;background-image:none!important;box-shadow:none}.btn-neutral.btn-simple.active{border-color:#fff!important}.btn-neutral.btn-simple.active:active,.btn-neutral.btn-simple.active:focus,.btn-neutral.btn-simple.active:hover,.btn-neutral.btn-simple.active:not(:disabled):not(.disabled):active{color:#fff;border-color:#fff;background-image:linear-gradient(to bottom left,#fff,#fff,#fff)!important;background-image:-moz-linear-gradient(to bottom left,#fff,#fff,#fff)!important;background-color:#fff!important;box-shadow:none}.btn-neutral.btn-link{color:#fff}.btn-neutral.btn-link:active,.btn-neutral.btn-link:focus,.btn-neutral.btn-link:hover{background-color:transparent!important;background-image:none!important;color:#fff!important;text-decoration:none;box-shadow:none}.btn.disabled,.btn:disabled,.btn[disabled]{opacity:.5;filter:alpha(opacity=50);pointer-events:none}.btn-simple{border:1px solid;border-color:#344675;box-shadow:none;padding:10px 22px;background-color:transparent}.btn-link.disabled,.btn-link.disabled.active,.btn-link.disabled.focus,.btn-link.disabled:active,.btn-link.disabled:focus,.btn-link.disabled:hover,.btn-link:disabled,.btn-link:disabled.active,.btn-link:disabled.focus,.btn-link:disabled:active,.btn-link:disabled:focus,.btn-link:disabled:hover,.btn-link[disabled],.btn-link[disabled].active,.btn-link[disabled].focus,.btn-link[disabled]:active,.btn-link[disabled]:focus,.btn-link[disabled]:hover,.btn-simple.disabled,.btn-simple.disabled.active,.btn-simple.disabled.focus,.btn-simple.disabled:active,.btn-simple.disabled:focus,.btn-simple.disabled:hover,.btn-simple:disabled,.btn-simple:disabled.active,.btn-simple:disabled.focus,.btn-simple:disabled:active,.btn-simple:disabled:focus,.btn-simple:disabled:hover,.btn-simple[disabled],.btn-simple[disabled].active,.btn-simple[disabled].focus,.btn-simple[disabled]:active,.btn-simple[disabled]:focus,.btn-simple[disabled]:hover,fieldset[disabled] .btn-link,fieldset[disabled] .btn-link.active,fieldset[disabled] .btn-link.focus,fieldset[disabled] .btn-link:active,fieldset[disabled] .btn-link:focus,fieldset[disabled] .btn-link:hover,fieldset[disabled] .btn-simple,fieldset[disabled] .btn-simple.active,fieldset[disabled] .btn-simple.focus,fieldset[disabled] .btn-simple:active,fieldset[disabled] .btn-simple:focus,fieldset[disabled] .btn-simple:hover{background:transparent}.btn:not(:disabled):not(.disabled).active,.btn:not(:disabled):not(.disabled):active{box-shadow:2px 2px 6px rgba(0,0,0,.4)}.btn-link{border:0;box-shadow:none;padding:.5rem .7rem;background:transparent;color:#e3e3e3;font-weight:600}.btn-link:hover{box-shadow:none!important;transform:none!important}.btn-group-lg>.btn,.btn-lg{font-size:.875rem;border-radius:.4285rem;padding:15px 48px}.btn-group-lg>.btn-simple.btn,.btn-lg.btn-simple{padding:14px 47px}.btn-group-sm>.btn,.btn-sm{font-size:.875rem;border-radius:.2857rem;padding:5px 15px}.btn-group-sm>.btn-simple.btn,.btn-sm.btn-simple{padding:4px 14px}.btn-wd{min-width:140px}.btn-group.select{width:100%}.btn-group.select .btn{text-align:left}.btn-group.select .caret{position:absolute;top:50%;margin-top:-1px;right:8px}.btn-group .btn.active{box-shadow:2px 2px 6px rgba(0,0,0,.4);transform:translateY(-1px);-webkit-transform:translateY(-1px)}.btn-round{border-width:1px;border-radius:30px}.btn-round.btn-simple{padding:10px 22px}.no-caret.dropdown-toggle:after{display:none}.btn-secondary:not(:disabled):not(.disabled).active,.btn-secondary:not(:disabled):not(.disabled):active,.show>.btn-secondary.dropdown-toggle{color:#fff}.btn-group label.btn.active{transform:translateY(0);-webkit-transform:translateY(0)}.dropdown-menu{border:0;box-shadow:0 10px 50px 0 rgba(0,0,0,.2);border-radius:.1428rem;transition:all .15s linear}.dropdown-menu.dropdown-menu-right:after,.dropdown-menu.dropdown-menu-right:before{left:auto;right:10px}.dropdown-menu.dropdown-black{background:linear-gradient(180deg,#222a42 0,#1d253b);border:1px solid #344675}.dropdown-menu.dropdown-black .dropdown-item{color:hsla(0,0%,100%,.7)}.dropdown-menu.dropdown-black .dropdown-divider{border-color:#344675}.dropdown-menu.dropdown-black:before{color:#222a42;z-index:2}.dropdown-menu.dropdown-black:after{display:inline-block;position:absolute;width:0;height:0;z-index:1;vertical-align:middle;content:"";top:-6px;left:10px;right:auto;color:#344675;border-bottom:.4em solid;border-right:.4em solid transparent;border-left:.4em solid transparent}.dropdown-menu.dropdown-black.dropdown-menu-right:after{left:auto;right:10px}.dropup .dropdown-menu.dropdown-black:after{color:#1d253b;z-index:2}.dropup .dropdown-menu.dropdown-black:before{display:inline-block;position:absolute;width:0;height:0;vertical-align:middle;content:"";top:auto;bottom:-6px;right:auto;left:10px;color:#555;border-top:.4em solid;border-right:.4em solid transparent;border-left:.4em solid transparent;border-bottom:none;z-index:1}.dropdown-menu i{margin-right:5px;position:relative;top:1px}.dropdown-menu .tim-icons{margin-right:10px;position:relative;top:4px;font-size:18px;margin-top:-5px;opacity:.5}.dropdown-menu .dropdown-item.active,.dropdown-menu .dropdown-item:active{color:inherit}.dropup .dropdown-menu:before{display:none}.dropup .dropdown-menu:after{display:inline-block;position:absolute;width:0;height:0;vertical-align:middle;content:"";top:auto;bottom:-5px;right:auto;left:10px;color:#fff;border-top:.4em solid;border-right:.4em solid transparent;border-left:.4em solid transparent;border-bottom:none}.dropup .dropdown-menu.dropdown-menu-right:after,.dropup .dropdown-menu.dropdown-menu-right:before{right:10px;left:auto}.dropdown-menu:before{display:inline-block;position:absolute;width:0;height:0;vertical-align:middle;content:"";top:-5px;left:10px;right:auto;color:#fff;border-bottom:.4em solid;border-right:.4em solid transparent;border-left:.4em solid transparent}.dropdown-menu.dropdown-menu-right{right:0!important;left:auto!important}.bootstrap-select .dropdown-menu.inner li a,.dropdown-menu .dropdown-item{font-size:.75rem;padding-top:.6rem;padding-bottom:.6rem;margin-top:5px;transition:all .15s linear}.bootstrap-select .dropdown-menu.inner li a:focus,.bootstrap-select .dropdown-menu.inner li a:hover,.dropdown-menu .dropdown-item:focus,.dropdown-menu .dropdown-item:hover{background-color:hsla(0,0%,87%,.3)}.bootstrap-select .dropdown-menu.inner li a.disabled,.bootstrap-select .dropdown-menu.inner li a:disabled,.dropdown-menu .dropdown-item.disabled,.dropdown-menu .dropdown-item:disabled{color:hsla(0,0%,71%,.6)}.bootstrap-select .dropdown-menu.inner li a.disabled:focus,.bootstrap-select .dropdown-menu.inner li a.disabled:hover,.bootstrap-select .dropdown-menu.inner li a:disabled:focus,.bootstrap-select .dropdown-menu.inner li a:disabled:hover,.dropdown-menu .dropdown-item.disabled:focus,.dropdown-menu .dropdown-item.disabled:hover,.dropdown-menu .dropdown-item:disabled:focus,.dropdown-menu .dropdown-item:disabled:hover{background-color:transparent;box-shadow:none}.dropdown-menu .dropdown-divider{background-color:hsla(0,0%,87%,.5)}.dropdown-menu .dropdown-header:not([href]):not([tabindex]){color:hsla(0,0%,71%,.6);font-size:.62475rem;text-transform:uppercase;font-weight:600}.dropdown-menu.dropdown-primary{background-color:#df41c6}.dropdown-menu.dropdown-primary:before{color:#df41c6}.dropdown-menu.dropdown-primary .dropdown-header:not([href]):not([tabindex]){color:hsla(0,0%,100%,.8)}.dropdown-menu.dropdown-primary .dropdown-item{color:#fff}.dropdown-menu.dropdown-primary .dropdown-divider,.dropdown-menu.dropdown-primary .dropdown-item:focus,.dropdown-menu.dropdown-primary .dropdown-item:hover{background-color:hsla(0,0%,100%,.2)}.dropdown-menu.dropdown-info{background-color:#0e84f8}.dropdown-menu.dropdown-info:before{color:#0e84f8}.dropdown-menu.dropdown-info .dropdown-header:not([href]):not([tabindex]){color:hsla(0,0%,100%,.8)}.dropdown-menu.dropdown-info .dropdown-item{color:#fff}.dropdown-menu.dropdown-info .dropdown-divider,.dropdown-menu.dropdown-info .dropdown-item:focus,.dropdown-menu.dropdown-info .dropdown-item:hover{background-color:hsla(0,0%,100%,.2)}.dropdown-menu.dropdown-danger{background-color:#fd4e89}.dropdown-menu.dropdown-danger:before{color:#fd4e89}.dropdown-menu.dropdown-danger .dropdown-header:not([href]):not([tabindex]){color:hsla(0,0%,100%,.8)}.dropdown-menu.dropdown-danger .dropdown-item{color:#fff}.dropdown-menu.dropdown-danger .dropdown-divider,.dropdown-menu.dropdown-danger .dropdown-item:focus,.dropdown-menu.dropdown-danger .dropdown-item:hover{background-color:hsla(0,0%,100%,.2)}.dropdown-menu.dropdown-success{background-color:#00e3b7}.dropdown-menu.dropdown-success:before{color:#00e3b7}.dropdown-menu.dropdown-success .dropdown-header:not([href]):not([tabindex]){color:hsla(0,0%,100%,.8)}.dropdown-menu.dropdown-success .dropdown-item{color:#fff}.dropdown-menu.dropdown-success .dropdown-divider,.dropdown-menu.dropdown-success .dropdown-item:focus,.dropdown-menu.dropdown-success .dropdown-item:hover{background-color:hsla(0,0%,100%,.2)}.dropdown-menu.dropdown-warning{background-color:#ff8163}.dropdown-menu.dropdown-warning:before{color:#ff8163}.dropdown-menu.dropdown-warning .dropdown-header:not([href]):not([tabindex]){color:hsla(0,0%,100%,.8)}.dropdown-menu.dropdown-warning .dropdown-item{color:#fff}.dropdown-menu.dropdown-warning .dropdown-divider,.dropdown-menu.dropdown-warning .dropdown-item:focus,.dropdown-menu.dropdown-warning .dropdown-item:hover{background-color:hsla(0,0%,100%,.2)}.bootstrap-select .dropdown-menu:not(.inner),.dropdown-menu.bootstrap-datetimepicker-widget.bottom,.dropdown .dropdown-menu,.dropup:not(.bootstrap-select) .dropdown-menu{transform:translate3d(0,-20px,0)!important;visibility:hidden;display:block;opacity:0;filter:alpha(opacity=0);top:100%!important}.dropdown-menu.bootstrap-datetimepicker-widget.top{transform:translate3d(0,-20px,0)!important;visibility:hidden;display:block;opacity:0;filter:alpha(opacity=0)}.dropdown-menu.bootstrap-datetimepicker-widget.bottom,.dropdown-menu.bootstrap-datetimepicker-widget.top{transform:translate3d(0,-20px,0)!important}.bootstrap-select.dropup .dropdown-menu:not(.inner){transform:translate3d(0,25px,0)!important}.dropup:not(.bootstrap-select) .dropdown-menu{transform:translate3d(0,20px,0)!important;top:auto!important;bottom:100%}.bootstrap-select.show .dropdown-menu:not(.inner),.dropdown-menu.bootstrap-datetimepicker-widget.bottom.open,.dropdown-menu.bootstrap-datetimepicker-widget.top.open,.dropdown.show .dropdown-menu,.dropup.show:not(.bootstrap-select) .dropdown-menu,.navbar .dropdown.show .dropdown-menu{opacity:1;filter:alpha(opacity=100);visibility:visible;transform:translate3d(0,1px,0)!important}.dropdown-menu.bootstrap-datetimepicker-widget.bottom.open,.dropdown-menu.bootstrap-datetimepicker-widget.top.open{transform:translateZ(0)!important}.dropup.show:not(.bootstrap-select) .dropdown-menu{transform:translate3d(0,-2px,0)!important}.dropdown-menu.dropdown-navbar{left:-80px}.dropdown-menu.dropdown-navbar:after,.dropdown-menu.dropdown-navbar:before{left:auto;right:17px}.btn{cursor:pointer}.btn.dropdown-toggle[data-toggle=dropdown]{padding:10px;margin:0;margin-bottom:5px}.btn.dropdown-toggle[data-toggle=dropdown]:after{content:"";margin-left:5px}.btn span.bs-caret{display:none}.btn.btn-link.dropdown-toggle{height:22px;padding:0;margin-right:5px}.dropdown-toggle:after{content:unset}.btn:not(:disabled):not(.disabled).active:focus,.btn:not(:disabled):not(.disabled):active:focus,.show>.btn.dropdown-toggle:focus{box-shadow:none}.dropdown-menu-sm{min-width:100px;border:.4285rem}.dropdown-menu-lg{min-width:260px;border-radius:.4285rem}.dropdown-menu-xl{min-width:450px;border-radius:.4285rem}@media screen and (max-width:991px){.dropdown-toggle:after{display:inline-block;width:0;height:0;margin-left:.255em;vertical-align:.255em;content:"";border-top:.3em solid;border-right:.3em solid transparent;border-bottom:0;border-left:.3em solid transparent}}@media screen and (min-width:992px){.dropdown-menu .dropdown-item{color:#9a9a9a}}.footer{padding:24px 0 24px 250px}.footer [class*=container-]{padding:0}.footer .nav{float:left;margin-bottom:0;padding-left:30px;list-style:none}.footer .nav,.footer .nav-item{display:inline-block}.footer .nav-item:first-child a{padding-left:0}.footer .nav-link{color:#fff;padding:0 .5rem;font-size:.75rem;text-transform:uppercase}.footer .nav-link,.footer .nav-link:hover{text-decoration:none}.footer .copyright{font-size:.75rem;line-height:1.8;color:#fff}.footer:after{display:table;clear:both;content:" "}@media screen and (max-width:991px){.footer{padding-left:0}.footer .copyright{text-align:right;margin-right:15px}}@media screen and (min-width:992px){.footer .copyright{float:right;padding-right:30px}}@media screen and (max-width:768px){.footer nav{display:block;margin-bottom:5px;float:none}}@media screen and (max-width:576px){.footer,.footer .copyright{text-align:center}.footer .nav{float:none;padding-left:0}}.form-control:-moz-placeholder,.form-control::-moz-placeholder{color:#6c757c;opacity:1;filter:alpha(opacity=100)}.form-control::-webkit-input-placeholder{color:#6c757c;opacity:1;filter:alpha(opacity=100)}.form-control:-ms-input-placeholder{color:#6c757c;opacity:1;filter:alpha(opacity=100)}.form-control{border-color:#2b3553;border-radius:.4285rem;font-size:.75rem;transition:color .3s ease-in-out,border-color .3s ease-in-out,background-color .3s ease-in-out}.form-control:focus{border-color:#e14eca;background-color:transparent;box-shadow:none}.form-control:focus+.input-group-append .input-group-text,.form-control:focus+.input-group-prepend .input-group-text,.form-control:focus~.input-group-append .input-group-text,.form-control:focus~.input-group-prepend .input-group-text{border:1px solid #e14eca;border-left:none;background-color:transparent}.has-error .form-control,.has-error .form-control:focus,.has-success .form-control,.has-success .form-control:focus{box-shadow:none}.has-danger .form-control.form-control-danger,.has-danger .form-control.form-control-success,.has-success .form-control.form-control-danger,.has-success .form-control.form-control-success{background-image:none}.form-control+.form-control-feedback{border-radius:.4285rem;margin-top:-7px;position:absolute;right:10px;top:50%;vertical-align:middle}.open .form-control{border-radius:.4285rem .4285rem 0 0;border-bottom-color:transparent}.form-control+.input-group-append .input-group-text,.form-control+.input-group-prepend .input-group-text{background-color:#fff}.has-success .form-control,.has-success .input-group-append .input-group-text,.has-success .input-group-prepend .input-group-text{border-color:#2b3553}.has-success .form-control:focus,.has-success.input-group-focus .input-group-append .input-group-text,.has-success.input-group-focus .input-group-prepend .input-group-text{border-color:#00bf9a}.has-danger .form-control,.has-danger .input-group-append .input-group-text,.has-danger.input-group-focus .input-group-append .input-group-text,.has-danger.input-group-focus .input-group-prepend .input-group-text,.has-danger .input-group-prepend .input-group-text{border-color:#f33620;color:#ec250d;background-color:hsla(0,0%,87%,.1)}.has-danger .form-control:focus,.has-danger .input-group-append .input-group-text:focus,.has-danger.input-group-focus .input-group-append .input-group-text:focus,.has-danger.input-group-focus .input-group-prepend .input-group-text:focus,.has-danger .input-group-prepend .input-group-text:focus{background-color:transparent}.has-danger:after,.has-success:after{font-family:nucleo;content:"\ea1b";display:inline-block;position:absolute;right:20px;top:13px;color:#00f2c3;font-size:11px}.has-danger.form-control-lg:after,.has-success.form-control-lg:after{font-size:13px;top:24px}.has-danger.has-label:after,.has-success.has-label:after{top:37px}.has-danger.form-check:after,.has-success.form-check:after{display:none!important}.has-danger.form-check .form-check-label,.has-success.form-check .form-check-label{color:#00f2c3}.has-danger:after{content:"\ea48";color:#ec250d}.has-danger.form-check .form-check-label{color:#ec250d}img{max-width:100%;border-radius:.2857rem}.img-raised{box-shadow:0 10px 25px 0 rgba(0,0,0,.3)}.modal-content{border:0}.modal-content .modal-header{border-bottom:none}.modal-content .modal-header button{position:absolute;right:27px;top:24px;outline:0;padding:1rem;margin:-1rem -1rem -1rem auto}.modal-content .modal-header .title{color:#222a42;margin-top:5px;margin-bottom:0}.modal-content .modal-header .modal-title{color:#222a42}.modal-content .modal-header i.tim-icons{font-size:16px}.modal-content .modal-body{line-height:1.9}.modal-content .modal-body p{color:#222a42}.modal-content .modal-footer{border-top:0;justify-content:space-between}.modal-content .modal-footer button{margin:0;padding-left:16px;padding-right:16px;width:auto}.modal-content .modal-footer button.pull-left{padding-left:5px;padding-right:5px;position:relative;left:-5px}.modal-content .modal-body+.modal-footer{padding-top:0}.modal-backdrop{background:rgba(0,0,0,.3)}.modal.modal-default .modal-content{background-color:#fff;color:#222a42}.modal.modal-default .modal-body p{color:hsla(0,0%,100%,.8)}.modal.modal-default .form-control:-moz-placeholder,.modal.modal-default .form-control::-moz-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-default .form-control::-webkit-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-default .form-control:-ms-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-default .form-control{border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-default .form-control:focus{border-color:#fff;background-color:transparent;color:#fff}.modal.modal-default .has-danger:after,.modal.modal-default .has-success:after{color:#fff}.modal.modal-default .has-danger .form-control{background-color:transparent}.modal.modal-default .input-group-prepend{margin-right:0}.modal.modal-default .input-group-append .input-group-text,.modal.modal-default .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-default .input-group-focus .input-group-append .input-group-text,.modal.modal-default .input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);border-color:#fff;color:#fff}.modal.modal-default .form-group.no-border .form-control,.modal.modal-default .input-group.no-border .form-control{background-color:rgba(30,30,47,.2);color:#fff}.modal.modal-default .form-group.no-border .form-control:active,.modal.modal-default .form-group.no-border .form-control:focus,.modal.modal-default .input-group.no-border .form-control:active,.modal.modal-default .input-group.no-border .form-control:focus{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-default .form-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-default .form-group.no-border .form-control+.input-group-prepend .input-group-text,.modal.modal-default .input-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-default .input-group.no-border .form-control+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2)}.modal.modal-default .form-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-default .form-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-default .form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-default .form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-default .form-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-default .form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.modal.modal-default .input-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-default .input-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-default .input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-default .input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-default .input-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-default .input-group.no-border .form-control:focus+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-default .form-group.no-border .input-group-append .input-group-text,.modal.modal-default .form-group.no-border .input-group-prepend .input-group-text,.modal.modal-default .input-group.no-border .input-group-append .input-group-text,.modal.modal-default .input-group.no-border .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border:none;color:#fff}.modal.modal-default .form-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-default .form-group.no-border.input-group-focus .input-group-prepend .input-group-text,.modal.modal-default .input-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-default .input-group.no-border.input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-primary .modal-content{background-color:#e14eca;color:#fff}.modal.modal-primary .modal-body p{color:hsla(0,0%,100%,.8)}.modal.modal-primary .form-control:-moz-placeholder,.modal.modal-primary .form-control::-moz-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-primary .form-control::-webkit-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-primary .form-control:-ms-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-primary .form-control{border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-primary .form-control:focus{border-color:#fff;background-color:transparent;color:#fff}.modal.modal-primary .has-danger:after,.modal.modal-primary .has-success:after{color:#fff}.modal.modal-primary .has-danger .form-control{background-color:transparent}.modal.modal-primary .input-group-prepend{margin-right:0}.modal.modal-primary .input-group-append .input-group-text,.modal.modal-primary .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-primary .input-group-focus .input-group-append .input-group-text,.modal.modal-primary .input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);border-color:#fff;color:#fff}.modal.modal-primary .form-group.no-border .form-control,.modal.modal-primary .input-group.no-border .form-control{background-color:rgba(30,30,47,.2);color:#fff}.modal.modal-primary .form-group.no-border .form-control:active,.modal.modal-primary .form-group.no-border .form-control:focus,.modal.modal-primary .input-group.no-border .form-control:active,.modal.modal-primary .input-group.no-border .form-control:focus{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-primary .form-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-primary .form-group.no-border .form-control+.input-group-prepend .input-group-text,.modal.modal-primary .input-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-primary .input-group.no-border .form-control+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2)}.modal.modal-primary .form-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-primary .form-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-primary .form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-primary .form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-primary .form-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-primary .form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.modal.modal-primary .input-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-primary .input-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-primary .input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-primary .input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-primary .input-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-primary .input-group.no-border .form-control:focus+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-primary .form-group.no-border .input-group-append .input-group-text,.modal.modal-primary .form-group.no-border .input-group-prepend .input-group-text,.modal.modal-primary .input-group.no-border .input-group-append .input-group-text,.modal.modal-primary .input-group.no-border .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border:none;color:#fff}.modal.modal-primary .form-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-primary .form-group.no-border.input-group-focus .input-group-prepend .input-group-text,.modal.modal-primary .input-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-primary .input-group.no-border.input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-danger .modal-content{background-color:#fd5d93;color:#fff}.modal.modal-danger .modal-body p{color:hsla(0,0%,100%,.8)}.modal.modal-danger .form-control:-moz-placeholder,.modal.modal-danger .form-control::-moz-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-danger .form-control::-webkit-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-danger .form-control:-ms-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-danger .form-control{border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-danger .form-control:focus{border-color:#fff;background-color:transparent;color:#fff}.modal.modal-danger .has-danger:after,.modal.modal-danger .has-success:after{color:#fff}.modal.modal-danger .has-danger .form-control{background-color:transparent}.modal.modal-danger .input-group-prepend{margin-right:0}.modal.modal-danger .input-group-append .input-group-text,.modal.modal-danger .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-danger .input-group-focus .input-group-append .input-group-text,.modal.modal-danger .input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);border-color:#fff;color:#fff}.modal.modal-danger .form-group.no-border .form-control,.modal.modal-danger .input-group.no-border .form-control{background-color:rgba(30,30,47,.2);color:#fff}.modal.modal-danger .form-group.no-border .form-control:active,.modal.modal-danger .form-group.no-border .form-control:focus,.modal.modal-danger .input-group.no-border .form-control:active,.modal.modal-danger .input-group.no-border .form-control:focus{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-danger .form-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-danger .form-group.no-border .form-control+.input-group-prepend .input-group-text,.modal.modal-danger .input-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-danger .input-group.no-border .form-control+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2)}.modal.modal-danger .form-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-danger .form-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-danger .form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-danger .form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-danger .form-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-danger .form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.modal.modal-danger .input-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-danger .input-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-danger .input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-danger .input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-danger .input-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-danger .input-group.no-border .form-control:focus+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-danger .form-group.no-border .input-group-append .input-group-text,.modal.modal-danger .form-group.no-border .input-group-prepend .input-group-text,.modal.modal-danger .input-group.no-border .input-group-append .input-group-text,.modal.modal-danger .input-group.no-border .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border:none;color:#fff}.modal.modal-danger .form-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-danger .form-group.no-border.input-group-focus .input-group-prepend .input-group-text,.modal.modal-danger .input-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-danger .input-group.no-border.input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-warning .modal-content{background-color:#ff8d72;color:#fff}.modal.modal-warning .modal-body p{color:hsla(0,0%,100%,.8)}.modal.modal-warning .form-control:-moz-placeholder,.modal.modal-warning .form-control::-moz-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-warning .form-control::-webkit-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-warning .form-control:-ms-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-warning .form-control{border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-warning .form-control:focus{border-color:#fff;background-color:transparent;color:#fff}.modal.modal-warning .has-danger:after,.modal.modal-warning .has-success:after{color:#fff}.modal.modal-warning .has-danger .form-control{background-color:transparent}.modal.modal-warning .input-group-prepend{margin-right:0}.modal.modal-warning .input-group-append .input-group-text,.modal.modal-warning .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-warning .input-group-focus .input-group-append .input-group-text,.modal.modal-warning .input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);border-color:#fff;color:#fff}.modal.modal-warning .form-group.no-border .form-control,.modal.modal-warning .input-group.no-border .form-control{background-color:rgba(30,30,47,.2);color:#fff}.modal.modal-warning .form-group.no-border .form-control:active,.modal.modal-warning .form-group.no-border .form-control:focus,.modal.modal-warning .input-group.no-border .form-control:active,.modal.modal-warning .input-group.no-border .form-control:focus{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-warning .form-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-warning .form-group.no-border .form-control+.input-group-prepend .input-group-text,.modal.modal-warning .input-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-warning .input-group.no-border .form-control+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2)}.modal.modal-warning .form-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-warning .form-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-warning .form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-warning .form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-warning .form-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-warning .form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.modal.modal-warning .input-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-warning .input-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-warning .input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-warning .input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-warning .input-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-warning .input-group.no-border .form-control:focus+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-warning .form-group.no-border .input-group-append .input-group-text,.modal.modal-warning .form-group.no-border .input-group-prepend .input-group-text,.modal.modal-warning .input-group.no-border .input-group-append .input-group-text,.modal.modal-warning .input-group.no-border .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border:none;color:#fff}.modal.modal-warning .form-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-warning .form-group.no-border.input-group-focus .input-group-prepend .input-group-text,.modal.modal-warning .input-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-warning .input-group.no-border.input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-success .modal-content{background-color:#00f2c3;color:#fff}.modal.modal-success .modal-body p{color:hsla(0,0%,100%,.8)}.modal.modal-success .form-control:-moz-placeholder,.modal.modal-success .form-control::-moz-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-success .form-control::-webkit-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-success .form-control:-ms-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-success .form-control{border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-success .form-control:focus{border-color:#fff;background-color:transparent;color:#fff}.modal.modal-success .has-danger:after,.modal.modal-success .has-success:after{color:#fff}.modal.modal-success .has-danger .form-control{background-color:transparent}.modal.modal-success .input-group-prepend{margin-right:0}.modal.modal-success .input-group-append .input-group-text,.modal.modal-success .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-success .input-group-focus .input-group-append .input-group-text,.modal.modal-success .input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);border-color:#fff;color:#fff}.modal.modal-success .form-group.no-border .form-control,.modal.modal-success .input-group.no-border .form-control{background-color:rgba(30,30,47,.2);color:#fff}.modal.modal-success .form-group.no-border .form-control:active,.modal.modal-success .form-group.no-border .form-control:focus,.modal.modal-success .input-group.no-border .form-control:active,.modal.modal-success .input-group.no-border .form-control:focus{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-success .form-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-success .form-group.no-border .form-control+.input-group-prepend .input-group-text,.modal.modal-success .input-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-success .input-group.no-border .form-control+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2)}.modal.modal-success .form-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-success .form-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-success .form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-success .form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-success .form-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-success .form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.modal.modal-success .input-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-success .input-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-success .input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-success .input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-success .input-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-success .input-group.no-border .form-control:focus+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-success .form-group.no-border .input-group-append .input-group-text,.modal.modal-success .form-group.no-border .input-group-prepend .input-group-text,.modal.modal-success .input-group.no-border .input-group-append .input-group-text,.modal.modal-success .input-group.no-border .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border:none;color:#fff}.modal.modal-success .form-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-success .form-group.no-border.input-group-focus .input-group-prepend .input-group-text,.modal.modal-success .input-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-success .input-group.no-border.input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-info .modal-content{background-color:#1d8cf8;color:#fff}.modal.modal-info .modal-body p{color:hsla(0,0%,100%,.8)}.modal.modal-info .form-control:-moz-placeholder,.modal.modal-info .form-control::-moz-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-info .form-control::-webkit-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-info .form-control:-ms-input-placeholder{color:hsla(0,0%,100%,.4);opacity:1;filter:alpha(opacity=100)}.modal.modal-info .form-control{border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-info .form-control:focus{border-color:#fff;background-color:transparent;color:#fff}.modal.modal-info .has-danger:after,.modal.modal-info .has-success:after{color:#fff}.modal.modal-info .has-danger .form-control{background-color:transparent}.modal.modal-info .input-group-prepend{margin-right:0}.modal.modal-info .input-group-append .input-group-text,.modal.modal-info .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border-color:hsla(0,0%,100%,.5);color:#fff}.modal.modal-info .input-group-focus .input-group-append .input-group-text,.modal.modal-info .input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);border-color:#fff;color:#fff}.modal.modal-info .form-group.no-border .form-control,.modal.modal-info .input-group.no-border .form-control{background-color:rgba(30,30,47,.2);color:#fff}.modal.modal-info .form-group.no-border .form-control:active,.modal.modal-info .form-group.no-border .form-control:focus,.modal.modal-info .input-group.no-border .form-control:active,.modal.modal-info .input-group.no-border .form-control:focus{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-info .form-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-info .form-group.no-border .form-control+.input-group-prepend .input-group-text,.modal.modal-info .input-group.no-border .form-control+.input-group-append .input-group-text,.modal.modal-info .input-group.no-border .form-control+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2)}.modal.modal-info .form-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-info .form-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-info .form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-info .form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-info .form-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-info .form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.modal.modal-info .input-group.no-border .form-control+.input-group-append .input-group-text:active,.modal.modal-info .input-group.no-border .form-control+.input-group-append .input-group-text:focus,.modal.modal-info .input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.modal.modal-info .input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.modal.modal-info .input-group.no-border .form-control:focus+.input-group-append .input-group-text,.modal.modal-info .input-group.no-border .form-control:focus+.input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal.modal-info .form-group.no-border .input-group-append .input-group-text,.modal.modal-info .form-group.no-border .input-group-prepend .input-group-text,.modal.modal-info .input-group.no-border .input-group-append .input-group-text,.modal.modal-info .input-group.no-border .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.2);border:none;color:#fff}.modal.modal-info .form-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-info .form-group.no-border.input-group-focus .input-group-prepend .input-group-text,.modal.modal-info .input-group.no-border.input-group-focus .input-group-append .input-group-text,.modal.modal-info .input-group.no-border.input-group-focus .input-group-prepend .input-group-text{background-color:rgba(30,30,47,.3);color:#fff}.modal .modal-header .close{color:#fd5d93;text-shadow:none}.modal .modal-header .close:focus,.modal .modal-header .close:hover{opacity:1}.modal.modal-black .modal-content{background:linear-gradient(180deg,#222a42 0,#1d253b);color:hsla(0,0%,100%,.8)}.modal.modal-black .modal-content .modal-header .modal-title,.modal.modal-black .modal-content .modal-header .title{color:hsla(0,0%,100%,.9)}.modal.modal-black .modal-content .modal-body p{color:hsla(0,0%,100%,.8)}.modal.modal-black h1,.modal.modal-black h2,.modal.modal-black h3,.modal.modal-black h4,.modal.modal-black h5,.modal.modal-black h6,.modal.modal-black p{color:#fff}.modal-search .modal-dialog{max-width:650px}.modal-search .modal-dialog input{border:none;font-size:17px;font-weight:100}.modal-search .modal-dialog span{font-size:35px;color:#b7b7b7}.modal-search .modal-content .modal-header{padding:24px}.modal-search .modal-header .close{color:#555;top:30px!important}.modal-search .modal-footer{border-top:2px solid #f9f9f9;margin:0 25px 20px}.navbar{padding:10px 30px 10px 15px;width:100%;z-index:1050;background:#1a1e34}.navbar .photo{display:inline-block;height:30px;width:30px;border-radius:50%;vertical-align:middle;overflow:hidden}.navbar .photo img{width:100%}.navbar .navbar-wrapper{display:flex;align-items:center}.navbar .navbar-text{color:#fff}.navbar .btn{margin:0 5px 0 10px}.navbar.navbar-absolute{position:absolute;z-index:1050}.navbar.navbar-transparent{background:transparent!important}.navbar.bg-white .navbar-nav .search-bar.input-group i{color:#222a42}.navbar.bg-white .navbar-nav .search-bar.input-group .form-control{background:rgba(34,42,66,.1);border-radius:4px}.navbar.bg-white .navbar-nav a.nav-link{color:#222a42!important}.navbar.bg-white .navbar-brand,.navbar.bg-white .navbar-nav a.nav-link p,.navbar.bg-white .navbar-text{color:#222a42}.navbar.bg-white .form-control{color:#222a42!important}.navbar.bg-white .form-control::placeholder{color:#9a9a9a!important}.navbar.bg-dark{background:#222a42!important}.navbar.bg-primary{background-color:#e14eca!important}.navbar.bg-warning{background-color:#ff8d72!important}.navbar.bg-info{background-color:#1d8cf8!important}.navbar.bg-success{background-color:#00bf9a!important}.navbar.bg-danger{background-color:#fd5d93!important}.navbar .navbar-brand{position:fixed;padding-top:.3125rem;padding-bottom:.3125rem;color:#fff;margin-left:17px;margin-top:3px;text-transform:uppercase;font-size:1rem}.navbar .navbar-toggle button:focus,.navbar .navbar-toggler{outline:none}.navbar-minimize-fixed{position:fixed;margin-left:40px;margin-top:14px;transition:.3s ease;color:#fff;z-index:20;opacity:0;transition:.2s ease}.navbar-minimize-fixed button i{font-size:20px}.notification{background:#fd5d93;color:#fff;border-radius:.875rem;height:6px;width:6px;position:absolute;text-align:center;font-size:12px;font-weight:800;top:10px;right:10px;border:1px solid #fd5d93}.navbar-nav li{padding:0 10px}.navbar-nav li a{color:#fff}.navbar-nav li i{vertical-align:middle;font-size:20px}@media screen and (max-width:991px){.navbar .container-fluid{padding-right:15px;padding-left:15px}.navbar .navbar-collapse .input-group{margin:0;margin-top:5px}.navbar .navbar-nav .btn{margin-left:-3px;display:flex}.navbar .navbar-nav .btn i{margin-right:12px}.navbar .navbar-nav .btn span{margin:0;text-transform:uppercase;font-weight:300}.navbar .navbar-nav .btn span,.navbar .navbar-nav .btn span:active,.navbar .navbar-nav .btn span:active:focus,.navbar .navbar-nav .btn span:focus,.navbar .navbar-nav .btn span:hover{color:#222a42!important}.navbar .navbar-nav a.nav-link i{opacity:1;margin-left:4px;margin-right:5px}.navbar .navbar-nav a.nav-link p{display:inline-block;text-transform:uppercase;margin-left:7px}.navbar .navbar-nav .modal-search .modal-dialog{padding:0 40px}.navbar .navbar-nav .dropdown{margin:5px 0}.navbar .navbar-nav .dropdown .nav-link{padding-bottom:0}.navbar .navbar-nav .dropdown .dropdown-menu .dropdown-item{margin-top:0;padding-left:24px}.navbar .dropdown.show .dropdown-menu{display:block}.navbar .dropdown .dropdown-menu{display:none}.navbar .dropdown .dropdown-menu li a{color:#222a42}.navbar .dropdown .dropdown-menu,.navbar .dropdown.show .dropdown-menu{background-color:transparent;border:0;transition:none;box-shadow:none;width:auto;margin:0 1rem;margin-top:0}.navbar .dropdown .dropdown-menu:before,.navbar .dropdown.show .dropdown-menu:before{display:none}.navbar .dropdown-menu .dropdown-item:focus,.navbar .dropdown-menu .dropdown-item:hover{color:#fff}.navbar.bg-white .dropdown-menu .dropdown-item:focus,.navbar.bg-white .dropdown-menu .dropdown-item:hover{color:#344675}.navbar button.navbar-toggler[data-target="#navigation"]{padding-top:0}.navbar .navbar-toggler-bar{display:block;position:relative;width:22px;height:1px;border-radius:1px;background:#fff}.navbar .navbar-toggler-bar.navbar-kebab{height:4px;width:4px;margin-bottom:3px;border-radius:50%}.navbar .navbar-toggler-bar+.navbar-toggler-bar{margin-top:7px}.navbar .navbar-toggler-bar+.navbar-toggler-bar.navbar-kebab{margin-top:0}.navbar .navbar-toggler-bar.bar2{width:17px;transition:width .2s linear}.navbar.bg-white:not(.navbar-transparent) .navbar-toggler-bar{background-color:#344675}.navbar .toggled .navbar-toggler-bar{width:24px}.navbar .toggled .navbar-toggler-bar+.navbar-toggler-bar{margin-top:5px}.navbar .navbar-brand{margin-left:20px;position:relative}.navbar-nav .nav-link i.fa,.navbar-nav .nav-link i.tim-icons{opacity:.5}.bar1,.bar2,.bar3{outline:1px solid transparent}.bar1{top:0;animation:c .5s 0s;animation-fill-mode:forwards}.bar2{opacity:1}.bar3{bottom:0;animation:e .5s 0s;animation-fill-mode:forwards}.toggled .bar1{top:6px;animation:b .5s 0s;animation-fill-mode:forwards}.toggled .bar2{opacity:0}.toggled .bar3{bottom:6px;animation:d .5s 0s;animation-fill-mode:forwards}@keyframes b{0%{top:0;transform:rotate(0deg)}45%{top:6px;transform:rotate(145deg)}75%{transform:rotate(130deg)}to{transform:rotate(135deg)}}@keyframes c{0%{top:6px;transform:rotate(135deg)}45%{transform:rotate(-10deg)}75%{transform:rotate(5deg)}to{top:0;transform:rotate(0)}}@keyframes d{0%{bottom:0;transform:rotate(0deg)}45%{bottom:6px;transform:rotate(-145deg)}75%{transform:rotate(-130deg)}to{transform:rotate(-135deg)}}@keyframes e{0%{bottom:6px;transform:rotate(-135deg)}45%{transform:rotate(10deg)}75%{transform:rotate(-5deg)}to{bottom:0;transform:rotate(0)}}}@media screen and (min-width:992px){.navbar-collapse{background:none!important}.navbar .navbar-toggle{display:none}.navbar-nav .nav-link.profile-photo{padding:0;margin:7px .7rem}.navbar .caret{position:absolute;left:80%;top:55%;margin-left:0}.navbar-expand-lg .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}}@media screen and (max-width:576px){.navbar[class*=navbar-toggleable-] .container{margin-left:0;margin-right:0}}body{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6{line-height:1.2}p{font-weight:300}button,input,optgroup,select,textarea{font-family:Poppins,sans-serif}.card h1,.card h2,.card h3,.card h4,.card h5,.card h6,.card p,h1,h2,h3,h4,h5,h6{color:hsla(0,0%,100%,.8)}h1,h2,h3,h4,h5,h6{font-weight:400}a{color:#ba54f5;font-weight:300}a:focus,a:hover{color:#e14eca}.h1,h1{line-height:1.05;margin-bottom:30px}.h1 small,h1 small{font-weight:600;text-transform:uppercase;opacity:.8}.h2,h2{line-height:1.2}.h2,.h3,h2,h3{margin-bottom:30px}.h3,h3{line-height:1.4em}.h4,h4{line-height:1.45em;margin-bottom:15px}.h4+.category,.h4.title+.category,h4+.category,h4.title+.category{margin-top:-10px}.h5,h5{line-height:1.4em;margin-bottom:15px}.h6,h6{text-transform:uppercase;font-weight:600}p{color:hsla(0,0%,100%,.8);margin-bottom:5px}p.description{font-size:1.14em}.title{font-weight:600}.title.title-up{text-transform:uppercase}.title.title-up a{color:#222a42;text-decoration:none}.title+.category{margin-top:-10px}.card-description,.card .footer .stats,.description,.footer-big p{color:#9a9a9a;font-weight:300}.card-category,.category{text-transform:capitalize;font-weight:400;color:hsla(0,0%,100%,.6);font-size:.75rem}.card-category{font-size:.75rem}.blockquote{border-left:none;border:1px solid #344675;padding:20px;font-size:.9625rem;line-height:1.8}.blockquote small{color:#344675;font-size:.75rem;text-transform:uppercase}.blockquote.blockquote-primary{border-color:#e14eca;color:#e14eca}.blockquote.blockquote-primary small{color:#e14eca}.blockquote.blockquote-danger{border-color:#fd5d93;color:#fd5d93}.blockquote.blockquote-danger small{color:#fd5d93}.blockquote.blockquote-white{border-color:hsla(0,0%,100%,.8);color:#fff}.blockquote.blockquote-white small{color:hsla(0,0%,100%,.8)}ol li,ul li{color:#fff}pre{color:hsla(0,0%,100%,.8)}hr{border-top:1px solid rgba(0,0,0,.1);margin-top:1rem;margin-bottom:1rem}.table>tbody>tr>td{color:hsla(0,0%,100%,.7)!important}.table>tbody>tr>td .photo{height:30px;width:30px;border-radius:50%;overflow:hidden;margin:0 auto}.table>tbody>tr>td .photo img{width:100%}.table>tbody>tr.table-success>td{background-color:#00bf9a}.table>tbody>tr.table-info>td{background-color:#1d8cf8}.table>tbody>tr.table-primary>td{background-color:#e14eca}.table>tbody>tr.table-warning>td{background-color:#ff8d72}.table>tbody>tr.table-danger>td{background-color:#fd5d93}.table .img-wrapper{width:40px;height:40px;border-radius:50%;overflow:hidden;margin:0 auto}.table .img-row{max-width:60px;width:60px}.table .form-check{margin:0;margin-top:5px}.table .form-check label .form-check-sign:after,.table .form-check label .form-check-sign:before{top:-17px;left:4px}.table .btn{margin:0}.table .small,.table small{font-weight:300}.card-tasks .card-body .table{margin-bottom:0}.card-tasks .card-body .table>tbody>tr>td,.card-tasks .card-body .table>tbody>tr>th,.card-tasks .card-body .table>tfoot>tr>td,.card-tasks .card-body .table>tfoot>tr>th,.card-tasks .card-body .table>thead>tr>td,.card-tasks .card-body .table>thead>tr>th{padding-top:5px;padding-bottom:5px}.table>thead>tr>th{border-bottom-width:1px;font-size:12px;text-transform:uppercase;font-weight:700;border:0;color:hsla(0,0%,100%,.7)}.table .checkbox,.table .radio{margin-top:0;margin-bottom:0;padding:0;width:15px}.table .checkbox .icons,.table .radio .icons{position:relative}.table .checkbox label:after,.table .checkbox label:before,.table .radio label:after,.table .radio label:before{top:-17px;left:-3px}.table>tbody>tr>td,.table>tbody>tr>th,.table>tfoot>tr>td,.table>tfoot>tr>th,.table>thead>tr>td,.table>thead>tr>th{border-color:hsla(0,0%,100%,.1);padding:12px 7px;vertical-align:middle}.table.table-shopping tbody tr:last-child td{border:none}.table .th-description{max-width:150px}.table .td-price{font-size:26px;font-weight:300;margin-top:5px;position:relative;top:4px;text-align:right}.table .td-total{font-weight:600;font-size:.8125rem;padding-top:20px;text-align:right}.table .td-actions .btn{margin:0}.table>tbody>tr{position:relative}.table>tfoot>tr{color:hsla(0,0%,100%,.7);text-transform:uppercase}.table-responsive{overflow:scroll;padding-bottom:10px}#tables .table-responsive{margin-bottom:30px}table.tablesorter thead tr .header{background-image:url(../img/bg.html);background-repeat:no-repeat;background-position:100%;cursor:pointer}table.tablesorter thead tr .headerSortUp{background-image:url(../img/asc.html)}table.tablesorter thead tr .headerSortDown{background-image:url(../img/desc.html)}.dataTables_wrapper .table-striped tbody tr:nth-of-type(odd){background-color:rgba(0,0,0,.05)}.dataTables_wrapper .form-control-sm{font-size:10px}.form-check{margin-top:.5rem;padding-left:0}.form-check .form-check-label{display:inline-block;position:relative;cursor:pointer;padding-left:25px;line-height:18px;margin-bottom:0;transition:color .3s linear}.radio .form-check-sign{padding-left:28px}.form-check-radio.form-check-inline .form-check-label{padding-left:5px;margin-right:10px}.form-check .form-check-sign:after,.form-check .form-check-sign:before{content:" ";display:inline-block;position:absolute;width:17px;height:17px;left:0;cursor:pointer;border-radius:3px;top:0;border:1px solid #818181;transition:opacity .3s linear}.form-check input[type=checkbox]:checked+.form-check-sign:before{border:none;background-color:#e14eca}.form-check .form-check-sign:after{font-family:nucleo;content:"\ea1b";top:0;text-align:center;font-size:14px;opacity:0;color:#fff;font-weight:600;border:0;background-color:inherit}.form-check.disabled .form-check-label{color:#9a9a9a;opacity:.5;cursor:not-allowed}.form-check input[type=checkbox],.radio input[type=radio]{opacity:0;position:absolute;visibility:hidden}.form-check input[type=checkbox]:checked+.form-check-sign:after{opacity:1;font-size:10px;margin-top:0}.form-check input[type=checkbox]+.form-check-sign:after{opacity:0;font-size:10px;margin-top:0}.checkbox input[type=checkbox]:disabled+.form-check-sign:after,.form-control input[type=checkbox]:disabled+.form-check-sign:before{cursor:not-allowed}.form-check input[type=checkbox]:disabled+.form-check-sign,.form-check input[type=radio]:disabled+.form-check-sign{pointer-events:none}.form-check-radio .form-check-label{padding-top:3px}.form-check-radio .form-check-sign:after,.form-check-radio .form-check-sign:before{content:" ";width:18px;height:18px;border-radius:50%;border:1px solid #818181;display:inline-block;position:absolute;left:0;top:3px;padding:1px;transition:opacity .3s linear}.form-check-radio input[type=radio],.form-check-radio input[type=radio]+.form-check-sign:after{opacity:0}.form-check-radio input[type=radio]:checked+.form-check-sign:after{width:6px;height:6px;background-color:#e14eca;border-color:#e14eca;top:9px;left:6px}.form-check-radio input[type=radio]:checked+.form-check-sign:before{border-color:#e14eca}.form-check-radio input[type=radio]:checked+.form-check-sign:after{opacity:1}.form-check-radio input[type=radio]:disabled+.form-check-sign,.form-check-radio input[type=radio]:disabled+.form-check-sign:after,.form-check-radio input[type=radio]:disabled+.form-check-sign:before{color:#9a9a9a}.fixed-plugin{position:fixed;right:0;width:64px;background:rgba(0,0,0,.3);z-index:1031;border-radius:8px 0 0 8px;text-align:center;top:130px}.fixed-plugin .badge,.fixed-plugin li>a{transition:all .34s;-webkit-transition:all .34s;-moz-transition:all .34s}.fixed-plugin .fa-cog{color:#fff;padding:10px;border-radius:0 0 6px 6px;width:auto}.fixed-plugin .dropdown-menu{right:80px;left:auto!important;top:-52px!important;width:290px;border-radius:.1875rem;padding:0 10px;background:linear-gradient(#222a42,#1d253b)}.fixed-plugin .dropdown .dropdown-menu .tim-icons{top:5px}.fixed-plugin .dropdown-menu:after,.fixed-plugin .dropdown-menu:before{right:10px;margin-left:auto;left:auto}.fixed-plugin .fa-circle-thin{color:#fff}.fixed-plugin .active .fa-circle-thin{color:#0bf}.fixed-plugin .dropdown-menu>.active>a,.fixed-plugin .dropdown-menu>.active>a:focus,.fixed-plugin .dropdown-menu>.active>a:hover{color:#777;text-align:center}.fixed-plugin img{border-radius:0;width:100%;height:100px;margin:0 auto}.fixed-plugin .dropdown-menu li>a:focus,.fixed-plugin .dropdown-menu li>a:hover{box-shadow:none}.fixed-plugin .badge{border:2px solid #fff;border-radius:50%;cursor:pointer;display:inline-block;height:23px;margin-right:5px;position:relative;width:23px}.fixed-plugin .badge.active,.fixed-plugin .badge:hover{border-color:#1d253b}.fixed-plugin .dark-badge,.fixed-plugin .light-badge{margin:0;border:1px solid #1d8cf8}.fixed-plugin .dark-badge:hover,.fixed-plugin .light-badge:hover{border:1px solid #1d8cf8}.fixed-plugin .light-badge,.fixed-plugin .light-badge:hover{background:#fff}.fixed-plugin .dark-badge,.fixed-plugin .dark-badge:hover{background:#222a42}.fixed-plugin h5{margin:10px}.fixed-plugin .dropdown-menu li{display:block;padding:18px 2px;width:25%;float:left}.fixed-plugin li.adjustments-line,.fixed-plugin li.button-container,.fixed-plugin li.header-title{width:100%;height:50px;min-height:inherit}.fixed-plugin li.button-container{height:auto}.fixed-plugin li.button-container div{margin-bottom:5px}.fixed-plugin #sharrreTitle{text-align:center;padding:10px 0;height:50px}.fixed-plugin li.header-title{color:#fff;height:30px;line-height:25px;font-size:12px;font-weight:600;text-align:center;text-transform:uppercase}.fixed-plugin .adjustments-line a{color:transparent}.fixed-plugin .adjustments-line a .badge-colors{position:relative;top:-2px}.fixed-plugin .adjustments-line a a:focus,.fixed-plugin .adjustments-line a a:hover{color:transparent}.fixed-plugin .adjustments-line .togglebutton{text-align:center}.fixed-plugin .adjustments-line .togglebutton .label-switch{position:relative;left:-10px;font-size:.62475rem;color:#fff}.fixed-plugin .adjustments-line .togglebutton .label-switch.label-right{left:10px}.fixed-plugin .adjustments-line .togglebutton .toggle{margin-right:0}.fixed-plugin .adjustments-line .color-label{position:relative;top:-7px;font-size:.62475rem;color:#fff}.fixed-plugin .adjustments-line .dropdown-menu>li.adjustments-line>a{padding-right:0;padding-left:0;border-bottom:1px solid #ddd;border-radius:0;margin:0}.fixed-plugin .dropdown-menu>li>a.img-holder{font-size:1rem;text-align:center;border-radius:10px;background-color:#fff;border:3px solid #fff;padding-left:0;padding-right:0;opacity:1;cursor:pointer;display:block;max-height:100px;overflow:hidden;padding:0}.fixed-plugin .dropdown-menu>li>a.img-holder img{margin-top:auto}.fixed-plugin .dropdown-menu>li>a.switch-trigger:focus,.fixed-plugin .dropdown-menu>li a.switch-trigger:hover{background-color:transparent}.fixed-plugin .dropdown-menu>li:focus>a.img-holder,.fixed-plugin .dropdown-menu>li:hover>a.img-holder{border-color:rgba(0,187,255,.53)}.fixed-plugin .dropdown-menu>.active>a.img-holder{border-color:#0bf;background-color:#fff}.fixed-plugin .btn-social{width:50%;display:block;width:48%;float:left;font-weight:600}.fixed-plugin .btn-social i{margin-right:5px}.fixed-plugin .btn-social:first-child{margin-right:2%}.fixed-plugin .dropdown .dropdown-menu{transform:translateY(-15%);top:27px;opacity:0;transform-origin:0 0}.fixed-plugin .dropdown .dropdown-menu:before{border-bottom:.4em solid transparent;border-left:.4em solid rgba(0,0,0,.2);border-top:.4em solid transparent;right:-16px;top:46px}.fixed-plugin .dropdown .dropdown-menu:after{border-bottom:.4em solid transparent;border-left:.4em solid #222a42;border-top:.4em solid transparent;right:-16px}.fixed-plugin .dropdown .dropdown-menu:after,.fixed-plugin .dropdown .dropdown-menu:before{content:"";display:inline-block;position:absolute;top:74px;width:16px;transform:translateY(-50%);-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%)}.fixed-plugin .dropdown.show .dropdown-menu{opacity:1;transform:translateY(-13%);transform-origin:0 0}.fixed-plugin .bootstrap-switch{margin:0}.wrapper{position:relative;top:0;height:100vh}.wrapper.wrapper-full-page{min-height:100vh;height:auto}.sidebar-wrapper ul li div.collapse ul li div.collapse ul li a,.sidebar-wrapper ul li div.collapse ul li div.collapsing ul li a,.sidebar-wrapper ul li div.collapsing ul li div.collapse ul li a{margin-left:25px}.off-canvas-sidebar,.sidebar{background:#ba54f5;background:linear-gradient(0deg,#ba54f5,#e14eca);height:calc(100vh - 90px);width:230px;position:fixed;top:0;left:0;z-index:1;background-size:cover;background-position:50%;display:block;box-shadow:0 0 45px 0 rgba(0,0,0,.6);margin-top:80px;margin-left:20px;border-radius:5px;transition:.5s cubic-bezier(.685,.0473,.346,1)}.off-canvas-sidebar .sidebar-wrapper,.sidebar .sidebar-wrapper{width:100%;min-height:100%;max-height:calc(100vh - 705px);z-index:4;position:relative;overflow:auto}.off-canvas-sidebar .sidebar-wrapper .dropdown .dropdown-backdrop,.sidebar .sidebar-wrapper .dropdown .dropdown-backdrop{display:none!important}.off-canvas-sidebar .sidebar-wrapper .navbar-form,.sidebar .sidebar-wrapper .navbar-form{border:none}.off-canvas-sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a span,.off-canvas-sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a span,.sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a span,.sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a span{display:inline-block}.off-canvas-sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.off-canvas-sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a .sidebar-normal{margin:0;position:relative;transform:translateX(0);opacity:1;white-space:nowrap;display:block;line-height:23px;z-index:1;color:hsla(0,0%,100%,.8)}.off-canvas-sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon,.off-canvas-sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon,.sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon,.sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon{text-transform:uppercase;width:34px;margin-right:10px;margin-left:0;font-size:12px;text-align:center;line-height:25px;position:relative;float:left;z-index:1;display:inherit;line-height:24px;color:hsla(0,0%,100%,.8)}.off-canvas-sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a i,.off-canvas-sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a i,.sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a i,.sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a i{font-size:17px;line-height:20px;width:26px}.off-canvas-sidebar .sidebar-wrapper [data-toggle=collapse]~div>ul>li:hover>a .sidebar-mini-icon,.off-canvas-sidebar .sidebar-wrapper [data-toggle=collapse]~div>ul>li:hover>a .sidebar-normal,.sidebar .sidebar-wrapper [data-toggle=collapse]~div>ul>li:hover>a .sidebar-mini-icon,.sidebar .sidebar-wrapper [data-toggle=collapse]~div>ul>li:hover>a .sidebar-normal{color:#fff}.off-canvas-sidebar .sidebar-wrapper .nav [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon,.sidebar .sidebar-wrapper .nav [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon{opacity:0}.off-canvas-sidebar .navbar-minimize,.sidebar .navbar-minimize{position:absolute;right:20px;top:2px;opacity:1}.off-canvas-sidebar .nav,.sidebar .nav{margin-top:20px;display:block}.off-canvas-sidebar .nav .caret,.sidebar .nav .caret{top:14px;position:absolute;right:10px}.off-canvas-sidebar .nav li>a+div .nav,.sidebar .nav li>a+div .nav{margin-top:5px}.off-canvas-sidebar .nav li>a+div .nav li>a,.sidebar .nav li>a+div .nav li>a{margin-top:0;padding:8px}.off-canvas-sidebar .nav li>a,.sidebar .nav li>a{margin:10px 15px 0;border-radius:30px;color:#fff;display:block;text-decoration:none;position:relative;text-transform:uppercase;cursor:pointer;font-size:.62475rem;padding:10px 8px;line-height:1.625rem}.off-canvas-sidebar .nav li:first-child>a,.sidebar .nav li:first-child>a{margin:0 15px}.off-canvas-sidebar .nav li:focus:not(.active)>a i,.off-canvas-sidebar .nav li:focus:not(.active)>a p,.off-canvas-sidebar .nav li:hover:not(.active)>a i,.off-canvas-sidebar .nav li:hover:not(.active)>a p,.sidebar .nav li:focus:not(.active)>a i,.sidebar .nav li:focus:not(.active)>a p,.sidebar .nav li:hover:not(.active)>a i,.sidebar .nav li:hover:not(.active)>a p{color:#fff}.off-canvas-sidebar .nav li.active>a:not([data-toggle=collapse]),.sidebar .nav li.active>a:not([data-toggle=collapse]){background:transparent}.off-canvas-sidebar .nav li.active>a:not([data-toggle=collapse]) i,.off-canvas-sidebar .nav li.active>a:not([data-toggle=collapse]) p,.sidebar .nav li.active>a:not([data-toggle=collapse]) i,.sidebar .nav li.active>a:not([data-toggle=collapse]) p{color:#fff}.off-canvas-sidebar .nav li.active>a:not([data-toggle=collapse]):before,.sidebar .nav li.active>a:not([data-toggle=collapse]):before{content:" ";position:absolute;height:6px;width:6px;top:22px;left:-4px;background:#fff;border-radius:50%}.off-canvas-sidebar .nav li.active>a[data-toggle=collapse],.sidebar .nav li.active>a[data-toggle=collapse]{background:transparent;box-shadow:none;color:#fff}.off-canvas-sidebar .nav li.active>a[data-toggle=collapse] i,.sidebar .nav li.active>a[data-toggle=collapse] i{color:#fff}.off-canvas-sidebar .nav li.active>a[data-toggle=collapse]+div .nav .active a,.sidebar .nav li.active>a[data-toggle=collapse]+div .nav .active a{box-shadow:none}.off-canvas-sidebar .nav li.active>a[data-toggle=collapse]+div .nav .active a .sidebar-mini-icon,.off-canvas-sidebar .nav li.active>a[data-toggle=collapse]+div .nav .active a .sidebar-normal,.sidebar .nav li.active>a[data-toggle=collapse]+div .nav .active a .sidebar-mini-icon,.sidebar .nav li.active>a[data-toggle=collapse]+div .nav .active a .sidebar-normal{color:#fff;font-weight:400}.off-canvas-sidebar .nav li.active>a[data-toggle=collapse]+div .nav .active a:before,.sidebar .nav li.active>a[data-toggle=collapse]+div .nav .active a:before{content:" ";position:absolute;height:6px;width:6px;top:17px;left:-4px;background:#fff;border-radius:50%}.off-canvas-sidebar .nav li.active>a[data-toggle=collapse]:before,.sidebar .nav li.active>a[data-toggle=collapse]:before{content:" ";position:absolute;height:6px;width:6px;top:22px;left:-4px;background:hsla(0,0%,100%,.6);border-radius:50%}.off-canvas-sidebar .nav p,.sidebar .nav p{margin:0;line-height:30px;position:relative;display:block;height:auto;white-space:nowrap}.off-canvas-sidebar .nav i,.sidebar .nav i{font-size:20px;float:left;margin-right:12px;line-height:30px;width:34px;text-align:center;color:hsla(0,0%,100%,.8);position:relative}.off-canvas-sidebar .logo,.sidebar .logo{position:relative;padding:.5rem .7rem;z-index:4}.off-canvas-sidebar .logo a.logo-mini,.sidebar .logo a.logo-mini{opacity:1;float:left;width:34px;text-align:center;margin-left:10px;margin-right:12px}.off-canvas-sidebar .logo a.logo-normal,.sidebar .logo a.logo-normal{display:block;opacity:1;transform:translateZ(0)}.off-canvas-sidebar .logo:after,.sidebar .logo:after{content:"";position:absolute;bottom:0;right:15px;height:1px;width:calc(100% - 30px);background:hsla(0,0%,100%,.5)}.off-canvas-sidebar .logo p,.sidebar .logo p{float:left;font-size:20px;margin:10px;color:#fff;line-height:20px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}.off-canvas-sidebar .logo .simple-text,.sidebar .logo .simple-text{text-transform:uppercase;padding:.5rem 0;display:block;white-space:nowrap;color:#fff;text-decoration:none;font-weight:400;line-height:30px;overflow:hidden}.off-canvas-sidebar .logo-tim,.sidebar .logo-tim{border-radius:50%;border:1px solid #333;display:block;height:61px;width:61px;float:left;overflow:hidden}.off-canvas-sidebar .logo-tim img,.sidebar .logo-tim img{width:60px;height:60px}.off-canvas-sidebar[data=blue],.sidebar[data=blue]{background:#3358f4;background:linear-gradient(0deg,#3358f4,#1d8cf8)}.off-canvas-sidebar[data=green],.sidebar[data=green]{background:#0098f0;background:linear-gradient(0deg,#0098f0,#00f2c3)}.off-canvas-sidebar .user,.sidebar .user{padding-bottom:20px;margin:20px auto 0;position:relative}.off-canvas-sidebar .user:after,.sidebar .user:after{content:"";position:absolute;bottom:0;right:15px;height:1px;width:calc(100% - 30px);background:hsla(0,0%,100%,.5)}.off-canvas-sidebar .user .photo,.sidebar .user .photo{width:34px;height:34px;overflow:hidden;float:left;z-index:5;margin-right:10px;border-radius:50%;margin-left:23px;box-shadow:0 10px 25px 0 rgba(0,0,0,.3)}.off-canvas-sidebar .user .photo img,.sidebar .user .photo img{width:100%}.off-canvas-sidebar .user a,.sidebar .user a{color:#fff;text-decoration:none;padding:.5rem 15px;white-space:nowrap}.off-canvas-sidebar .user .info>a,.sidebar .user .info>a{display:block;line-height:18px}.off-canvas-sidebar .user .info>a>span,.sidebar .user .info>a>span{display:block;position:relative;opacity:1}.off-canvas-sidebar .user .info .caret,.sidebar .user .info .caret{position:absolute;top:8px;right:18px}.visible-on-sidebar-regular{display:inline-block!important}.visible-on-sidebar-mini{display:none!important}.off-canvas-sidebar .nav>li>a,.off-canvas-sidebar .nav>li>a:hover{color:#fff}.off-canvas-sidebar .nav>li>a:focus{background:hsla(0,0%,78%,.2)}.main-panel{position:relative;float:right;width:100%;min-height:100vh;border-top:2px solid #e14eca;background:linear-gradient(#1e1e2f,#1e1e24);transition:all .5s cubic-bezier(.685,.0473,.346,1)}.main-panel[data=blue]{border-top:2px solid #1d8cf8}.main-panel[data=green]{border-top:2px solid #00f2c3}.main-panel[data=primary]{border-top:2px solid #e14eca}.main-panel>.content{padding:78px 30px 30px 280px;min-height:calc(100vh - 70px)}.main-panel>.navbar{margin-bottom:0}.main-panel .header{margin-bottom:50px}.perfect-scrollbar-on .main-panel,.perfect-scrollbar-on .sidebar{height:100%;max-height:100%}@media (min-width:991px){.main-panel,.sidebar,.sidebar-wrapper{transition-property:top,bottom,width;transition-duration:.2s,.2s,.35s;transition-timing-function:linear,linear,ease;-webkit-overflow-scrolling:touch}.sidebar-mini .visible-on-sidebar-regular{display:none!important}.sidebar-mini .visible-on-sidebar-mini{display:inline-block!important}.sidebar-mini .sidebar{width:80px}.sidebar-mini .sidebar .sidebar-wrapper{width:100%!important}.sidebar-mini .sidebar{display:block;z-index:1030;box-shadow:0 2px 22px 0 rgba(0,0,0,.2),0 2px 30px 0 rgba(0,0,0,.35)}.sidebar-mini .sidebar .logo a.logo-normal,.sidebar-mini .sidebar .sidebar-wrapper .user .info>a>span,.sidebar-mini .sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.sidebar-mini .sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.sidebar-mini .sidebar .sidebar-wrapper>.nav li>a p{opacity:0;transform:translate3d(-25px,0,0)}.sidebar-mini .sidebar .nav [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon{opacity:1}.sidebar-mini .sidebar:hover{width:260px}.sidebar-mini .sidebar:hover .logo a.logo-normal{opacity:1;transform:translateZ(0)}.sidebar-mini .sidebar:hover .navbar-minimize{opacity:1}.sidebar-mini .sidebar:hover .sidebar-wrapper{width:260px}.sidebar-mini .sidebar:hover .sidebar-wrapper .user .info>a>span,.sidebar-mini .sidebar:hover .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.sidebar-mini .sidebar:hover .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.sidebar-mini .sidebar:hover .sidebar-wrapper>.nav li>a p{transform:translateZ(0);opacity:1}.sidebar-mini .sidebar:hover .nav [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon{opacity:0}.sidebar-mini .main-panel>.content,.sidebar-mini footer{padding-left:130px}.navbar-minimize button{margin-left:10px}.navbar-minimize button i{color:#fff;font-size:20px}}.panel-header{height:260px;padding-top:80px;padding-bottom:45px;background:#141e30;background:linear-gradient(90deg,#0c2646 0,#204065 60%,#2a5788);position:relative;overflow:hidden}.panel-header .header .title{color:#fff}.panel-header .header .category{max-width:600px;color:hsla(0,0%,100%,.5);margin:0 auto;font-size:13px}.panel-header .header .category a{color:#fff}.panel-header-sm{height:135px}.panel-header-lg{height:380px}@media screen and (max-width:991px){.sidebar{position:fixed;display:block;top:0;height:100%;width:260px;right:auto;left:0;margin:0;border-radius:0;z-index:1032;visibility:visible;overflow-y:visible;padding:0;transition:.5s cubic-bezier(.685,.0473,.346,1);transform:translate3d(-260px,0,0)}}@media screen and (max-width:991px) and (prefers-reduced-motion:reduce){.sidebar{transition:none}}@media screen and (max-width:991px){.wrapper{transition:.5s,cubic-bezier(.685,.0473,.346,1)}}@media screen and (max-width:991px) and (prefers-reduced-motion:reduce){.wrapper{transition:none}}@media screen and (max-width:991px){.main-panel{width:100%}.main-panel .content{padding-left:30px}.nav-open .main-panel{right:0;transform:translate3d(260px,0,0)}.nav-open .sidebar{transition:.5s cubic-bezier(.685,.0473,.346,1);transform:translateZ(0)}}@media screen and (max-width:991px) and (prefers-reduced-motion:reduce){.nav-open .sidebar{transition:none}}@media screen and (max-width:991px){.nav-open .sidebar:before{content:unset}.nav-open body{position:relative;overflow-x:hidden}.nav-open .menu-on-right .main-panel{transform:translate3d(-260px,0,0)}.nav-open .menu-on-right .navbar-collapse,.nav-open .menu-on-right .sidebar{transform:translateZ(0)}.nav-open .menu-on-right #bodyClick{right:260px;left:auto}.menu-on-right .sidebar{left:auto;right:0;transform:translate3d(260px,0,0)}#bodyClick{height:100%;width:100%;position:fixed;opacity:1;top:0;right:0;left:260px;content:"";z-index:9999;overflow-x:hidden;background-color:transparent;transition:.5s,cubic-bezier(.685,.0473,.346,1)}}@media screen and (max-width:991px) and (prefers-reduced-motion:reduce){#bodyClick{transition:none}}@media screen and (max-width:768px){.main-panel .content{padding-left:15px;padding-right:15px}}.color-swatch{margin:1rem 0;border-radius:.25rem;background-color:#f4f5f7;width:100%!important;height:auto!important;margin-bottom:20px}.color-swatch:after{content:" ";display:table;clear:both}.color-swatch-header{position:relative;height:0;padding-bottom:50%;border-radius:.25rem .25rem 0 0;border:1px solid transparent}.color-swatch-header.is-light{border-color:#c1c7d0}.color-swatch-header .pass-fail{position:absolute;width:100%;bottom:0}.color-swatch-header .pass-fail-item-wrap{position:relative;float:left;left:50%;transform:translateX(-50%)}.color-swatch-header .pass-fail-item-group{display:inline-block;padding:0 5px}.color-swatch-header .pass-fail-item{float:left;display:inline-block;text-align:center;padding:2px}.color-swatch-header .pass-fail-item.white .example{color:#fff}.color-swatch-header .pass-fail-item.small .example{font-size:10px}.color-swatch-header .pass-fail-item .lozenge{font-size:11px;text-transform:uppercase;font-weight:600;background:#000;color:#fff;padding:2px 4px;line-height:10px;border-radius:4px;letter-spacing:.05em}.color-swatch-body{position:relative;left:50%;float:left;padding:10px 0;transform:translateX(-50%)}.color-swatch-body .prop-item-wrap{float:left;padding:0 15px;min-width:65px}.color-swatch-body .prop-item{padding:15px 0}.color-swatch-body .prop-item .label{font-size:11px;color:#62748c;text-transform:uppercase;line-height:16px}.color-swatch-body .prop-item .value{font-size:14px}.table-colors .swatch{display:inline-block;float:left;width:40px;height:40px;margin-right:20px;border:1px solid transparent;border-radius:4px}.table-colors:first-child td:first-child,.table-colors td:first-child{line-height:40px}.table-colors .lozenge{font-size:10px;font-weight:600;line-height:10px;display:inline-block;float:left;margin:5px 10px 0 0;padding:2px 4px;letter-spacing:.05em;text-transform:uppercase;color:#042a53;border-radius:4px;background:#97a0af}.bd-example .row>.col span,.bd-example .row>[class^=col-] span{font-size:.75rem;display:block;margin:1rem 0;padding:.75rem;color:#393f49;background-color:#fff;border-radius:.2857rem}.bd-docs .navbar .navbar-brand{position:relative}.animation-transition-general,.nav-pills .nav-link,.nav-tabs .nav-link,.navbar,.navbar-collapse .navbar-nav .nav-link,.off-canvas-sidebar .logo a.logo-mini,.off-canvas-sidebar .logo a.logo-normal,.off-canvas-sidebar .nav p,.off-canvas-sidebar .user .info>a>span,.off-canvas-sidebar .user .photo,.off-canvas-sidebar .user a,.sidebar .logo a.logo-mini,.sidebar .logo a.logo-normal,.sidebar .nav a,.sidebar .nav a i,.sidebar .nav p,.sidebar .user .info>a>span,.sidebar .user .photo,.sidebar .user a,.tag,.tag [data-role=remove]{transition:all .3s ease 0s}.bootstrap-switch-label:before,.caret{transition:all .15s ease 0s}.card-collapse .card a[data-toggle=collapse].expanded i,.card-collapse .card a[data-toggle=collapse][aria-expanded=true] i,.dropdown-toggle[aria-expanded=true]:after,a[data-toggle=collapse][aria-expanded=true] .caret{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);transform:rotate(180deg)}.caret{width:0;height:0;vertical-align:middle;border-top:4px dashed;border-right:4px solid transparent;border-left:4px solid transparent;margin-top:-5px;position:absolute;top:30px;margin-left:5px}.pull-left{float:left}.pull-right{float:right}.card form label+.form-control{margin-bottom:20px}.card .map-title{color:#fff}.card.card-chart .gm-style-cc,.card.card-chart .gmnoprint{display:none!important}.bd-docs h1,.bd-docs h2,.bd-docs h3,.bd-docs h4,.bd-docs h5,.bd-docs h6,.bd-docs ol li,.bd-docs p,.bd-docs ul li{color:#2c2c2c}.bd-docs .bd-content>table>thead>tr>th{color:#222a42}.bd-docs .blockquote,.bd-docs .blockquote p,.bd-docs .card p{color:hsla(0,0%,100%,.8)}.bd-docs .bd-example{background:linear-gradient(#1e1e2f,#1e1e24)}.bd-docs .navbar{border-top:none}.bd-docs .navbar .navbar-nav .nav-link{color:hsla(0,0%,100%,.8)!important}.bd-docs .bd-example .btn{margin:4px 0}.bd-docs .bd-example .btn .badge{display:inline-block}.bd-docs .bd-example .tim-icons{color:#fff}.bd-docs .bd-example .popover .popover-header{color:hsla(0,0%,71%,.6)}.bd-docs .bd-example .popover-body p{color:#212529}.bd-docs .bd-example.tooltip-demo p,.bd-docs .card.card-body,.bd-docs .card .card-body,.bd-docs .form-check,.bd-docs label{color:hsla(0,0%,100%,.8)}.bd-docs .form-check+.btn{margin-top:20px}.bd-docs .bd-example .h1,.bd-docs .bd-example .h2,.bd-docs .bd-example .h3,.bd-docs .bd-example .h4,.bd-docs .bd-example .h5,.bd-docs .bd-example .h6,.bd-docs .bd-example h1,.bd-docs .bd-example h2,.bd-docs .bd-example h3,.bd-docs .bd-example h4,.bd-docs .bd-example h5,.bd-docs .bd-example h6,.bd-docs .bd-example thead th,.bd-docs table .h1,.bd-docs table .h2,.bd-docs table .h3,.bd-docs table .h4,.bd-docs table .h5,.bd-docs table .h6,.bd-docs table h1,.bd-docs table h2,.bd-docs table h3,.bd-docs table h4,.bd-docs table h5,.bd-docs table h6,.bd-docs table thead th{color:hsla(0,0%,100%,.8)}.bd-docs .bd-example .datepicker .tim-icons,.bd-docs .bd-example .datepicker table thead th,.bd-docs .bd-example .datepicker thead th,.bd-docs .bd-example .picker-switch .tim-icons,.bd-docs table .datepicker .tim-icons,.bd-docs table .datepicker table thead th,.bd-docs table .datepicker thead th,.bd-docs table .picker-switch .tim-icons{color:#e14eca}.bd-docs .footer .container-fluid>nav{display:inline-block}.modal.show .modal-dialog{transform:translateY(30%)}code{color:#f3a4b5}@media screen and (max-width:991px){.profile-photo .profile-photo-small{margin-left:-2px}.button-dropdown{display:none}#searchModal .modal-dialog{margin:20px}#minimizeSidebar{display:none}}@media screen and (max-width:768px){.landing-page .section-story-overview .image-container:nth-child(2){margin-left:0;margin-bottom:30px}}@media screen and (max-width:576px){.page-header .container h6.category-absolute{width:90%}.form-horizontal .col-form-label,.form-horizontal .label-on-right{text-align:inherit;padding-top:0}.form-horizontal .col-form-label code,.form-horizontal .label-on-right code{padding:0 10px}}.rtl .bootstrap-navbar,.rtl .sidebar{right:0;left:auto;margin-right:20px;margin-left:0}.rtl .bootstrap-navbar .nav i,.rtl .sidebar .nav i{float:right;margin-left:15px;margin-right:0}.rtl .bootstrap-navbar .nav p,.rtl .sidebar .nav p{margin-right:45px;text-align:right}.rtl .bootstrap-navbar .nav .caret,.rtl .sidebar .nav .caret{left:11px;right:auto}.rtl .bootstrap-navbar .logo a.logo-mini,.rtl .sidebar .logo a.logo-mini{float:right;margin-right:20px;margin-left:10px}.rtl .bootstrap-navbar .logo .simple-text,.rtl .sidebar .logo .simple-text{text-align:right}.rtl .bootstrap-navbar .sidebar-wrapper .nav [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon,.rtl .bootstrap-navbar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon,.rtl .sidebar .sidebar-wrapper .nav [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon,.rtl .sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-mini-icon{float:right;margin-left:15px;margin-right:0}.rtl .bootstrap-navbar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.rtl .bootstrap-navbar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.rtl .sidebar .sidebar-wrapper .user .info [data-toggle=collapse]~div>ul>li>a .sidebar-normal,.rtl .sidebar .sidebar-wrapper>.nav [data-toggle=collapse]~div>ul>li>a .sidebar-normal{text-align:right}.rtl .bootstrap-navbar:before,.rtl .sidebar:before{right:30px;left:auto}.rtl .main-panel .content{padding:80px 280px 30px 30px}.rtl .footer{padding:24px 300px 24px 0}.rtl .dropdown-toggle:after{margin-right:.255em;margin-left:0}.rtl .dropdown-menu.dropdown-menu-right.dropdown-navbar{right:-220px!important;left:auto}.rtl .dropdown-menu.dropdown-menu-right.dropdown-navbar:before{right:auto;left:35px}.rtl .notification{left:40px;right:auto}.rtl .dropdown-menu{right:auto;left:0}.rtl .minimize-sidebar{float:right}.rtl .alert{left:0;margin-left:0;margin-right:0}.rtl .alert button.close{left:10px!important;right:auto!important}.rtl .alert span[data-notify=icon]{right:15px;left:auto}.rtl .alert.alert-with-icon{padding-right:65px;padding-left:15px}.rtl .alert.alert-with-icon i[data-notify=icon]{right:15px;left:auto}.rtl .search-bar{margin-left:0}.rtl .modal-search .modal-header .close{margin-right:auto;left:10px}@media (min-width:991px){.rtl.sidebar-mini .main-panel .content,.rtl.sidebar-mini footer{padding-right:130px;padding-left:50px}.rtl .navbar-minimize button{margin-right:-5px}}@media screen and (max-width:991px){.rtl .sidebar{margin-right:0}.rtl .main-panel .content{padding-right:50px}.rtl #bodyClick{right:260px;left:auto}.rtl .footer{padding-right:15px}}.rtl .navbar .navbar-nav{padding-right:0}.rtl .navbar .navbar-nav a.nav-link{text-align:right}.rtl .navbar .navbar-nav a.nav-link p{margin-right:7px}.rtl .navbar .navbar-nav .btn{margin-right:0;padding:0}.rtl .navbar .navbar-nav .btn i{margin-left:4px;margin-right:5px}.rtl .navbar .navbar-nav .search-bar span{margin-right:10px}.rtl .ps__rail-y{right:auto!important;left:0}.rtl .main-panel{position:fixed;height:100%;overflow-y:scroll;overflow-x:hidden}@media screen and (max-width:768px){.rtl .main-panel .content{padding-left:15px;padding-right:15px}}.form-group.has-danger .error,.input-group.has-danger .error{color:#ec250d}.form-group.has-success .error,.input-group.has-success .error{color:#00f2c3}.form-group.no-border.form-control-lg .input-group-append .input-group-text,.input-group.no-border.form-control-lg .input-group-append .input-group-text{padding:15px 0 15px 19px}.form-group.no-border.form-control-lg .form-control,.input-group.no-border.form-control-lg .form-control{padding:15px 19px}.form-group.no-border.form-control-lg .form-control+.input-group-append .input-group-text,.form-group.no-border.form-control-lg .form-control+.input-group-prepend .input-group-text,.input-group.no-border.form-control-lg .form-control+.input-group-append .input-group-text,.input-group.no-border.form-control-lg .form-control+.input-group-prepend .input-group-text{padding:15px 19px 15px 0}.form-group.form-control-lg .form-control,.input-group.form-control-lg .form-control{padding:14px 18px;height:100%}.form-group.form-control-lg .form-control+.input-group-append .input-group-text,.form-group.form-control-lg .form-control+.input-group-prepend .input-group-text,.input-group.form-control-lg .form-control+.input-group-append .input-group-text,.input-group.form-control-lg .form-control+.input-group-prepend .input-group-text{padding:14px 18px 14px 0}.form-group.form-control-lg .input-group-append .input-group-text,.form-group.form-control-lg .input-group-prepend .input-group-text,.input-group.form-control-lg .input-group-append .input-group-text,.input-group.form-control-lg .input-group-prepend .input-group-text{padding:14px 0 15px 18px}.form-group.form-control-lg .input-group-append .input-group-text+.form-control,.form-group.form-control-lg .input-group-prepend .input-group-text+.form-control,.input-group.form-control-lg .input-group-append .input-group-text+.form-control,.input-group.form-control-lg .input-group-prepend .input-group-text+.form-control{padding:15px 18px 15px 16px}.form-group.no-border .form-control,.input-group.no-border .form-control{padding:11px 19px}.form-group.no-border .form-control+.input-group-append .input-group-text,.form-group.no-border .form-control+.input-group-prepend .input-group-text,.input-group.no-border .form-control+.input-group-append .input-group-text,.input-group.no-border .form-control+.input-group-prepend .input-group-text{padding:11px 19px 11px 0}.form-group.no-border .input-group-append .input-group-text,.form-group.no-border .input-group-prepend .input-group-text,.input-group.no-border .input-group-append .input-group-text,.input-group.no-border .input-group-prepend .input-group-text{padding:11px 0 11px 19px}.form-group .form-control,.input-group .form-control{padding:10px 18px}.form-group .form-control+.input-group-append .input-group-text,.form-group .form-control+.input-group-prepend .input-group-text,.input-group .form-control+.input-group-append .input-group-text,.input-group .form-control+.input-group-prepend .input-group-text{padding:10px 18px 10px 0}.form-group .input-group-append .input-group-text,.form-group .input-group-prepend .input-group-text,.input-group .input-group-append .input-group-text,.input-group .input-group-prepend .input-group-text{padding:10px 0 10px 18px}.form-group .input-group-append .input-group-text+.form-control,.form-group .input-group-append .input-group-text~.form-control,.form-group .input-group-prepend .input-group-text+.form-control,.form-group .input-group-prepend .input-group-text~.form-control,.input-group .input-group-append .input-group-text+.form-control,.input-group .input-group-append .input-group-text~.form-control,.input-group .input-group-prepend .input-group-text+.form-control,.input-group .input-group-prepend .input-group-text~.form-control{padding:10px 19px 11px 16px}.form-group.no-border .form-control,.form-group.no-border .form-control+.input-group-append .input-group-text,.form-group.no-border .form-control+.input-group-prepend .input-group-text,.input-group.no-border .form-control,.input-group.no-border .form-control+.input-group-append .input-group-text,.input-group.no-border .form-control+.input-group-prepend .input-group-text{background-color:#242c45;border:medium none}.form-group.no-border .form-control+.input-group-append .input-group-text:active,.form-group.no-border .form-control+.input-group-append .input-group-text:focus,.form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.form-group.no-border .form-control:active,.form-group.no-border .form-control:focus,.input-group.no-border .form-control+.input-group-append .input-group-text:active,.input-group.no-border .form-control+.input-group-append .input-group-text:focus,.input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.input-group.no-border .form-control:active,.input-group.no-border .form-control:focus{border:medium none;background-color:#252e49}.form-group.no-border .form-control:focus+.input-group-append .input-group-text,.form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.input-group.no-border .form-control:focus+.input-group-append .input-group-text,.input-group.no-border .form-control:focus+.input-group-prepend .input-group-text{background-color:#252e49}.form-group.no-border .input-group-append .input-group-text,.form-group.no-border .input-group-prepend .input-group-text,.input-group.no-border .input-group-append .input-group-text,.input-group.no-border .input-group-prepend .input-group-text{background-color:#242c45;border:none}.has-error .control-label,.has-error .form-control-feedback{color:#ec250d}.has-success .control-label,.has-success .form-control-feedback{color:#00f2c3}.input-group-append .input-group-text,.input-group-prepend .input-group-text{background-color:transparent;border:1px solid #2b3553;border-radius:.4285rem;color:#fff;transition:color .3s ease-in-out,border-color .3s ease-in-out,background-color .3s ease-in-out}.input-group-append .input-group-text i,.input-group-prepend .input-group-text i{opacity:.5}.has-danger.input-group-focus .input-group-append .input-group-text,.has-danger.input-group-focus .input-group-prepend .input-group-text,.has-success .input-group-append .input-group-text,.has-success .input-group-prepend .input-group-text{background-color:transparent}.has-danger .form-control:focus+.input-group-append .input-group-text,.has-danger .form-control:focus+.input-group-prepend .input-group-text{color:#ec250d}.has-success .form-control:focus+.input-group-append .input-group-text,.has-success .form-control:focus+.input-group-prepend .input-group-text{color:#00f2c3}.input-group-append .input-group-text+.form-control,.input-group-append .input-group-text~.form-control,.input-group-prepend .input-group-text+.form-control,.input-group-prepend .input-group-text~.form-control{padding:-.5rem .7rem;padding-left:18px}.input-group-append .input-group-text i,.input-group-prepend .input-group-text i{width:17px}.input-group-append,.input-group-prepend .input-group-text{background-color:transparent;border:1px solid #2b3553;border-radius:.4285rem;color:#fff;margin:0}.input-group-append .input-group-text{border-left:none}.input-group-prepend .input-group-text{border-right:none}.input-group-focus .input-group-append .input-group-text,.input-group-focus .input-group-prepend .input-group-text{background-color:#fff;background-color:transparent;border-color:#e14eca}.input-group-focus.no-border .input-group-append .input-group-text,.input-group-focus.no-border .input-group-prepend .input-group-text{background-color:#252e49}.form-group,.input-group{margin-bottom:10px;position:relative}.form-group .form-control-static,.input-group .form-control-static{margin-top:9px}.input-group[disabled] .input-group-append .input-group-text,.input-group[disabled] .input-group-prepend .input-group-text{background-color:#e3e3e3}.input-group-btn:not(:first-child):not(:last-child),.input-group .form-control:not(:first-child):not(:last-child){border-radius:.4285rem;border-top-left-radius:0;border-bottom-left-radius:0;border-left:0 none}.input-group-btn:first-child>.dropdown-toggle,.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group .form-control:first-child{border-right:0 none}.input-group-btn:first-child>.btn:not(:first-child),.input-group-btn:last-child>.dropdown-toggle,.input-group .form-control:last-child{border-left:0 none}.form-control[disabled],.form-control[readonly],fieldset[disabled] .form-control{background-color:#1d253b;color:#344675;cursor:not-allowed}.input-group-btn .btn{border-width:1px;padding:11px .7rem}.input-group-btn .btn-default:not(.btn-fill){border-color:#ddd}.input-group-btn:last-child>.btn{margin-left:0}textarea.form-control{max-width:100%;max-height:80px;padding:10px 10px 0 0;resize:none;border:none;border-bottom:1px solid #2b3553;border-radius:0;line-height:2}textarea.form-control:active,textarea.form-control:focus{border-left:none;border-top:none;border-right:none}.has-danger.form-group .form-control,.has-danger.form-group.no-border .form-control,.has-success.form-group .form-control,.has-success.form-group.no-border .form-control{padding-right:40px}.form.form-newsletter .form-group{float:left;width:78%;margin-right:2%;margin-top:9px}.input-group .input-group-btn{padding:0 12px}.form-group input[type=file]{opacity:0;position:absolute;top:0;right:0;bottom:0;left:0;width:100%;height:100%;z-index:100}.form-text{font-size:.75rem;color:hsla(0,0%,100%,.8)}.form-control-lg{padding:0;font-size:inherit;line-height:0;border-radius:0}.form-control-lg .form-control{height:calc(2.875rem + 2px)}.form-horizontal .col-form-label,.form-horizontal .label-on-right{padding:10px 5px 0 15px;text-align:right;max-width:180px}.form-horizontal .checkbox-radios{margin-bottom:15px}.form-horizontal .checkbox-radios .form-check:first-child{margin-top:8px}.form-horizontal .label-on-right{text-align:left;padding:10px 15px 0 5px}.form-horizontal .form-check-inline{margin-top:6px}.form-horizontal .form-check-inline .form-check-label{margin-right:1.5rem}.search-bar{margin-left:30px}.search-bar .btn{margin:0}.search-bar.input-group{border-radius:25px;z-index:4;margin-bottom:0;height:43px;padding-right:5px}.search-bar.input-group .input-group-addon{padding:10px;background:transparent;border:none;color:hsla(0,0%,100%,.7)}.search-bar.input-group i{font-size:20px;color:#fff;margin-top:0!important}.search-bar.input-group input{background:transparent;border:none!important;border-radius:0;padding:12px!important;font-size:12px;opacity:.5}.search-bar.input-group input:focus{background:transparent}.search-bar.input-group .form-control{opacity:1;color:#fff}.search-bar.input-group .form-control::placeholder{color:#fff}.modal-search .modal-dialog{max-width:1000px;margin:20px auto}.modal-search .modal-dialog .form-control{border:none;color:#222a42}.modal-search .modal-dialog .form-control::placeholder{color:#222a42}.input-group-prepend{margin-right:0}.input-group-append .tim-icons,.input-group-prepend .tim-icons{font-size:1rem}.ps{overflow:hidden!important;overflow-anchor:none;-ms-overflow-style:none;touch-action:auto;-ms-touch-action:auto}.ps__rail-x{height:15px;bottom:0}.ps__rail-x,.ps__rail-y{display:none;opacity:0;transition:background-color .2s linear,opacity .2s linear;-webkit-transition:background-color .2s linear,opacity .2s linear;position:absolute}.ps__rail-y{width:15px;right:0}.ps--active-x>.ps__rail-x,.ps--active-y>.ps__rail-y{display:block;background-color:transparent}.ps--focus>.ps__rail-x,.ps--focus>.ps__rail-y,.ps--scrolling-x>.ps__rail-x,.ps--scrolling-y>.ps__rail-y,.ps:hover>.ps__rail-x,.ps:hover>.ps__rail-y{opacity:.6}.ps .ps__rail-x.ps--clicking,.ps .ps__rail-x:focus,.ps .ps__rail-x:hover,.ps .ps__rail-y.ps--clicking,.ps .ps__rail-y:focus,.ps .ps__rail-y:hover{background-color:#eee;opacity:.9}.ps__thumb-x{transition:background-color .2s linear,height .2s ease-in-out;-webkit-transition:background-color .2s linear,height .2s ease-in-out;height:6px;bottom:2px}.ps__thumb-x,.ps__thumb-y{background-color:#aaa;border-radius:6px;position:absolute}.ps__thumb-y{transition:background-color .2s linear,width .2s ease-in-out;-webkit-transition:background-color .2s linear,width .2s ease-in-out;width:6px;right:2px}.ps__rail-x.ps--clicking .ps__thumb-x,.ps__rail-x:focus>.ps__thumb-x,.ps__rail-x:hover>.ps__thumb-x{background-color:#999;height:11px}.ps__rail-y.ps--clicking .ps__thumb-y,.ps__rail-y:focus>.ps__thumb-y,.ps__rail-y:hover>.ps__thumb-y{background-color:#999;width:11px}@supports (-ms-overflow-style:none){.ps{overflow:auto!important}}@media (-ms-high-contrast:none),screen and (-ms-high-contrast:active){.ps{overflow:auto!important}}

  /*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license - http://opensource.org/licenses/MIT

Copyright (c) 2015 Daniel Eden
*/.animated {
  animation-duration: 1s;
  animation-fill-mode: both
}

.animated.infinite {
  animation-iteration-count: infinite
}

.animated.hinge {
  animation-duration: 2s
}

.animated.bounceIn,.animated.bounceOut,.animated.flipOutX,.animated.flipOutY {
  animation-duration: .75s
}

@keyframes f {
  0%,to {
    transform: translateZ(0)
  }

  10%,30%,50%,70%,90% {
    transform: translate3d(-10px,0,0)
  }

  20%,40%,60%,80% {
    transform: translate3d(10px,0,0)
  }}

.shake {
  animation-name: f
}

@keyframes g {
  0% {
    opacity: 0;
    transform: translate3d(0,-100%,0)
  }

  to {
    opacity: 1;
    transform: none
  }}

.fadeInDown {
  animation-name: g
}

@keyframes h {
  0% {
    opacity: 1
  }

  to {
    opacity: 0
  }}

.fadeOut {
  animation-name: h
}

@keyframes i {
  0% {
    opacity: 1
  }

  to {
    opacity: 0;
    transform: translate3d(0,100%,0)
  }}

.fadeOutDown {
  animation-name: i
}

@keyframes j {
  0% {
    opacity: 1
  }

  to {
    opacity: 0;
    transform: translate3d(0,-100%,0)
  }}

.fadeOutUp {
  animation-name: j
}

.white-content {
  background: #f5f6fa
}

.white-content .navbar.navbar-transparent .navbar-brand {
  color: #1d253b
}

.white-content .navbar.navbar-transparent .navbar-toggler-bar {
  background: #1d253b
}

.white-content .navbar.navbar-transparent .navbar-minimize button i,.white-content .navbar.navbar-transparent .navbar-nav li a:not(.dropdown-item),.white-content .navbar.navbar-transparent .navbar-nav li a:not(.dropdown-item) i,.white-content .navbar.navbar-transparent .search-bar.input-group i {
  color: #1d253b
}

.white-content .navbar.navbar-transparent .search-bar.input-group .form-control {
  color: #344675
}

.white-content .navbar.navbar-transparent .search-bar.input-group .form-control::placeholder {
  color: #9a9a9a
}

.white-content .sidebar {
  box-shadow: 0 2px 22px 0 rgba(0,0,0,.1),0 4px 20px 0 rgba(0,0,0,.15)
}

.white-content .sidebar p {
  color: hsla(0,0%,100%,.8)
}

.white-content .main-panel {
  background: #f5f6fa
}

.white-content h1,.white-content h2,.white-content h3,.white-content h4,.white-content h5,.white-content h6,.white-content ol li,.white-content p,.white-content pre,.white-content ul li {
  color: #1d253b
}

.white-content .font-icon-detail i {
  color: #222a42
}

.white-content .btn:not([data-action]):hover {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.white-content .btn-neutral.btn-link {
  color: rgba(34,42,66,.7)
}

.white-content .form-control:-moz-placeholder,.white-content .form-control::-moz-placeholder {
  color: rgba(34,42,66,.4);
  opacity: 1;
  filter: alpha(opacity=100)
}

.white-content .form-control::-webkit-input-placeholder {
  color: rgba(34,42,66,.4);
  opacity: 1;
  filter: alpha(opacity=100)
}

.white-content .form-control:-ms-input-placeholder {
  color: rgba(34,42,66,.4);
  opacity: 1;
  filter: alpha(opacity=100)
}

.white-content .has-danger .form-control,.white-content .has-danger .input-group-prepend .input-group-text {
  border-color: #ec250d
}

.white-content .input-group-prepend .input-group-text {
  border-color: rgba(29,37,59,.5);
  color: #1d253b
}

.white-content .form-control {
  color: #222a42;
  border-color: rgba(29,37,59,.5)
}

.white-content .form-control:focus {
  border-color: #e14eca
}

.white-content .form-group.no-border .form-control,.white-content .form-group.no-border .form-control+.input-group-append .input-group-text,.white-content .form-group.no-border .form-control+.input-group-prepend .input-group-text,.white-content .form-group.no-border .input-group-append .input-group-text,.white-content .form-group.no-border .input-group-prepend .input-group-text,.white-content .input-group.no-border .form-control,.white-content .input-group.no-border .form-control+.input-group-append .input-group-text,.white-content .input-group.no-border .form-control+.input-group-prepend .input-group-text,.white-content .input-group.no-border .input-group-append .input-group-text,.white-content .input-group.no-border .input-group-prepend .input-group-text {
  background-color: hsla(0,0%,87%,.3)
}

.white-content .form-group.no-border .form-control+.input-group-append .input-group-text:active,.white-content .form-group.no-border .form-control+.input-group-append .input-group-text:focus,.white-content .form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.white-content .form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.white-content .form-group.no-border .form-control:active,.white-content .form-group.no-border .form-control:focus,.white-content .form-group.no-border .input-group-append .input-group-text:active,.white-content .form-group.no-border .input-group-append .input-group-text:focus,.white-content .form-group.no-border .input-group-prepend .input-group-text:active,.white-content .form-group.no-border .input-group-prepend .input-group-text:focus,.white-content .input-group.no-border .form-control+.input-group-append .input-group-text:active,.white-content .input-group.no-border .form-control+.input-group-append .input-group-text:focus,.white-content .input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.white-content .input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.white-content .input-group.no-border .form-control:active,.white-content .input-group.no-border .form-control:focus,.white-content .input-group.no-border .input-group-append .input-group-text:active,.white-content .input-group.no-border .input-group-append .input-group-text:focus,.white-content .input-group.no-border .input-group-prepend .input-group-text:active,.white-content .input-group.no-border .input-group-prepend .input-group-text:focus {
  background-color: hsla(0,0%,87%,.5)
}

.white-content .form-group.no-border .form-control:focus+.input-group-append .input-group-text,.white-content .form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.white-content .input-group.no-border .form-control:focus+.input-group-append .input-group-text,.white-content .input-group.no-border .form-control:focus+.input-group-prepend .input-group-text {
  background-color: transparent
}

.white-content .input-group[disabled] .input-group-append .input-group-text,.white-content .input-group[disabled] .input-group-prepend .input-group-text {
  background-color: #222a42
}

.white-content .form-control[disabled],.white-content .form-control[readonly],.white-content fieldset[disabled] .form-control {
  background: #e3e3e3;
  border-color: rgba(29,37,59,.3)
}

.white-content .input-group-focus .form-control,.white-content .input-group-focus .input-group-append .input-group-text,.white-content .input-group-focus .input-group-prepend .input-group-text {
  border-color: #e14eca
}

.white-content .input-group-focus.no-border .input-group-append .input-group-text,.white-content .input-group-focus.no-border .input-group-prepend .input-group-text {
  background-color: hsla(0,0%,87%,.5)
}

.white-content .input-group-prepend .input-group-text {
  border-right: none
}

.white-content .input-group-append .input-group-text {
  border-left: none
}

.white-content .has-danger .form-control:focus,.white-content .has-success.input-group-focus .input-group-append .input-group-text,.white-content .has-success.input-group-focus .input-group-prepend .input-group-text {
  border-color: #ec250d
}

.white-content .has-success .form-control:focus,.white-content .has-success.input-group-focus .input-group-append .input-group-text,.white-content .has-success.input-group-focus .input-group-prepend .input-group-text {
  border-color: #00bf9a
}

.white-content .btn.btn-link:active,.white-content .btn.btn-link:focus,.white-content .btn.btn-link:hover {
  color: #9a9a9a!important
}

.white-content .btn-group .btn.active {
  box-shadow: 0 4px 6px rgba(50,50,93,.11),0 1px 3px rgba(0,0,0,.08)
}

.white-content .card:not(.card-white) {
  background: #fff;
  box-shadow: 0 1px 15px 0 hsla(0,0%,48%,.05)
}

.white-content .card:not(.card-white) .card-header,.white-content .card:not(.card-white) .card-header a[data-toggle=collapse] {
  color: #222a42
}

.white-content .card:not(.card-white) .card-body .card-category,.white-content .card:not(.card-white) .card-body .card-description,.white-content .card:not(.card-white) .card-body .card-title,.white-content .card:not(.card-white) .card-header .card-title {
  color: #1d253b
}

.white-content .card:not(.card-white) label:not(.btn) {
  color: #344675
}

.white-content .card:not(.card-white).nav-pills .nav-item .nav-link {
  color: #444;
  background-color: hsla(0,0%,87%,.3)
}

.white-content .card:not(.card-white).nav-pills .nav-item .nav-link:not(.active):hover {
  background: hsla(0,0%,87%,.5)
}

.white-content .card:not(.card-white).nav-pills .nav-item .nav-link.active {
  color: #fff
}

.white-content .card:not(.card-white) .tab-content .tab-pane {
  color: #444
}

.white-content .card:not(.card-white) .card {
  box-shadow: none
}

.white-content .card:not(.card-white).card-plain {
  background: transparent;
  box-shadow: none
}

.white-content .card:not(.card-white).card-tasks .card-body i {
  color: rgba(34,42,66,.7)
}

.white-content .card:not(.card-white).card-tasks .card-body i:hover {
  color: #222a42
}

.white-content .table>tbody>tr>td {
  color: rgba(34,42,66,.7)!important
}

.white-content .table>tbody>tr>td,.white-content .table>tbody>tr>th,.white-content .table>tfoot>tr>td,.white-content .table>tfoot>tr>th,.white-content .table>thead>tr>td,.white-content .table>thead>tr>th {
  border-color: rgba(34,42,66,.2);
  padding: 12px 7px;
  vertical-align: middle
}

.white-content .table>thead>tr>th,.white-content .table button.btn-neutral.btn-link {
  color: rgba(34,42,66,.7)
}

.white-content .footer .copyright,.white-content .footer ul li a {
  color: #1d253b
}

.white-content .progress-container .progress,.white-content .progress-container.progress.sm .progress {
  background: rgba(34,42,66,.1);
  box-shadow: 0 0 0 3px rgba(34,42,66,.1)
}

.white-content .progress-container .progress .progress-value,.white-content .progress-container.progress.sm .progress .progress-value {
  color: #344675
}

.white-content .progress-container .progress-badge,.white-content .progress-container.progress.sm .progress-badge {
  color: #1d253b
}

.white-content .full-page {
  background: #f5f6fa
}

.white-content .full-page h1,.white-content .full-page h2,.white-content .full-page h3,.white-content .full-page h4,.white-content .full-page h5,.white-content .full-page h6,.white-content .full-page ol li,.white-content .full-page p,.white-content .full-page pre,.white-content .full-page ul li {
  color: #1d253b
}

.white-content .full-page .description {
  color: #9a9a9a
}

.white-content .full-page .footer .copyright,.white-content .full-page .footer ul li a {
  color: #1d253b
}

.white-content .nav-pills .nav-item .nav-link:not(.active) {
  background: #d3d7e9;
  color: #1d253b
}

.white-content .nav-pills .nav-item .nav-link:not(.active):hover {
  background: #ccc
}

.card {
  background: #27293d;
  border: 0;
  position: relative;
  width: 100%;
  margin-bottom: 30px;
  box-shadow: 0 1px 20px 0 rgba(0,0,0,.1)
}

.card label {
  color: hsla(0,0%,100%,.6)
}

.card .card-title {
  margin-bottom: .75rem
}

.card .card-body {
  padding: 15px
}

.card .card-body.table-full-width {
  padding-left: 0;
  padding-right: 0
}

.card .card-body .card-title {
  color: #fff;
  text-transform: inherit;
  font-weight: 300;
  margin-bottom: .75rem
}

.card .card-body .card-category,.card .card-body .card-description {
  color: hsla(0,0%,100%,.6)
}

.card .card-header {
  padding: 15px 15px 0;
  border: 0;
  color: hsla(0,0%,100%,.8)
}

.card .card-header:not([data-background-color]) {
  background-color: transparent
}

.card .card-header .card-title {
  color: #fff;
  font-weight: 100
}

.card .card-header .card-category {
  color: #9a9a9a;
  margin-bottom: 5px;
  font-weight: 300
}

.card .map {
  border-radius: .2857rem
}

.card .map.map-big {
  height: 420px
}

.card.card-white {
  background: #fff
}

.card.card-white .card-title {
  color: #222a42
}

.card.card-white .card-category,.card.card-white .stats {
  color: gray
}

.card.card-white .form-control:-moz-placeholder,.card.card-white .form-control::-moz-placeholder {
  color: rgba(34,42,66,.4);
  opacity: 1;
  filter: alpha(opacity=100)
}

.card.card-white .form-control::-webkit-input-placeholder {
  color: rgba(34,42,66,.4);
  opacity: 1;
  filter: alpha(opacity=100)
}

.card.card-white .form-control:-ms-input-placeholder {
  color: rgba(34,42,66,.4);
  opacity: 1;
  filter: alpha(opacity=100)
}

.card.card-white .has-danger .form-control,.card.card-white .has-danger .input-group-prepend .input-group-text {
  border-color: #ec250d
}

.card.card-white .input-group-prepend .input-group-text {
  border-color: rgba(29,37,59,.2);
  color: #1d253b
}

.card.card-white .form-control {
  color: #222a42;
  border-color: rgba(29,37,59,.2)
}

.card.card-white .form-control:focus {
  border-color: #e14eca
}

.card.card-white label:not(.btn) {
  color: #344675
}

.card.card-white .form-group.no-border .form-control,.card.card-white .form-group.no-border .form-control+.input-group-append .input-group-text,.card.card-white .form-group.no-border .form-control+.input-group-prepend .input-group-text,.card.card-white .form-group.no-border .input-group-append .input-group-text,.card.card-white .form-group.no-border .input-group-prepend .input-group-text,.card.card-white .input-group.no-border .form-control,.card.card-white .input-group.no-border .form-control+.input-group-append .input-group-text,.card.card-white .input-group.no-border .form-control+.input-group-prepend .input-group-text,.card.card-white .input-group.no-border .input-group-append .input-group-text,.card.card-white .input-group.no-border .input-group-prepend .input-group-text {
  background-color: hsla(0,0%,87%,.3)
}

.card.card-white .form-group.no-border .form-control+.input-group-append .input-group-text:active,.card.card-white .form-group.no-border .form-control+.input-group-append .input-group-text:focus,.card.card-white .form-group.no-border .form-control+.input-group-prepend .input-group-text:active,.card.card-white .form-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.card.card-white .form-group.no-border .form-control:active,.card.card-white .form-group.no-border .form-control:focus,.card.card-white .form-group.no-border .input-group-append .input-group-text:active,.card.card-white .form-group.no-border .input-group-append .input-group-text:focus,.card.card-white .form-group.no-border .input-group-prepend .input-group-text:active,.card.card-white .form-group.no-border .input-group-prepend .input-group-text:focus,.card.card-white .input-group.no-border .form-control+.input-group-append .input-group-text:active,.card.card-white .input-group.no-border .form-control+.input-group-append .input-group-text:focus,.card.card-white .input-group.no-border .form-control+.input-group-prepend .input-group-text:active,.card.card-white .input-group.no-border .form-control+.input-group-prepend .input-group-text:focus,.card.card-white .input-group.no-border .form-control:active,.card.card-white .input-group.no-border .form-control:focus,.card.card-white .input-group.no-border .input-group-append .input-group-text:active,.card.card-white .input-group.no-border .input-group-append .input-group-text:focus,.card.card-white .input-group.no-border .input-group-prepend .input-group-text:active,.card.card-white .input-group.no-border .input-group-prepend .input-group-text:focus {
  background-color: hsla(0,0%,87%,.5)
}

.card.card-white .form-group.no-border .form-control:focus+.input-group-append .input-group-text,.card.card-white .form-group.no-border .form-control:focus+.input-group-prepend .input-group-text,.card.card-white .input-group.no-border .form-control:focus+.input-group-append .input-group-text,.card.card-white .input-group.no-border .form-control:focus+.input-group-prepend .input-group-text {
  background-color: transparent
}

.card.card-white .input-group[disabled] .input-group-append .input-group-text,.card.card-white .input-group[disabled] .input-group-prepend .input-group-text {
  background-color: #222a42
}

.card.card-white .form-control[disabled],.card.card-white .form-control[readonly],.card.card-white fieldset[disabled] .form-control {
  background: #e3e3e3;
  border-color: rgba(29,37,59,.3)
}

.card.card-white .input-group-focus .form-control,.card.card-white .input-group-focus .input-group-append .input-group-text,.card.card-white .input-group-focus .input-group-prepend .input-group-text {
  background-color: #fff;
  border-color: #e14eca
}

.card.card-white .input-group-focus.no-border .input-group-append .input-group-text,.card.card-white .input-group-focus.no-border .input-group-prepend .input-group-text {
  background-color: hsla(0,0%,87%,.5)
}

.card.card-white .input-group-prepend .input-group-text {
  border-right: none
}

.card.card-white .input-group-append .input-group-text {
  border-left: none
}

.card.card-white .has-danger .form-control:focus,.card.card-white .has-success.input-group-focus .input-group-append .input-group-text,.card.card-white .has-success.input-group-focus .input-group-prepend .input-group-text {
  border-color: #ec250d
}

.card.card-white .has-success .form-control:focus,.card.card-white .has-success.input-group-focus .input-group-append .input-group-text,.card.card-white .has-success.input-group-focus .input-group-prepend .input-group-text {
  border-color: #00bf9a
}

.card.card-plain {
  background: transparent;
  box-shadow: none
}

.card .image {
  overflow: hidden;
  height: 200px;
  position: relative
}

.card .avatar {
  width: 30px;
  height: 30px;
  overflow: hidden;
  border-radius: 50%;
  margin-bottom: 15px
}

.card label {
  font-size: .75rem;
  margin-bottom: 5px
}

.card .card-footer {
  background-color: transparent;
  border: 0;
  padding: 15px
}

.card .card-footer .stats i {
  margin-right: 5px;
  position: relative
}

.card .card-footer h6 {
  margin-bottom: 0;
  padding: 7px 0
}

.card-body {
  padding: 1.25rem
}

@media (max-width:767.98px) {
  .card.card-chart .card-header .btn-group-toggle .tim-icons {
    font-size: .875rem;
    top: -1px
  }}

.card-chart {
  overflow: hidden
}

.card-chart .card-header .card-title i {
  font-size: 16px;
  margin-right: 5px;
  margin-bottom: 3px
}

.card-chart .card-header .card-category {
  margin-bottom: 5px
}

.card-chart .card-body {
  padding-left: 5px;
  padding-right: 5px
}

.card-chart .card-body .tab-space {
  padding: 0
}

.card-chart .table {
  margin-bottom: 0
}

.card-chart .table td {
  border-top: none;
  border-bottom: 1px solid hsla(0,0%,100%,.1)
}

.card-chart .card-progress {
  margin-top: 30px;
  padding: 0 10px
}

.card-chart .chart-area {
  height: 220px;
  width: 100%
}

.card-chart .card-footer {
  margin-top: 15px
}

.card-chart .card-footer .stats {
  color: #9a9a9a
}

.card-chart .dropdown {
  position: absolute;
  right: 20px;
  top: 20px
}

.card-chart .dropdown .btn {
  margin: 0
}

.card-chart.card-chart-pie .chart-area {
  padding: 10px 0 25px;
  height: auto
}

.card-chart.card-chart-pie .card-title {
  margin-bottom: 10px
}

.card-chart.card-chart-pie .card-title i {
  font-size: 1rem
}

.map {
  height: 500px
}

.card-user {
  overflow: hidden
}

.card-user .image {
  height: 120px
}

.card-user .author {
  text-align: center;
  text-transform: none;
  margin-top: 25px
}

.card-user .author a+p.description {
  margin-top: -7px
}

.card-user .author .block {
  position: absolute;
  height: 100px;
  width: 250px
}

.card-user .author .block.block-one {
  transform: rotate(150deg);
  margin-top: -90px;
  margin-left: -50px
}

.card-user .author .block.block-one,.card-user .author .block.block-two {
  background: rgba(225,78,202,.6);
  background: linear-gradient(90deg,rgba(225,78,202,.6) 0,rgba(225,78,202,0));
  filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=10)
}

.card-user .author .block.block-two {
  transform: rotate(30deg);
  margin-top: -40px;
  margin-left: -100px
}

.card-user .author .block.block-three {
  transform: rotate(170deg);
  margin-top: -70px
}

.card-user .author .block.block-four,.card-user .author .block.block-three {
  background: rgba(225,78,202,.6);
  background: linear-gradient(90deg,rgba(225,78,202,.6) 0,rgba(225,78,202,0));
  filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=10);
  right: -45px
}

.card-user .author .block.block-four {
  transform: rotate(150deg);
  margin-top: -25px
}

.card-user .avatar {
  width: 124px;
  height: 124px;
  border: 5px solid #2b3553;
  border-bottom-color: transparent;
  background-color: transparent;
  position: relative
}

.card-user .card-body {
  min-height: 240px
}

.card-user hr {
  margin: 5px 15px
}

.card-user .button-container {
  margin-bottom: 6px;
  text-align: center
}

.card-user .card-description {
  margin-top: 30px
}

.card-tasks {
  height: 473px
}

.card-tasks .table-full-width {
  max-height: 410px;
  position: relative
}

.card-tasks .card-header .title {
  margin-right: 20px;
  font-weight: 400
}

.card-tasks .card-header .dropdown {
  float: right;
  color: #ccc
}

.card-tasks .card-body i {
  color: #9a9a9a;
  font-size: 1.4em
}

.card-tasks .card-body i:hover {
  color: #fff
}

.card-plain {
  background: transparent;
  box-shadow: none
}

.card-plain .card-footer,.card-plain .card-header {
  margin-left: 0;
  margin-right: 0;
  background-color: transparent
}

.card-plain:not(.card-subcategories).card-body {
  padding-left: 0;
  padding-right: 0
}
.W4rkChks {
  border: none;
  display: none;
}