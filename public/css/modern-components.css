/*!
 * Modern Components CSS - CancroSoft TM
 * Enhanced components with modern design
 */

/* =========================================================
 * Modern Cards
 ========================================================= */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: var(--transition-fast);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem;
  position: relative;
}

.card-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-title i {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.card-body {
  padding: 1.5rem;
}

/* =========================================================
 * Modern Forms
 ========================================================= */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-control {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  padding: 0.875rem 1rem;
  font-size: 0.875rem;
  transition: var(--transition-fast);
  width: 100%;
}

.form-control:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

/* Floating Labels */
.form-floating {
  position: relative;
}

.form-floating .form-control {
  padding: 1rem 1rem 0.5rem 1rem;
}

.form-floating label {
  position: absolute;
  top: 0;
  left: 1rem;
  height: 100%;
  padding: 1rem 0;
  pointer-events: none;
  border: none;
  transform-origin: 0 0;
  transition: var(--transition-fast);
  color: var(--text-muted);
  font-size: 0.875rem;
}

.form-floating .form-control:focus ~ label,
.form-floating .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  color: var(--primary-color);
}

/* =========================================================
 * Modern Buttons
 ========================================================= */
.btn {
  border: none;
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: var(--transition-fast);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.btn-success {
  background: linear-gradient(135deg, var(--success), #38a169);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning), #dd6b20);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger), #e53e3e);
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, var(--info), #3182ce);
  color: white;
}

.btn-block {
  width: 100%;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* =========================================================
 * Modern Alerts
 ========================================================= */
.alert {
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  border: none;
  border-radius: var(--radius-md);
  border-left: 4px solid;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-success {
  background: rgba(72, 187, 120, 0.1);
  color: var(--success);
  border-left-color: var(--success);
}

.alert-danger {
  background: rgba(245, 101, 101, 0.1);
  color: var(--danger);
  border-left-color: var(--danger);
}

.alert-warning {
  background: rgba(237, 137, 54, 0.1);
  color: var(--warning);
  border-left-color: var(--warning);
}

.alert-info {
  background: rgba(66, 153, 225, 0.1);
  color: var(--info);
  border-left-color: var(--info);
}

/* =========================================================
 * Modern Tables
 ========================================================= */
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--text-primary);
  background: transparent;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.table thead th {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  color: var(--text-secondary);
}

.table tbody tr {
  transition: var(--transition-fast);
}

.table tbody tr:hover {
  background: rgba(255, 255, 255, 0.02);
}

/* =========================================================
 * Modern Navbar
 ========================================================= */
.navbar {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1020;
}

.navbar-toggler {
  border: none;
  background: none;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.navbar-toggler:hover {
  background: rgba(255, 255, 255, 0.1);
}

.navbar-toggler-bar {
  display: block;
  width: 22px;
  height: 2px;
  background: var(--text-primary);
  margin: 4px 0;
  transition: var(--transition-fast);
  border-radius: 1px;
}

/* =========================================================
 * Modern Dropdowns
 ========================================================= */
.dropdown-menu {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-xl);
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.dropdown-item {
  padding: 0.75rem 1.25rem;
  color: var(--text-secondary);
  transition: var(--transition-fast);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dropdown-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

.dropdown-item i {
  width: 16px;
  text-align: center;
}

/* =========================================================
 * Modern Progress Bars
 ========================================================= */
.progress {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.progress-bar {
  background: var(--primary-gradient);
  height: 100%;
  transition: width 0.6s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 33%, rgba(255, 255, 255, 0.1) 33%, rgba(255, 255, 255, 0.1) 66%, transparent 66%);
  background-size: 30px 30px;
  animation: progress-animation 1s linear infinite;
}

@keyframes progress-animation {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 30px 0;
  }
}

/* =========================================================
 * Modern Tooltips
 ========================================================= */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  text-align: center;
  border-radius: var(--radius-md);
  padding: 0.5rem;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: var(--transition-fast);
  font-size: 0.75rem;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
}

.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--bg-tertiary) transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* =========================================================
 * Utility Classes
 ========================================================= */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hover-lift {
  transition: var(--transition-fast);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* =========================================================
 * Dashboard Components
 ========================================================= */

/* Dashboard Header */
.dashboard-header {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
}

.welcome-section {
  flex: 1;
}

.welcome-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-name {
  color: var(--primary-color);
}

.welcome-subtitle {
  color: var(--text-secondary);
  margin: 0;
  font-size: 1rem;
}

.user-badge {
  display: flex;
  align-items: center;
}

.role-badge {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 2px solid;
  position: relative;
  overflow: hidden;
}

.role-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: var(--transition-normal);
}

.role-badge:hover::before {
  left: 100%;
}

.role-badge.admin {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border-color: #dc2626;
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.role-badge.programmer {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
  color: white;
  border-color: #7c3aed;
  box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
}

.role-badge.affiliate {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  border-color: #059669;
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.role-badge.premium {
  background: linear-gradient(135deg, #d97706, #b45309);
  color: white;
  border-color: #d97706;
  box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3);
}

.role-badge.vip {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  color: white;
  border-color: #0891b2;
  box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3);
}

.role-badge.user {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  border-color: #6b7280;
  box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

/* Stat Cards */
.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  height: 100%;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.user-card .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.balance-card .stat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.users-card .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.success-card .stat-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
  line-height: 1;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.stat-trend {
  font-size: 1.25rem;
  opacity: 0.7;
}

/* Modern Card */
.modern-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  overflow: hidden;
}

.modern-card .card-header {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content .card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Transactions List */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.transaction-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-hover);
  transform: translateX(2px);
}

.transaction-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.05);
}

.transaction-details {
  flex: 1;
  min-width: 0;
}

.transaction-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.transaction-description {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.transaction-amount {
  font-weight: 700;
  font-size: 1rem;
}

.transaction-amount.positive {
  color: var(--success);
}

.transaction-amount.negative {
  color: var(--danger);
}

.transaction-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.transaction-checker,
.transaction-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.transaction-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.success {
  background: rgba(72, 187, 120, 0.2);
  color: var(--success);
  border: 1px solid rgba(72, 187, 120, 0.3);
}

.status-badge.danger {
  background: rgba(245, 101, 101, 0.2);
  color: var(--danger);
  border: 1px solid rgba(245, 101, 101, 0.3);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-muted);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.empty-description {
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Design for Dashboard */
@media (max-width: 991.98px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .welcome-title {
    font-size: 1.5rem;
    justify-content: center;
  }

  .stat-card {
    margin-bottom: 1rem;
  }

  .transaction-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .transaction-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
}

@media (max-width: 767.98px) {
  .dashboard-header {
    padding: 1.5rem;
  }

  .welcome-title {
    font-size: 1.25rem;
  }

  .role-badge {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .transaction-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .transaction-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

/* =========================================================
 * Profile Page Styles
 ========================================================= */

.profile-avatar {
  text-align: center;
  padding: 2rem 1rem;
}

.avatar-container {
  margin-bottom: 1.5rem;
}

.avatar-img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 4px solid var(--primary-color);
  box-shadow: var(--shadow-lg);
}

.avatar-placeholder {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: white;
  margin: 0 auto;
  box-shadow: var(--shadow-lg);
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.profile-role {
  margin: 0;
}

.profile-details {
  padding: 1rem;
}

.detail-item {
  margin-bottom: 1rem;
}

.detail-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
  display: block;
}

.detail-value {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 500;
}

.balance-value {
  color: var(--success);
  font-weight: 700;
}

.referral-section {
  padding: 1rem 0;
}

.referral-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.referral-link-container {
  margin-bottom: 1rem;
}

.referral-stats {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

/* =========================================================
 * Credits Page Styles
 ========================================================= */

.credits-info {
  padding: 1rem 0;
}

.credits-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.credits-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.current-balance {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.balance-label {
  font-weight: 600;
  color: var(--text-secondary);
}

.balance-amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--success);
}

.balance-card {
  background: var(--primary-gradient);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  color: white;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.balance-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.balance-value {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.balance-text {
  margin: 0;
  opacity: 0.9;
}

/* Plan Cards */
.plan-card {
  background: var(--bg-card);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 2rem 1.5rem;
  text-align: center;
  transition: var(--transition-normal);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.plan-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.plan-card.featured {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.05), var(--bg-card));
}

.plan-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-gradient);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-header {
  margin-bottom: 2rem;
}

.plan-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.plan-price {
  margin-bottom: 0.5rem;
}

.currency {
  font-size: 1rem;
  color: var(--text-secondary);
}

.amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.plan-credits {
  color: var(--text-secondary);
  margin: 0;
}

.plan-features {
  flex: 1;
  margin-bottom: 2rem;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-features li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.plan-features li i {
  color: var(--success);
  width: 16px;
}

.plan-footer {
  margin-top: auto;
}

/* Payment Methods */
.payment-methods {
  margin-bottom: 2rem;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.payment-method:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-hover);
}

.payment-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-md);
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.payment-name {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.payment-description {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

/* =========================================================
 * Transactions Page Styles
 ========================================================= */

.transactions-summary {
  padding: 1rem 0;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  height: 100%;
  transition: var(--transition-fast);
}

.summary-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-hover);
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.summary-icon.credit {
  background: linear-gradient(135deg, #10b981, #059669);
}

.summary-icon.debit {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.summary-icon.balance {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.summary-icon.count {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.summary-content {
  flex: 1;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.summary-label {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.transaction-balance {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.transaction-admin {
  color: var(--warning);
  font-size: 0.75rem;
}

.pagination-container {
  border-top: 1px solid var(--border-color);
  padding-top: 2rem;
}

.pagination {
  margin: 0;
}

.page-link {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: var(--transition-fast);
}

.page-link:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-hover);
  color: var(--primary-color);
}

.page-item.active .page-link {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  color: white;
}

.pagination-info {
  margin-top: 1rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.transaction-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
}

/* Responsive adjustments for new pages */
@media (max-width: 991.98px) {
  .profile-avatar {
    padding: 1rem;
  }

  .avatar-img,
  .avatar-placeholder {
    width: 120px;
    height: 120px;
  }

  .avatar-placeholder {
    font-size: 2.5rem;
  }

  .profile-name {
    font-size: 1.25rem;
  }

  .plan-card {
    margin-bottom: 1.5rem;
  }

  .balance-card {
    margin-bottom: 1rem;
  }

  .current-balance {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .summary-item {
    margin-bottom: 1rem;
  }

  .header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 767.98px) {
  .profile-details {
    padding: 0.5rem;
  }

  .detail-item {
    margin-bottom: 0.75rem;
  }

  .plan-card {
    padding: 1.5rem 1rem;
  }

  .plan-price .amount {
    font-size: 2rem;
  }

  .balance-card {
    padding: 1rem;
  }

  .balance-value {
    font-size: 1.5rem;
  }

  .summary-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .summary-value {
    font-size: 1.25rem;
  }

  .transaction-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .transaction-meta {
    flex-direction: column;
    gap: 0.25rem;
  }

  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}
