/* PrivXploit Custom Styles */

/* Basic reset and common styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Custom utility classes */
.text-primary {
  color: #e14eca !important;
}

.bg-primary {
  background-color: #e14eca !important;
}

.border-primary {
  border-color: #e14eca !important;
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(to right, #e14eca, #ba54f5);
  border: none;
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(to right, #ba54f5, #e14eca);
  color: white;
}

/* Form styles */
.form-control:focus {
  border-color: #e14eca;
  box-shadow: 0 0 0 0.2rem rgba(225, 78, 202, 0.25);
}

/* Link styles */
a {
  color: #e14eca;
  text-decoration: none;
}

a:hover {
  color: #ba54f5;
  text-decoration: none;
}

/* Card styles */
.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

/* Alert styles */
.alert {
  border-radius: 5px;
  border: none;
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

/* Input group styles */
.input-group-text {
  border-color: #2b3553;
}

/* Logo styles */
.logo {
  max-width: 150px;
  height: auto;
  filter: brightness(0) invert(1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .logo {
    max-width: 120px;
  }
}
