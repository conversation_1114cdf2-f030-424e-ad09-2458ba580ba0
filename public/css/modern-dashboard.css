/*!
 * Modern Dashboard CSS - CancroSoft TM
 * Enhanced visual design with smooth animations and modern UI
 */

/* =========================================================
 * CSS Variables for Modern Theme
 ========================================================= */
:root {
  /* Primary Colors */
  --primary-gradient: linear-gradient(135deg, #9d4edd 0%, #ff6b9d 100%);
  --primary-color: #9d4edd;
  --primary-dark: #7209b7;
  --primary-light: #c77dff;

  /* Background Colors */
  --bg-primary: #000000;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2d1b69;
  --bg-card: #1a0033;
  --bg-card-hover: #2d1b69;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #c77dff;
  --text-muted: #9d4edd;
  --text-accent: #ff6b9d;

  /* Border Colors */
  --border-color: rgba(255, 255, 255, 0.1);
  --border-hover: rgba(255, 255, 255, 0.2);

  /* Status Colors */
  --success: #48bb78;
  --warning: #ff8d72;
  --danger: #ff6b9d;
  --info: #9d4edd;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: all 0.15s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* =========================================================
 * Base Styles
 ========================================================= */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-primary) !important;
  background: var(--bg-primary) !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* =========================================================
 * Wrapper and Layout
 ========================================================= */
.wrapper {
  position: relative;
  min-height: 100vh;
  display: flex;
}

/* =========================================================
 * Modern Sidebar
 ========================================================= */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: var(--bg-secondary);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-color);
  z-index: 1000;
  transition: var(--transition-normal);
  overflow: hidden;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0.05;
  z-index: -1;
}

.sidebar-wrapper {
  position: relative;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* Main content area that expands */
.sidebar-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* Custom Scrollbar */
.sidebar-wrapper::-webkit-scrollbar {
  width: 4px;
}

.sidebar-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-wrapper::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 2px;
}

.sidebar-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* =========================================================
 * Logo Section
 ========================================================= */
.sidebar .logo {
  position: relative;
  padding: 1.5rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.02);
}

.sidebar .logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  transition: var(--transition-fast);
}

.sidebar .logo a:hover {
  color: var(--primary-color);
  transform: translateX(2px);
}

.sidebar .logo img {
  margin-right: 0.75rem;
  filter: drop-shadow(0 0 8px var(--primary-color));
}

/* =========================================================
 * User Info Section
 ========================================================= */
.user-info {
  padding: 1.5rem 1.25rem;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.02);
}

.user-avatar {
  position: relative;
  display: inline-block;
  margin-bottom: 0.75rem;
}

.user-avatar img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 3px solid var(--primary-color);
  box-shadow: var(--shadow-md);
  transition: var(--transition-fast);
}

.user-avatar:hover img {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.user-info h6 {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-primary);
}

.user-info .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.user-info .text-muted {
  color: var(--text-secondary) !important;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.user-info .fa-coins {
  color: var(--warning);
}

/* =========================================================
 * Navigation Menu
 ========================================================= */
.sidebar .nav {
  padding: 1rem 0;
  margin: 0;
  list-style: none;
  flex: 1;
}

.sidebar .nav > li {
  margin: 0.25rem 0.75rem;
  position: relative;
}

.sidebar .nav > li > a {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  font-weight: 500;
  position: relative;
  overflow: hidden;
  min-height: 48px; /* Consistent height for all menu items */
}

.sidebar .nav > li > a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: var(--transition-fast);
  z-index: -1;
}

.sidebar .nav > li > a:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(4px);
}

.sidebar .nav > li > a:hover::before {
  opacity: 0.1;
}

.sidebar .nav > li.active > a {
  color: var(--text-primary);
  background: var(--primary-gradient);
  box-shadow: var(--shadow-md);
}

.sidebar .nav > li.active > a::before {
  opacity: 1;
}

.sidebar .nav > li > a i {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
  flex-shrink: 0; /* Prevent icon from shrinking */
  text-align: center;
}

.sidebar .nav > li > a p {
  margin: 0;
  flex: 1;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1.4;
}

/* =========================================================
 * Collapsible Menu Items
 ========================================================= */
.sidebar .nav .collapse {
  margin-top: 0.5rem;
  transition: all 0.3s ease;
  overflow: hidden;
  max-height: 0;
}

.sidebar .nav .collapse.show {
  max-height: 500px; /* Adjust based on content */
}

.sidebar .nav .collapse .nav {
  padding: 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  margin: 0;
}

.sidebar .nav .collapse .nav li {
  margin: 0;
}

.sidebar .nav .collapse .nav li a {
  padding: 0.625rem 1rem 0.625rem 3rem;
  font-size: 0.8125rem;
  color: var(--text-muted);
  border-radius: 0;
  position: relative;
  display: flex;
  align-items: center;
  min-height: 40px; /* Consistent height for submenu items */
}

.sidebar .nav .collapse .nav li a::before {
  content: '';
  position: absolute;
  left: 2.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: var(--text-muted);
  border-radius: 50%;
  transition: var(--transition-fast);
}

.sidebar .nav .collapse .nav li a:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.05);
  transform: none;
}

.sidebar .nav .collapse .nav li a:hover::before {
  background: var(--primary-color);
  transform: translateY(-50%) scale(1.5);
}

.sidebar .nav .collapse .nav li.active a {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
}

.sidebar .nav .collapse .nav li.active a::before {
  background: var(--primary-color);
  transform: translateY(-50%) scale(1.5);
}

/* =========================================================
 * Caret Icon for Collapsible Items
 ========================================================= */
.sidebar .nav > li > a .caret {
  margin-left: auto;
  transition: var(--transition-fast);
}

.sidebar .nav > li > a[aria-expanded="true"] .caret {
  transform: rotate(180deg);
}

/* =========================================================
 * Status Badges in Menu
 ========================================================= */
.sidebar .nav .badge {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background: var(--success);
  color: white;
}

.badge-warning {
  background: var(--warning);
  color: white;
}

.badge-danger {
  background: var(--danger);
  color: white;
}

.badge-primary {
  background: var(--primary-color);
  color: white;
}

.badge-info {
  background: var(--info);
  color: white;
}

.badge-secondary {
  background: var(--text-muted);
  color: white;
}

/* =========================================================
 * Sidebar Footer
 ========================================================= */
.sidebar-footer {
  margin-top: auto;
  padding: 1rem 1.25rem;
  border-top: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.02);
  text-align: center;
}

.sidebar-footer p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* =========================================================
 * Main Panel
 ========================================================= */
.main-panel {
  flex: 1;
  margin-left: 280px;
  min-height: 100vh;
  background: var(--bg-primary);
  transition: var(--transition-normal);
}

/* When no sidebar is present */
body:not(.sidebar-present) .main-panel {
  margin-left: 0;
}

/* =========================================================
 * Content Area
 ========================================================= */
.content {
  padding: 2rem;
  min-height: calc(100vh - 140px);
}

/* =========================================================
 * Enhanced Sidebar Styles
 ========================================================= */

/* Category and Checker Organization */
.category-item {
  margin-bottom: 0.5rem;
}

.category-link {
  position: relative;
  padding: 0.75rem 1rem !important;
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid transparent;
}

.category-link:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-hover);
  transform: translateX(2px);
}

.category-count {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 400;
  margin-left: 0.25rem;
}

.checker-list {
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  margin: 0.5rem 0;
  padding: 0.5rem 0;
}

.checker-item {
  margin-bottom: 0.25rem;
}

.checker-link {
  padding: 0.5rem 1rem 0.5rem 2rem !important;
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  position: relative;
}

.checker-link:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(3px);
}

.checker-status {
  font-size: 0.65rem;
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 0.5rem;
  display: inline-block;
}

.status-active {
  background: rgba(72, 187, 120, 0.2);
  color: var(--success);
  border: 1px solid rgba(72, 187, 120, 0.3);
}

.status-maintenance {
  background: rgba(237, 137, 54, 0.2);
  color: var(--warning);
  border: 1px solid rgba(237, 137, 54, 0.3);
}

.status-inactive {
  background: rgba(245, 101, 101, 0.2);
  color: var(--danger);
  border: 1px solid rgba(245, 101, 101, 0.3);
}

/* Empty states */
.empty-category a,
.empty-tools a {
  color: var(--text-muted) !important;
  cursor: default;
  opacity: 0.6;
}

.empty-category a:hover,
.empty-tools a:hover {
  background: none !important;
  transform: none !important;
}

/* Section headers enhancement */
.nav-section-header {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  position: relative;
}

.nav-section-header:first-child {
  margin-top: 0;
}

.nav-section-header span {
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--text-accent);
  padding: 0 1rem;
  position: relative;
}

.nav-section-header span::after {
  content: '';
  position: absolute;
  bottom: -0.25rem;
  left: 1rem;
  right: 1rem;
  height: 1px;
  background: linear-gradient(90deg, var(--primary-color), transparent);
}

/* Section divider */
.nav-section-divider {
  height: 1px;
  background: var(--border-color);
  margin: 1.5rem 1rem;
  opacity: 0.5;
}

/* Sidebar mini icons and normal text alignment */
.sidebar-mini-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.sidebar-mini-icon i {
  font-size: 0.875rem;
  width: 16px;
  text-align: center;
}

.sidebar-normal {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.8125rem;
  line-height: 1.4;
}

/* Logout link enhancement */
.logout-link {
  color: var(--danger) !important;
  background: rgba(245, 101, 101, 0.1);
  border: 1px solid rgba(245, 101, 101, 0.2);
  border-radius: var(--radius-md);
  margin: 0.5rem 0;
}

.logout-link:hover {
  background: rgba(245, 101, 101, 0.2) !important;
  border-color: rgba(245, 101, 101, 0.4);
  color: white !important;
}

/* =========================================================
 * Responsive Design
 ========================================================= */
@media (max-width: 1199.98px) {
  .sidebar {
    width: 260px;
  }

  .main-panel {
    margin-left: 260px;
  }
}

@media (max-width: 991.98px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
    z-index: 1050;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-panel {
    margin-left: 0;
    width: 100%;
  }

  .content {
    padding: 1.5rem 1rem;
  }

  /* Mobile sidebar adjustments */
  .category-count {
    display: none;
  }

  .checker-status {
    font-size: 0.6rem;
    padding: 0.1rem 0.25rem;
  }
}

@media (max-width: 767.98px) {
  .content {
    padding: 1rem 0.75rem;
  }

  .user-info {
    padding: 1rem;
  }

  .sidebar .logo {
    padding: 1rem;
  }
}

/* =========================================================
 * Mobile Overlay
 ========================================================= */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-fast);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* =========================================================
 * Animations
 ========================================================= */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.sidebar {
  animation: slideInLeft 0.3s ease-out;
}

.content {
  animation: fadeIn 0.5s ease-out;
}

/* =========================================================
 * Additional Sidebar Styles
 ========================================================= */

/* Status Indicator */
.user-avatar {
  position: relative;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid var(--bg-secondary);
}

.status-indicator.online {
  background: var(--success);
  box-shadow: 0 0 8px rgba(72, 187, 120, 0.5);
}

.status-indicator.offline {
  background: var(--text-muted);
}

/* Navigation Section Headers */
.nav-section-header {
  padding: 1rem 1.25rem 0.5rem 1.25rem;
  margin-top: 1.5rem;
}

.nav-section-header:first-child {
  margin-top: 0;
}

.nav-section-header span {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--text-muted);
  position: relative;
}

.nav-section-header span::after {
  content: '';
  position: absolute;
  bottom: -0.25rem;
  left: 0;
  width: 30px;
  height: 1px;
  background: var(--primary-color);
  opacity: 0.5;
}

/* Navigation Section Divider */
.nav-section-divider {
  height: 1px;
  background: var(--border-color);
  margin: 1rem 1.25rem;
  opacity: 0.5;
}

/* User Badges */
.user-badges {
  margin-bottom: 0.75rem;
}

.user-badges .badge {
  font-size: 0.6875rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

/* User Credits */
.user-credits {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.user-credits i {
  color: var(--warning);
  font-size: 1rem;
}

/* =========================================================
 * Compact User Info Section
 ========================================================= */
.user-info-compact {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.02);
  margin-top: auto;
}

.user-summary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar-small {
  position: relative;
  flex-shrink: 0;
}

.user-avatar-small img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);
}

.user-avatar-small:hover img {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  margin-bottom: 0.375rem;
}

.role-badge {
  font-size: 0.6875rem;
  padding: 0.125rem 0.5rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.role-badge.admin {
  background: rgba(52, 144, 220, 0.2);
  color: #3490dc;
  border: 1px solid rgba(52, 144, 220, 0.3);
}

.role-badge.vip {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.role-badge.premium {
  background: rgba(23, 162, 184, 0.2);
  color: #17a2b8;
  border: 1px solid rgba(23, 162, 184, 0.3);
}

.role-badge.user {
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.user-credits-compact {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.user-credits-compact i {
  color: var(--warning);
  font-size: 0.875rem;
}

/* =========================================================
 * Sidebar Footer
 ========================================================= */
.sidebar-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.02);
  text-align: center;
}

.sidebar-footer p {
  margin: 0;
  font-size: 0.6875rem;
  color: var(--text-secondary);
  opacity: 0.7;
  line-height: 1.2;
}

.sidebar-footer p:first-child {
  margin-bottom: 0.125rem;
}

/* Logo Text */
.logo-text {
  font-weight: 700;
  font-size: 1.125rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Logout Link */
.logout-link {
  color: var(--danger) !important;
  transition: var(--transition-fast);
}

.logout-link:hover {
  background: rgba(245, 101, 101, 0.1) !important;
  color: var(--danger) !important;
}

/* Sidebar Mini Icons */
.sidebar-mini-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-muted);
  transition: var(--transition-fast);
}

.sidebar-normal {
  flex: 1;
  font-size: 0.875rem;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

/* Active state for submenu items */
.sidebar .nav .collapse .nav li.active .sidebar-mini-icon,
.sidebar .nav .collapse .nav li.active .sidebar-normal {
  color: var(--primary-color);
}

/* Hover effects for submenu items */
.sidebar .nav .collapse .nav li a:hover .sidebar-mini-icon,
.sidebar .nav .collapse .nav li a:hover .sidebar-normal {
  color: var(--text-primary);
}

/* Badge positioning in sidebar */
.sidebar .nav .sidebar-normal .badge {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
  margin-left: 0.5rem;
}

/* Improved caret animation */
.sidebar .nav > li > a .caret {
  font-size: 0.75rem;
  transition: var(--transition-fast);
  color: var(--text-muted);
}

.sidebar .nav > li > a[aria-expanded="true"] .caret {
  transform: rotate(180deg);
  color: var(--primary-color);
}

/* Enhanced hover effects */
.sidebar .nav > li > a:hover .caret {
  color: var(--text-primary);
}

/* Mobile specific styles */
@media (max-width: 991.98px) {
  .user-info {
    padding: 1rem;
  }

  .user-info h6 {
    font-size: 0.875rem;
  }

  .user-credits {
    font-size: 0.8125rem;
    padding: 0.375rem 0.75rem;
  }

  .nav-section-header {
    padding: 0.75rem 1rem 0.375rem 1rem;
    margin-top: 1rem;
  }

  .nav-section-header span {
    font-size: 0.6875rem;
  }

  /* Compact user info mobile adjustments */
  .user-info-compact {
    padding: 0.75rem;
  }

  .user-summary {
    gap: 0.5rem;
  }

  .user-avatar-small img {
    width: 36px;
    height: 36px;
  }

  .user-name {
    font-size: 0.8125rem;
  }

  .role-badge {
    font-size: 0.625rem;
    padding: 0.1rem 0.375rem;
  }

  .user-credits-compact {
    font-size: 0.6875rem;
    padding: 0.1875rem 0.375rem;
  }
}
