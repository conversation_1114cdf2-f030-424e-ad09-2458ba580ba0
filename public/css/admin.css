/**
 * PrivXploit Admin Panel CSS
 * Custom styles for the admin panel
 */

/* Admin Panel Improvements */
.admin-panel .card {
  box-shadow: 0 1px 20px 0 rgba(0,0,0,.1);
  transition: all 0.3s ease;
}

.admin-panel .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 25px 0 rgba(0,0,0,.2);
}

/* Improved table styles */
.admin-panel .table {
  margin-bottom: 0;
}

.admin-panel .table th {
  font-weight: 600;
  border-top: 0;
}

.admin-panel .table td {
  vertical-align: middle;
}

.admin-panel .table-hover tbody tr:hover {
  background-color: rgba(34, 42, 66, 0.1);
}

/* Action buttons */
.admin-panel .btn-action {
  padding: 0.25rem 0.5rem;
  margin: 0 0.1rem;
}

/* Form improvements */
.admin-panel .form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.admin-panel .form-control {
  transition: all 0.2s ease;
}

.admin-panel .form-control:focus {
  border-color: #9d4edd;
  box-shadow: 0 0 0 0.2rem rgba(157, 78, 221, 0.25);
}

/* Loading indicator */
.admin-panel .loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.admin-panel .loading i {
  color: #9d4edd;
}

/* Card header improvements */
.admin-panel .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
}

.admin-panel .card-header h4 {
  margin-bottom: 0;
}

.admin-panel .card-header .btn {
  margin-bottom: 0;
}

/* Sidebar improvements */
.sidebar .nav li.active > a:not([data-toggle="collapse"]) {
  background: linear-gradient(to right, #9d4edd, #ff6b9d);
}

.sidebar .nav li.active > a[data-toggle="collapse"] {
  background: rgba(157, 78, 221, 0.1);
  color: #9d4edd;
}

.sidebar .nav li.active > a i {
  color: #fff;
}

/* DataTables improvements */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
  margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
  background-color: rgba(34, 42, 66, 0.2);
  border: 1px solid rgba(34, 42, 66, 0.2);
  color: #9a9a9a;
  border-radius: 4px;
  padding: 5px 10px;
}

.dataTables_wrapper .dataTables_filter input:focus {
  border-color: #9d4edd;
  outline: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  padding: 0.25rem 0.5rem;
  margin: 0 0.1rem;
  border-radius: 4px;
  color: #9a9a9a !important;
  background: transparent;
  border: 1px solid rgba(34, 42, 66, 0.2);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: linear-gradient(to right, #9d4edd, #ff6b9d) !important;
  color: #fff !important;
  border: 1px solid #9d4edd;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .admin-panel .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .admin-panel .card-header .btn {
    margin-top: 1rem;
    align-self: flex-end;
  }

  .admin-panel .table-responsive {
    padding-bottom: 1rem;
  }

  .admin-panel .dataTables_wrapper .dataTables_length,
  .admin-panel .dataTables_wrapper .dataTables_filter {
    text-align: left;
  }
}

/* Status badges */
.badge-active {
  background-color: #00f2c3;
  color: #1e1e2f;
}

.badge-inactive,
.badge-disabled {
  background-color: #ff6b9d;
  color: #fff;
}

.badge-maintenance {
  background-color: #ff8d72;
  color: #1e1e2f;
}

/* Flash messages */
#success-message,
#error-message {
  display: none;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(34, 42, 66, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(225, 78, 202, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(225, 78, 202, 0.8);
}
