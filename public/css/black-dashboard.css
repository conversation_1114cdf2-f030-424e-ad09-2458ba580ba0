/*!
 * Black Dashboard v1.0.1 (https://www.creative-tim.com/product/black-dashboard)
 * Copyright 2018 Creative Tim (https://www.creative-tim.com)
 * Licensed under MIT (https://github.com/creativetimofficial/black-dashboard/blob/master/LICENSE.md)
 */

/* =========================================================
 * Black Dashboard - v1.0.1
 ========================================================= */

/* =========================================================
 * Bootstrap CSS
 ========================================================= */

:root {
  --blue: #5e72e4;
  --indigo: #5603ad;
  --purple: #9d4edd;
  --pink: #ff6b9d;
  --red: #f5365c;
  --orange: #fb6340;
  --yellow: #ffd600;
  --green: #2dce89;
  --teal: #11cdef;
  --cyan: #2bffc6;
  --gray: #6c757d;
  --gray-dark: #32325d;
  --light: #ced4da;
  --lighter: #e9ecef;
  --primary: #9d4edd;
  --secondary: #1a1a1a;
  --success: #00f2c3;
  --info: #7209b7;
  --warning: #ff8d72;
  --danger: #ff6b9d;
  --light: #adb5bd;
  --dark: #000000;
  --default: #2d1b69;
  --white: #fff;
  --neutral: #fff;
  --darker: #000;
}

/* =========================================================
 * Black Dashboard Main Styles
 ========================================================= */

/* Test CSS Loading */
body::before {
  content: "Black Dashboard CSS Loaded";
  position: fixed;
  top: 0;
  left: 0;
  background: #e14eca;
  color: white;
  padding: 5px 10px;
  font-size: 12px;
  z-index: 10000;
  display: none; /* Hidden by default, can be shown for debugging */
}

body {
  margin: 0;
  font-family: Poppins, sans-serif !important;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #fff !important;
  text-align: left;
  background-color: #000000 !important;
  background: linear-gradient(135deg, #000000 0%, #1a0033 50%, #000000 100%) !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.wrapper {
  position: relative;
  top: 0;
  height: 100vh;
}

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  width: 230px;
  display: block;
  z-index: 1;
  color: #fff;
  font-weight: 200;
  background-size: cover;
  background-position: center center;
  background: #1e1e2f;
}

.sidebar .sidebar-wrapper {
  position: relative;
  max-height: calc(100vh - 75px);
  min-height: 100%;
  overflow: auto;
  width: 100%;
  z-index: 4;
  padding-bottom: 30px;
}

.sidebar .logo {
  position: relative;
  padding: 0.5rem 0.7rem;
  z-index: 4;
}

.sidebar .logo a.logo-mini,
.sidebar .logo a.logo-normal {
  transition: all 300ms ease;
  font-weight: 400;
  font-size: 1.125rem;
  text-transform: uppercase;
  opacity: 0.8;
  text-decoration: none;
  color: #fff;
}

.sidebar .logo:after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 15px;
  height: 1px;
  width: calc(100% - 30px);
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.06), rgba(255, 255, 255, 0.6));
}

.sidebar .nav {
  margin-top: 20px;
  display: block;
}

.sidebar .nav li {
  position: relative;
  display: block;
}

.sidebar .nav li > a {
  color: rgba(255, 255, 255, 0.8);
  display: block;
  text-decoration: none;
  position: relative;
  cursor: pointer;
  opacity: 0.7;
  padding: 10px 15px;
  font-size: 0.8125rem;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 1.5rem;
  margin: 0 15px;
  border-radius: 0.1875rem;
  transition: all 300ms ease;
}

.sidebar .nav li > a i {
  font-size: 1.25rem;
  float: left;
  margin-right: 12px;
  line-height: 30px;
  width: 34px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}

.sidebar .nav li > a:hover {
  color: #fff;
  opacity: 1;
}

.sidebar .nav li.active > a {
  color: #fff;
  opacity: 1;
  background: rgba(255, 255, 255, 0.13);
}

.sidebar .nav li.active > a:before {
  content: " ";
  position: absolute;
  height: 6px;
  width: 6px;
  top: 22px;
  left: -4px;
  background: #fff;
  border-radius: 50%;
}

.sidebar .nav p {
  margin: 0;
  line-height: 30px;
  position: relative;
  display: block;
  height: auto;
  white-space: nowrap;
}

.main-panel {
  position: relative;
  float: right;
  width: calc(100% - 230px);
  min-height: 100vh;
  background: linear-gradient(0deg, #1e1e2f 0%, #1e1e2f 100%);
}

/* When no sidebar is present (no user logged in) */
body:not(.sidebar-present) .main-panel {
  width: 100%;
  float: none;
}

/* Navbar adjustments when no sidebar */
body:not(.sidebar-present) .navbar {
  position: relative;
  width: 100%;
}

.main-panel > .content {
  padding: 78px 30px 30px 30px;
  min-height: calc(100vh - 123px);
}

.main-panel > .navbar {
  margin-bottom: 0;
}

.navbar {
  border: 0;
  border-radius: 0;
  position: absolute;
  width: 100%;
  z-index: 1029;
  color: #525f7f;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  background-color: rgba(34, 42, 66, 0.95);
  box-shadow: 0 1px 20px 0px rgba(0, 0, 0, 0.1);
}

.navbar.navbar-transparent {
  background-color: transparent !important;
  box-shadow: none;
  color: #fff;
}

.navbar .navbar-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  color: #525f7f;
}

.navbar .navbar-brand {
  position: relative;
  color: inherit;
  height: 50px;
  font-size: 1.125rem;
  line-height: 30px;
  padding: 0.625rem 0;
  font-weight: 400;
  text-decoration: none;
}

.navbar .navbar-toggler {
  position: relative;
  color: #9A9A9A;
  cursor: pointer;
  font-size: 1.5em;
  top: 4px;
  background: transparent;
  border: 0;
  outline: 0;
}

.navbar .navbar-toggler-bar {
  display: block;
  position: relative;
  width: 22px;
  height: 1px;
  border-radius: 1px;
  background: #fff;
}

.navbar .navbar-toggler-bar + .navbar-toggler-bar {
  margin-top: 4px;
}

.card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d1b69 100%);
  border: 1px solid rgba(157, 78, 221, 0.2);
  position: relative;
  width: 100%;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px 0px rgba(157, 78, 221, 0.3);
  border-radius: 0.5rem;
}

.card .card-header {
  padding: 15px 15px 0;
  border: 0;
  color: rgba(255, 255, 255, 0.8);
  background-color: transparent;
}

.card .card-body {
  padding: 15px;
}

.card .card-title {
  color: #fff;
  text-transform: inherit;
  font-weight: 300;
  margin-bottom: 0.75rem;
}

.card .card-category,
.card .card-description {
  color: rgba(255, 255, 255, 0.6);
}

.btn {
  border-width: 2px;
  background-color: transparent;
  font-weight: 400;
  opacity: 0.8;
  filter: alpha(opacity=80);
  padding: 0.625rem 0.875rem;
  font-size: 0.75rem;
  line-height: 1.35;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.15s ease;
  letter-spacing: 0.025em;
  position: relative;
  text-transform: uppercase;
  will-change: transform;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  border: 1px solid transparent;
  white-space: nowrap;
  display: inline-block;
  user-select: none;
}

.btn:hover,
.btn:focus {
  opacity: 1;
  filter: alpha(opacity=100);
  outline: 0;
}

.btn-primary {
  color: #fff;
  background: linear-gradient(135deg, #9d4edd 0%, #7209b7 100%);
  border-color: #9d4edd;
  box-shadow: 0 4px 15px rgba(157, 78, 221, 0.4);
}

.btn-primary:hover {
  color: #fff;
  background: linear-gradient(135deg, #7209b7 0%, #5a189a 100%);
  border-color: #7209b7;
  box-shadow: 0 6px 20px rgba(157, 78, 221, 0.6);
  transform: translateY(-2px);
}

.form-control {
  background-color: transparent;
  border: 1px solid #cad1d7;
  border-radius: 0.25rem;
  color: #fff;
  padding: 0.5rem 0.7rem;
  font-size: 0.875rem;
  line-height: 1.428571;
  background-clip: padding-box;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  color: #fff;
  background-color: transparent;
  border-color: #e14eca;
  outline: 0;
  box-shadow: none;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
  opacity: 1;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.7rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.428571;
  color: #adb5bd;
  text-align: center;
  white-space: nowrap;
  background-color: transparent;
  border: 1px solid #cad1d7;
  border-radius: 0.25rem;
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #fff;
  background-color: transparent;
}

.table th,
.table td {
  padding: 1rem 0.75rem;
  vertical-align: top;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-transform: uppercase;
  font-size: 0.6875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc;
}

.alert-danger {
  color: #842029;
  background-color: #f8d7da;
  border-color: #f5c2c7;
}

.fixed-plugin {
  position: fixed;
  top: 180px;
  right: 0;
  width: 64px;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1031;
  border-radius: 8px 0 0 8px;
  text-align: center;
}

.fixed-plugin .dropdown {
  color: #fff;
  border-radius: 0;
  text-align: center;
}

.fixed-plugin .dropdown-menu {
  right: 80px;
  left: auto;
  width: 290px;
  border-radius: 0.1875rem;
  background-color: #27293d;
  border: none;
}

/* Responsive */
@media (max-width: 991.98px) {
  .sidebar {
    width: 230px;
    position: fixed;
    display: block;
    top: 0;
    height: 100vh;
    left: -230px;
    z-index: 1032;
    visibility: visible;
    overflow-y: visible;
    border-top: none;
    text-align: left;
    padding-right: 0px;
    padding-left: 0;
    transform: translateX(0);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
  }

  .nav-open .sidebar {
    transform: translateX(230px);
  }

  .main-panel {
    width: 100%;
    transform: translateX(0);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
  }

  .nav-open .main-panel {
    right: 0;
    transform: translateX(230px);
  }
}

/* =========================================================
 * Additional Black Dashboard Styles
 ========================================================= */

/* Sidebar Mini */
.sidebar-mini .sidebar {
  width: 80px;
}

.sidebar-mini .sidebar .logo a.logo-normal {
  opacity: 0;
  transform: translate3d(-25px, 0, 0);
}

.sidebar-mini .sidebar .nav li > a p {
  transform: translate3d(-25px, 0, 0);
  opacity: 0;
}

.sidebar-mini .main-panel {
  width: calc(100% - 80px);
}

/* Perfect Scrollbar */
.ps {
  overflow: hidden !important;
  overflow-anchor: none;
  -ms-overflow-style: none;
  touch-action: auto;
  -ms-touch-action: auto;
}

.ps__rail-x {
  display: none;
  opacity: 0;
  transition: background-color .2s linear, opacity .2s linear;
  -webkit-transition: background-color .2s linear, opacity .2s linear;
  height: 15px;
  bottom: 0px;
  position: absolute;
}

.ps__rail-y {
  display: none;
  opacity: 0;
  transition: background-color .2s linear, opacity .2s linear;
  -webkit-transition: background-color .2s linear, opacity .2s linear;
  width: 15px;
  right: 0;
  position: absolute;
}

.ps--active-x > .ps__rail-x,
.ps--active-y > .ps__rail-y {
  display: block;
  background-color: transparent;
}

.ps:hover > .ps__rail-x,
.ps:hover > .ps__rail-y,
.ps--focus > .ps__rail-x,
.ps--focus > .ps__rail-y,
.ps--scrolling-x > .ps__rail-x,
.ps--scrolling-y > .ps__rail-y {
  opacity: 0.6;
}

.ps__rail-x:hover,
.ps__rail-y:hover,
.ps__rail-x:focus,
.ps__rail-y:focus,
.ps__rail-x.ps--clicking,
.ps__rail-y.ps--clicking {
  background-color: #eee;
  opacity: 0.9;
}

.ps__thumb-x {
  background-color: #aaa;
  border-radius: 6px;
  transition: background-color .2s linear, height .2s ease-in-out;
  -webkit-transition: background-color .2s linear, height .2s ease-in-out;
  height: 6px;
  bottom: 2px;
  position: absolute;
}

.ps__thumb-y {
  background-color: #aaa;
  border-radius: 6px;
  transition: background-color .2s linear, width .2s ease-in-out;
  -webkit-transition: background-color .2s linear, width .2s ease-in-out;
  width: 6px;
  right: 2px;
  position: absolute;
}

.ps__rail-x:hover > .ps__thumb-x,
.ps__rail-x:focus > .ps__thumb-x,
.ps__rail-x.ps--clicking .ps__thumb-x {
  background-color: #999;
  height: 11px;
}

.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
  background-color: #999;
  width: 11px;
}

/* Dropdown Styles */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 0.875rem;
  color: #525f7f;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 0 solid rgba(34, 42, 66, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 50px 100px rgba(50, 50, 93, 0.1), 0 15px 35px rgba(50, 50, 93, 0.15), 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
}

.dropdown-item:hover,
.dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f6f9fc;
}

/* Badge Styles */
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.badge-primary {
  color: #fff;
  background-color: #e14eca;
}

.badge-success {
  color: #fff;
  background-color: #00f2c3;
}

.badge-danger {
  color: #fff;
  background-color: #fd5d93;
}

.badge-warning {
  color: #212529;
  background-color: #ff8d72;
}

.badge-info {
  color: #fff;
  background-color: #1d8cf8;
}

/* Progress Bar */
.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background: linear-gradient(135deg, #9d4edd 0%, #ff6b9d 100%);
  transition: width 0.6s ease;
}

/* Stats */
.stats {
  color: #9A9A9A;
  font-size: 0.8125rem;
}

.stats i {
  margin-right: 5px;
  position: relative;
}

/* Footer */
.footer {
  background: transparent;
  line-height: 1.4285714;
  color: #9A9A9A;
  padding: 15px;
}

.footer .copyright {
  font-size: 0.8125rem;
  color: #9A9A9A;
}

/* White Content Theme */
.white-content {
  background: #f5f6fa;
}

.white-content .main-panel {
  background: linear-gradient(0deg, #f5f6fa 0%, #f5f6fa 100%);
}

.white-content .card {
  background: #fff;
  box-shadow: 0 1px 20px 0px rgba(0, 0, 0, 0.1);
}

.white-content .card .card-title {
  color: #222a42;
}

.white-content .card .card-category,
.white-content .card .card-description {
  color: #9a9a9a;
}

.white-content .sidebar {
  background: #fff;
  box-shadow: 0 0 10px 0 rgba(183, 192, 206, 0.2);
}

.white-content .sidebar .nav li > a {
  color: rgba(34, 42, 66, 0.8);
}

.white-content .sidebar .nav li > a i {
  color: rgba(34, 42, 66, 0.6);
}

.white-content .sidebar .nav li.active > a {
  color: #222a42;
  background: rgba(34, 42, 66, 0.1);
}

.white-content .sidebar .nav li.active > a:before {
  background: #222a42;
}

.white-content .sidebar .logo:after {
  background: linear-gradient(90deg, rgba(34, 42, 66, 0.06), rgba(34, 42, 66, 0.6));
}

.white-content .navbar {
  background-color: rgba(255, 255, 255, 0.95);
  color: #525f7f;
}

.white-content .navbar.navbar-transparent {
  background-color: transparent !important;
  color: #525f7f;
}

.white-content .form-control {
  color: #222a42;
  border-color: rgba(29, 37, 59, 0.2);
}

.white-content .form-control:focus {
  border-color: #e14eca;
}

.white-content .form-control::placeholder {
  color: rgba(34, 42, 66, 0.4);
}

.white-content .table {
  color: #222a42;
}

.white-content .table th,
.white-content .table td {
  border-top: 1px solid rgba(34, 42, 66, 0.1);
}

.white-content .table thead th {
  color: rgba(34, 42, 66, 0.6);
}
