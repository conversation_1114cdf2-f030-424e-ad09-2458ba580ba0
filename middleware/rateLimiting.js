const rateLimit = require('express-rate-limit');

// Sistema configurado para usar rate limiting em memória (Redis removido)
console.log('Rate limiting configurado para usar memória local');

// Configuração segura para key generator que funciona com ou sem proxy
const getClientIP = (req) => {
  // Se trust proxy estiver habilitado, usar req.ip (que considera headers de proxy)
  // Senão, usar req.connection.remoteAddress diretamente
  return req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
};

// Rate limiter geral para toda a aplicação
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 1000, // máximo 1000 requests por IP por 15 minutos
  message: {
    error: 'Muitas tentativas. Tente novamente em 15 minutos.',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => getClientIP(req),
  skip: (req) => {
    // Pular rate limiting para admins
    return req.session && req.session.user && req.session.user.role === 'admin';
  }
});

// Rate limiter para login
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 tentativas de login por IP por 15 minutos
  message: {
    error: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => getClientIP(req),
  skipSuccessfulRequests: true // Não contar logins bem-sucedidos
});

// Rate limiter para APIs de checker
const checkerApiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minuto
  max: (req) => {
    // Limites baseados no role do usuário
    if (!req.session || !req.session.user) return 5; // Usuários não autenticados

    switch (req.session.user.role) {
      case 'admin': return 1000; // Sem limite prático para admins
      case 'vip': return 100;
      case 'premium': return 50;
      case 'programmer': return 200;
      case 'affiliate': return 30;
      default: return 20; // Usuários básicos
    }
  },
  message: (req) => ({
    error: 'Limite de API excedido para seu nível de usuário. Tente novamente em 1 minuto.',
    retryAfter: 60,
    userRole: (req.session && req.session.user) ? req.session.user.role : 'guest',
    upgradeMessage: 'Considere fazer upgrade para um plano superior para mais requests.'
  }),
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Usar ID do usuário se autenticado, senão IP seguro
    return (req.session && req.session.user) ? `user:${req.session.user.id}` : `ip:${getClientIP(req)}`;
  }
});

// Rate limiter para upload de código
const codeUploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hora
  max: (req) => {
    if (!req.session || !req.session.user) return 0; // Não permitir uploads sem autenticação

    switch (req.session.user.role) {
      case 'admin': return 100;
      case 'programmer': return 10;
      default: return 3; // Outros usuários
    }
  },
  message: {
    error: 'Limite de upload de código excedido. Tente novamente em 1 hora.',
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => `user:${(req.session && req.session.user) ? req.session.user.id : 'anonymous'}`
});

// Rate limiter para admin actions
const adminActionLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minuto
  max: 100, // máximo 100 ações administrativas por minuto
  message: {
    error: 'Muitas ações administrativas. Aguarde 1 minuto.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => `admin:${(req.session && req.session.user) ? req.session.user.id : 'anonymous'}`
});

// Rate limiter para reset de senha
const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hora
  max: 3, // máximo 3 tentativas de reset por hora
  message: {
    error: 'Muitas tentativas de reset de senha. Tente novamente em 1 hora.',
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => getClientIP(req)
});

// Rate limiter para registro de usuários
const registrationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hora
  max: 5, // máximo 5 registros por IP por hora
  message: {
    error: 'Muitas tentativas de registro. Tente novamente em 1 hora.',
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => getClientIP(req)
});

// Middleware para aplicar rate limiting baseado na rota
const applyRateLimiting = (req, res, next) => {
  // Aplicar rate limiting específico baseado na rota
  if (req.path.startsWith('/api/checkers') || req.path.startsWith('/api/dynamic')) {
    return checkerApiLimiter(req, res, next);
  }

  if (req.path === '/login' && req.method === 'POST') {
    return loginLimiter(req, res, next);
  }

  if (req.path === '/register' && req.method === 'POST') {
    return registrationLimiter(req, res, next);
  }

  if (req.path.includes('/reset-password')) {
    return passwordResetLimiter(req, res, next);
  }

  if (req.path.startsWith('/admin') && req.method !== 'GET') {
    return adminActionLimiter(req, res, next);
  }

  // Rate limiting geral para outras rotas
  return generalLimiter(req, res, next);
};

// Função para obter estatísticas de rate limiting
const getRateLimitStats = async (prefix = 'rl:*') => {
  return {
    message: 'Rate limiting usando memória local (Redis removido)',
    type: 'memory-only',
    active: true
  };
};

// Função para limpar rate limits de um usuário específico
const clearUserRateLimit = async (userId) => {
  console.log(`Rate limits em memória não podem ser limpos individualmente para usuário ${userId}`);
  return { message: 'Rate limiting em memória - limpeza individual não suportada' };
};

// Função para verificar se um usuário está sendo rate limited
const checkRateLimit = async (userId, type = 'general') => {
  return {
    current: 0,
    ttl: -1,
    isLimited: false,
    message: 'Rate limiting em memória - verificação individual não suportada'
  };
};

module.exports = {
  generalLimiter,
  loginLimiter,
  checkerApiLimiter,
  codeUploadLimiter,
  adminActionLimiter,
  passwordResetLimiter,
  registrationLimiter,
  applyRateLimiting,
  getRateLimitStats,
  clearUserRateLimit,
  checkRateLimit
};
