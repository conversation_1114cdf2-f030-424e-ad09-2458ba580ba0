const crypto = require('crypto');

// Sistema configurado para usar apenas cache em memória (Redis removido)
let redis = null;
let useMemoryCache = true;
const memoryCache = new Map();

console.log('Cache configurado para usar memória local (Redis removido)');

// Funções auxiliares para cache em memória
const memoryCacheGet = (key) => {
  const item = memoryCache.get(key);
  if (!item) return null;

  if (Date.now() > item.expiry) {
    memoryCache.delete(key);
    return null;
  }

  return item.value;
};

const memoryCacheSet = (key, value, duration) => {
  const expiry = Date.now() + (duration * 1000);
  memoryCache.set(key, { value, expiry });

  // Limpar cache expirado periodicamente
  if (memoryCache.size > 1000) {
    const now = Date.now();
    for (const [k, v] of memoryCache.entries()) {
      if (now > v.expiry) {
        memoryCache.delete(k);
      }
    }
  }
};

/**
 * Middleware de cache para respostas HTTP
 */
const cacheMiddleware = (options = {}) => {
  const {
    duration = 300, // 5 minutos por padrão
    keyPrefix = 'cache',
    varyBy = ['url', 'user'], // Variar cache por URL e usuário
    skipCache = false,
    onlySuccessful = true // Cachear apenas respostas 2xx
  } = options;

  return async (req, res, next) => {
    if (skipCache) {
      return next();
    }

    try {
      // Gerar chave do cache
      const cacheKey = generateCacheKey(req, keyPrefix, varyBy);

      // Tentar obter do cache
      const cached = memoryCacheGet(cacheKey);

      if (cached) {
        const cachedData = cached;

        // Definir headers de cache
        res.set('X-Cache', 'HIT');
        res.set('X-Cache-Key', cacheKey);
        res.set('X-Cache-Type', 'memory');

        // Restaurar headers originais
        if (cachedData.headers) {
          Object.entries(cachedData.headers).forEach(([key, value]) => {
            res.set(key, value);
          });
        }

        return res.status(cachedData.statusCode || 200).send(cachedData.body);
      }

      // Interceptar resposta para cachear
      const originalSend = res.send;
      const originalJson = res.json;
      const originalStatus = res.status;

      let statusCode = 200;

      res.status = function(code) {
        statusCode = code;
        return originalStatus.call(this, code);
      };

      res.send = function(body) {
        // Cachear apenas se for resposta bem-sucedida
        if (!onlySuccessful || (statusCode >= 200 && statusCode < 300)) {
          const dataToCache = {
            body: body,
            statusCode: statusCode,
            headers: extractCacheableHeaders(res),
            cachedAt: new Date().toISOString()
          };

          memoryCacheSet(cacheKey, dataToCache, duration);
        }

        res.set('X-Cache', 'MISS');
        res.set('X-Cache-Key', cacheKey);
        return originalSend.call(this, body);
      };

      res.json = function(obj) {
        // Cachear apenas se for resposta bem-sucedida
        if (!onlySuccessful || (statusCode >= 200 && statusCode < 300)) {
          const dataToCache = {
            body: obj,
            statusCode: statusCode,
            headers: extractCacheableHeaders(res),
            cachedAt: new Date().toISOString()
          };

          memoryCacheSet(cacheKey, dataToCache, duration);
        }

        res.set('X-Cache', 'MISS');
        res.set('X-Cache-Key', cacheKey);
        return originalJson.call(this, obj);
      };

      next();
    } catch (err) {
      console.error('Erro no middleware de cache:', err);
      next(); // Continuar sem cache em caso de erro
    }
  };
};

/**
 * Gera chave única para o cache
 */
const generateCacheKey = (req, prefix, varyBy) => {
  const parts = [prefix];

  varyBy.forEach(factor => {
    switch (factor) {
      case 'url':
        parts.push(req.originalUrl || req.url);
        break;
      case 'user':
        parts.push(req.session.user?.id || 'anonymous');
        break;
      case 'role':
        parts.push(req.session.user?.role || 'guest');
        break;
      case 'method':
        parts.push(req.method);
        break;
      case 'query':
        parts.push(JSON.stringify(req.query));
        break;
      case 'body':
        if (req.method === 'POST' || req.method === 'PUT') {
          parts.push(crypto.createHash('md5').update(JSON.stringify(req.body)).digest('hex'));
        }
        break;
    }
  });

  const key = parts.join(':');
  return crypto.createHash('md5').update(key).digest('hex');
};

/**
 * Extrai headers que devem ser cacheados
 */
const extractCacheableHeaders = (res) => {
  const cacheableHeaders = [
    'content-type',
    'content-encoding',
    'content-language',
    'cache-control',
    'expires',
    'last-modified',
    'etag'
  ];

  const headers = {};
  cacheableHeaders.forEach(header => {
    const value = res.get(header);
    if (value) {
      headers[header] = value;
    }
  });

  return headers;
};

/**
 * Cache específico para dados de usuário
 */
const userDataCache = {
  get: async (userId, key) => {
    try {
      const cacheKey = `user:${userId}:${key}`;
      return memoryCacheGet(cacheKey);
    } catch (err) {
      console.error('Erro ao obter cache de usuário:', err);
      return null;
    }
  },

  set: async (userId, key, data, duration = 300) => {
    try {
      const cacheKey = `user:${userId}:${key}`;
      memoryCacheSet(cacheKey, data, duration);
    } catch (err) {
      console.error('Erro ao salvar cache de usuário:', err);
    }
  },

  del: async (userId, key = '*') => {
    try {
      const pattern = `user:${userId}:${key}`;
      for (const k of memoryCache.keys()) {
        if (k.includes(pattern.replace('*', ''))) {
          memoryCache.delete(k);
        }
      }
    } catch (err) {
      console.error('Erro ao deletar cache de usuário:', err);
    }
  }
};

/**
 * Cache específico para checkers
 */
const checkerCache = {
  get: async (checkerId) => {
    try {
      const cacheKey = `checker:${checkerId}`;
      const cached = await redis.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (err) {
      console.error('Erro ao obter cache de checker:', err);
      return null;
    }
  },

  set: async (checkerId, data, duration = 600) => {
    try {
      const cacheKey = `checker:${checkerId}`;
      await redis.setex(cacheKey, duration, JSON.stringify(data));
    } catch (err) {
      console.error('Erro ao salvar cache de checker:', err);
    }
  },

  del: async (checkerId) => {
    try {
      const cacheKey = `checker:${checkerId}`;
      await redis.del(cacheKey);
    } catch (err) {
      console.error('Erro ao deletar cache de checker:', err);
    }
  },

  clearAll: async () => {
    try {
      const keys = await redis.keys('checker:*');
      if (keys.length > 0) {
        await redis.del(keys);
      }
    } catch (err) {
      console.error('Erro ao limpar cache de checkers:', err);
    }
  }
};

/**
 * Invalidar cache quando dados são modificados
 */
const invalidateCache = async (patterns) => {
  try {
    if (typeof patterns === 'string') {
      patterns = [patterns];
    }

    for (const pattern of patterns) {
      let removedCount = 0;
      for (const key of memoryCache.keys()) {
        if (key.includes(pattern.replace('*', ''))) {
          memoryCache.delete(key);
          removedCount++;
        }
      }
      console.log(`Cache invalidado: ${removedCount} chaves removidas para padrão ${pattern}`);
    }
  } catch (err) {
    console.error('Erro ao invalidar cache:', err);
  }
};

/**
 * Obter estatísticas do cache
 */
const getCacheStats = async () => {
  try {
    const allKeys = Array.from(memoryCache.keys());
    const cacheKeys = allKeys.filter(k => k.startsWith('cache:'));
    const userKeys = allKeys.filter(k => k.startsWith('user:'));
    const checkerKeys = allKeys.filter(k => k.startsWith('checker:'));

    return {
      totalKeys: allKeys.length,
      cacheKeys: cacheKeys.length,
      userCacheKeys: userKeys.length,
      checkerCacheKeys: checkerKeys.length,
      memoryUsage: `${memoryCache.size} items in memory`,
      type: 'memory'
    };
  } catch (err) {
    console.error('Erro ao obter estatísticas do cache:', err);
    return {};
  }
};

module.exports = {
  cacheMiddleware,
  userDataCache,
  checkerCache,
  invalidateCache,
  getCacheStats,
  generateCacheKey
};
