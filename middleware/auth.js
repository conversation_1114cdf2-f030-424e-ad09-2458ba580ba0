const { User, Checker, ActivityLog } = require('../models');

// Helper function to log activity
const logActivity = async (req, action, details = null, resourceType = null, resourceId = null) => {
  try {
    if (req.session && req.session.user) {
      await ActivityLog.create({
        user_id: req.session.user.id,
        action,
        details,
        ip_address: req.ip,
        user_agent: req.headers['user-agent'],
        resource_type: resourceType,
        resource_id: resourceId
      });
    }
  } catch (error) {
    console.error('Error logging activity:', error);
  }
};

module.exports = {
  // Ensure user is authenticated
  ensureAuthenticated: function(req, res, next) {
    if (req.session.user) {
      // Check if user account is active
      User.findByPk(req.session.user.id)
        .then(user => {
          if (!user || user.status !== 'active') {
            req.session.destroy();

            // Check if it's an API request
            if (req.path.startsWith('/api/') || req.originalUrl.startsWith('/api/')) {
              return res.status(401).json({ error: 'Conta suspensa ou desativada' });
            }

            req.flash('error_msg', 'Sua conta foi suspensa ou desativada. Entre em contato com o suporte.');
            return res.redirect('/');
          }

          // Update user session data
          req.session.user = {
            id: user.id,
            rank: user.rank,
            role: user.role,
            saldo: user.saldo,
            lives: user.lives,
            usuario: user.usuario,
            status: user.status
          };

          return next();
        })
        .catch(err => {
          console.error('Error checking user status:', err);

          // Check if it's an API request
          if (req.path.startsWith('/api/') || req.originalUrl.startsWith('/api/')) {
            return res.status(500).json({ error: 'Erro ao verificar status do usuário' });
          }

          req.flash('error_msg', 'Erro ao verificar status do usuário');
          res.redirect('/');
        });
    } else {
      // Check if it's an API request
      if (req.path.startsWith('/api/') || req.originalUrl.startsWith('/api/')) {
        return res.status(401).json({ error: 'Autenticação necessária' });
      }

      req.flash('error_msg', 'Por favor, faça login para acessar esta página');
      res.redirect('/');
    }
  },

  // Ensure user is an admin (rank = 1 or role = admin)
  ensureAdmin: function(req, res, next) {
    if (req.session.user && (req.session.user.rank === 1 || req.session.user.role === 'admin')) {
      logActivity(req, 'admin_access', 'Acesso à área administrativa');
      return next();
    }
    req.flash('error_msg', 'Acesso restrito a administradores');
    res.redirect('/painel');
  },

  // Ensure user is a programmer (role = programmer) or admin
  ensureProgrammer: function(req, res, next) {
    if (req.session.user && (req.session.user.rank === 1 || req.session.user.role === 'admin' || req.session.user.role === 'programmer')) {
      logActivity(req, 'programmer_access', 'Acesso à área de programador');
      return next();
    }
    req.flash('error_msg', 'Acesso restrito a programadores');
    res.redirect('/painel');
  },

  // Ensure user is an affiliate (role = affiliate) or admin
  ensureAffiliate: function(req, res, next) {
    if (req.session.user && (req.session.user.rank === 1 || req.session.user.role === 'admin' || req.session.user.role === 'affiliate')) {
      logActivity(req, 'affiliate_access', 'Acesso à área de afiliado');
      return next();
    }
    req.flash('error_msg', 'Acesso restrito a afiliados');
    res.redirect('/painel');
  },

  // Restrict access to specific admin sections (logs, transactions) to full admins only
  ensureFullAdmin: function(req, res, next) {
    if (req.session.user && (req.session.user.rank === 1 || req.session.user.role === 'admin')) {
      // Programmers can't access logs or transactions
      if (req.session.user.role === 'programmer' &&
         (req.path.includes('/logs') || req.path.includes('/transactions'))) {
        req.flash('error_msg', 'Acesso restrito a administradores completos');
        return res.redirect('/admin');
      }
      logActivity(req, 'admin_access', 'Acesso à área administrativa restrita');
      return next();
    }
    req.flash('error_msg', 'Acesso restrito a administradores');
    res.redirect('/painel');
  },

  // Ensure user has enough balance
  ensureBalance: function(req, res, next) {
    if (req.session.user && req.session.user.saldo > 0) {
      return next();
    }

    // Check if it's an API request
    if (req.path.startsWith('/api/') || req.originalUrl.startsWith('/api/')) {
      return res.status(400).json({ error: 'Créditos insuficientes' });
    }

    req.flash('error_msg', 'Créditos insuficientes');
    res.redirect('/painel');
  },

  // Ensure user has specific role
  ensureRole: function(roles) {
    return function(req, res, next) {
      if (!req.session.user) {
        req.flash('error_msg', 'Por favor, faça login para acessar esta página');
        return res.redirect('/');
      }

      const userRole = req.session.user.role;

      // If roles is a string, convert to array
      const requiredRoles = Array.isArray(roles) ? roles : [roles];

      // Admin role has access to everything
      if (userRole === 'admin' || req.session.user.rank === 1) {
        return next();
      }

      // Check if user has any of the required roles
      if (requiredRoles.includes(userRole)) {
        return next();
      }

      req.flash('error_msg', 'Você não tem permissão para acessar esta página');
      res.redirect('/painel');
    };
  },

  // Ensure user has access to a specific checker
  ensureCheckerAccess: function(req, res, next) {
    const checkerId = req.params.id || req.body.checker_id;

    if (!checkerId) {
      return next();
    }

    Checker.findByPk(checkerId)
      .then(checker => {
        if (!checker) {
          req.flash('error_msg', 'Checker não encontrado');
          return res.redirect('/painel');
        }

        // If checker is disabled or in maintenance
        if (checker.status !== 'active') {
          req.flash('error_msg', `Este checker está ${checker.status === 'maintenance' ? 'em manutenção' : 'desativado'}`);
          return res.redirect('/painel');
        }

        // If checker requires a specific role
        if (checker.required_role !== 'user' &&
            req.session.user.role !== checker.required_role &&
            req.session.user.role !== 'admin' &&
            req.session.user.rank !== 1) {
          req.flash('error_msg', 'Você não tem permissão para acessar este checker');
          return res.redirect('/painel');
        }

        next();
      })
      .catch(err => {
        console.error('Error checking checker access:', err);
        req.flash('error_msg', 'Erro ao verificar acesso ao checker');
        res.redirect('/painel');
      });
  },

  // Log user activity
  logActivity
};
