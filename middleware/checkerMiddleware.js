const { User, Checker, Transaction, Earning } = require('../models');

/**
 * Middleware para processar checkers de forma padronizada
 * Gerencia autenticação, débito de saldo e distribuição de ganhos
 */

/**
 * Distribui os ganhos entre programador, afiliado e site
 * @param {Object} transaction - Transação registrada
 * @param {Object} checker - Checker utilizado
 * @param {number} amount - Valor da transação
 * @returns {boolean} - Se a distribuição foi bem-sucedida
 */
const distributeEarnings = async (transaction, checker, amount) => {
  try {
    // Calcular percentuais de distribuição
    // 20% para afiliado, 20% para o site, 60% para o programador
    const sitePercentage = 0.20;
    const affiliatePercentage = 0.20;
    const programmerPercentage = 0.60;

    // Obter o usuário que usou o checker
    const user = await User.findByPk(transaction.user_id);

    // Encontrar o programador
    if (checker.programmer_id) {
      // Adicionar ganhos para o programador (60%)
      await Earning.create({
        user_id: checker.programmer_id,
        amount: amount * programmerPercentage,
        source_type: 'programmer',
        transaction_id: transaction.id,
        checker_id: checker.id
      });
    }

    // Encontrar o afiliado (se existe)
    if (user.referred_by) {
      // Adicionar ganhos para o afiliado (20%)
      await Earning.create({
        user_id: user.referred_by,
        amount: amount * affiliatePercentage,
        source_type: 'affiliate',
        transaction_id: transaction.id,
        checker_id: checker.id
      });
    }

    // Adicionar ganhos para o site (20%)
    // Encontrar usuário admin (rank 1)
    const admin = await User.findOne({ where: { rank: 1 } });
    if (admin) {
      await Earning.create({
        user_id: admin.id,
        amount: amount * sitePercentage,
        source_type: 'site',
        transaction_id: transaction.id,
        checker_id: checker.id
      });
    }

    return true;
  } catch (err) {
    console.error('Erro ao distribuir ganhos:', err);
    return false;
  }
};

/**
 * Debita o saldo do usuário e distribui ganhos
 * @param {number} userId - ID do usuário
 * @param {number} checkerId - ID do checker
 * @param {number} amount - Quantidade a ser debitada
 * @param {string} chargeType - Tipo de cobrança (per_test ou per_live)
 * @param {boolean} isLive - Se é um resultado "live" (aprovado)
 * @returns {boolean} - Se o débito foi bem-sucedido
 */
const debitBalance = async (userId, checkerId, amount = 1, chargeType = 'per_test', isLive = false) => {
  try {
    const user = await User.findByPk(userId);
    const checker = await Checker.findByPk(checkerId, {
      include: [{ model: User, as: 'programmer' }]
    });

    if (!user || !checker) {
      return false;
    }

    // Verificar se o usuário tem saldo suficiente
    if (user.saldo < amount) {
      return false;
    }

    // Só debitar se for per_test ou se for live (per_live)
    if (chargeType === 'per_test' || (chargeType === 'per_live' && isLive)) {
      const oldBalance = user.saldo;
      user.saldo -= amount;
      user.lives += 1;
      await user.save();

      // Criar registro de transação
      const transaction = await Transaction.create({
        user_id: userId,
        checker_id: checkerId,
        amount: amount,
        type: 'debit',
        description: `Uso do checker ${checker.name}`,
        balance_before: oldBalance,
        balance_after: user.saldo
      });

      // Distribuir ganhos
      await distributeEarnings(transaction, checker, amount);
    }

    return true;
  } catch (err) {
    console.error('Erro ao debitar saldo:', err);
    return false;
  }
};

/**
 * Middleware principal para checkers
 * Valida permissões, saldo e processa o débito
 */
const checkerMiddleware = (endpointName) => {
  return async (req, res, next) => {
    try {
      // 1. Verificar se o usuário está autenticado
      if (!req.session.user) {
        return res.status(401).json({ error: 'Usuário não autenticado' });
      }

      // 2. Buscar o checker no banco de dados
      const checker = await Checker.findOne({
        where: {
          endpoint: endpointName,
          status: 'active'
        }
      });

      if (!checker) {
        return res.status(404).send('Checker não encontrado ou em manutenção');
      }

      // 2.5. VALIDAÇÃO CRÍTICA: Verificar se o código foi aprovado
      const { validateCheckerExecution } = require('../utils/checkerLoader');
      const isCodeApproved = await validateCheckerExecution(checker);

      if (!isCodeApproved) {
        console.error(`🚫 Tentativa de execução de código não aprovado: ${checker.name} (ID: ${checker.id})`);
        return res.status(403).send(`
          <div style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
            <h3 style="color: #dc3545;">🚫 Código Não Aprovado</h3>
            <p>Este checker possui código que não foi aprovado pela equipe de segurança.</p>
            <p><strong>Checker:</strong> ${checker.title}</p>
            <p><strong>Status:</strong> Aguardando revisão de código</p>
            <hr>
            <small style="color: #6c757d;">
              Para administradores: Acesse o painel de revisão de código para aprovar este checker.
            </small>
          </div>
        `);
      }

      // 3. Verificar permissões de acesso
      if (checker.required_role !== 'user' &&
          req.session.user.role !== checker.required_role &&
          req.session.user.role !== 'admin' &&
          req.session.user.rank !== 1) {
        return res.status(403).send('Sem permissão para acessar este checker');
      }

      // 4. Verificar se o usuário tem saldo suficiente
      if (req.session.user.saldo < checker.price) {
        return res.status(402).send('Créditos insuficientes');
      }

      // 5. Adicionar dados do checker ao request para uso posterior
      req.checker = checker;

      // 6. Adicionar função de débito ao request
      req.debitAndDistribute = async (isLive = true) => {
        const debited = await debitBalance(
          req.session.user.id,
          checker.id,
          checker.price,
          checker.charge_type,
          isLive
        );

        if (debited) {
          // Atualizar sessão com dados atualizados
          const updatedUser = await User.findByPk(req.session.user.id);
          req.session.user.saldo = updatedUser.saldo;
          req.session.user.lives = updatedUser.lives;
        }

        return debited;
      };

      next();
    } catch (error) {
      console.error('Erro no middleware do checker:', error);
      return res.status(500).send('Erro interno do servidor');
    }
  };
};

/**
 * Middleware simplificado para checkers que sempre debitam
 * (para compatibilidade com checkers antigos)
 */
const simpleCheckerMiddleware = (endpointName) => {
  return async (req, res, next) => {
    try {
      // Usar o middleware principal
      const mainMiddleware = checkerMiddleware(endpointName);
      
      await new Promise((resolve, reject) => {
        mainMiddleware(req, res, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // Auto-debitar para checkers simples
      const debited = await req.debitAndDistribute(true);
      
      if (!debited) {
        return res.status(402).send('Erro ao debitar créditos');
      }

      next();
    } catch (error) {
      console.error('Erro no middleware simples do checker:', error);
      return res.status(500).send('Erro interno do servidor');
    }
  };
};

module.exports = {
  checkerMiddleware,
  simpleCheckerMiddleware,
  distributeEarnings,
  debitBalance
};
