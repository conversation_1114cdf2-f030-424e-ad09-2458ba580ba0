const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure directories exist
const modulesDir = path.join(__dirname, '../modules/checkers');
if (!fs.existsSync(modulesDir)) {
  fs.mkdirSync(modulesDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, modulesDir);
  },
  filename: function (req, file, cb) {
    // Use checker name or a timestamp if not available
    const checkerName = req.body.name ? req.body.name.replace(/[^a-zA-Z0-9]/g, '_') : Date.now();
    cb(null, `${checkerName}_${Date.now()}${path.extname(file.originalname)}`);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  // Accept only JavaScript files
  if (file.mimetype === 'application/javascript' || 
      file.originalname.endsWith('.js')) {
    cb(null, true);
  } else {
    cb(new Error('Apenas arquivos JavaScript (.js) são permitidos'), false);
  }
};

// Create multer upload instance
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 1024 * 1024 // 1MB limit
  }
});

module.exports = upload;
