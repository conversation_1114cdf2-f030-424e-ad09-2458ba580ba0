require('dotenv').config();
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const flash = require('connect-flash');
const morgan = require('morgan');
const expressLayouts = require('express-ejs-layouts');
const methodOverride = require('method-override');
const cors = require('cors');
const compression = require('compression');
const helmet = require('helmet');

// Importar middlewares de segurança e monitoramento
const { applyRateLimiting } = require('./middleware/rateLimiting');
const { cacheMiddleware } = require('./middleware/cache');
const monitor = require('./utils/monitoring');

// Validação de variáveis de ambiente obrigatórias
const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME', 'SESSION_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('❌ Variáveis de ambiente obrigatórias não encontradas:');
  missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
  console.error('📝 Verifique o arquivo .env e configure as variáveis necessárias.');
  process.exit(1);
}

// Initialize Express app
const app = express();

// Trust proxy - configuração segura baseada no ambiente
const isProduction = process.env.NODE_ENV === 'production';
const trustProxy = process.env.TRUST_PROXY || (isProduction ? '1' : 'false');

if (trustProxy === 'false') {
  app.set('trust proxy', false);
} else if (trustProxy === 'true') {
  app.set('trust proxy', true);
} else if (!isNaN(trustProxy)) {
  app.set('trust proxy', parseInt(trustProxy));
} else {
  // Lista específica de proxies confiáveis
  app.set('trust proxy', trustProxy.split(',').map(ip => ip.trim()));
}

console.log(`🔒 Trust proxy configurado: ${app.get('trust proxy')} (Ambiente: ${process.env.NODE_ENV || 'development'})`);

// Database connection
const db = require('./config/database');
const initDB = require('./config/init-db');

// Test database connection
db.authenticate()
  .then(() => {
    console.log('Database connected...');
    // Initialize database
    initDB();
  })
  .catch(err => console.log('Error: ' + err));

// Security headers - configuração baseada no ambiente
const helmetConfig = {
  contentSecurityPolicy: isProduction ? {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: []
    }
  } : false, // Desabilitar CSP em desenvolvimento
  hsts: isProduction ? {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  } : false,
  crossOriginEmbedderPolicy: false // Evitar problemas com recursos externos
};

app.use(helmet(helmetConfig));
console.log(`🛡️  Helmet configurado para ${isProduction ? 'produção' : 'desenvolvimento'}`);

// Compression
app.use(compression());

// CORS configuration - baseada no ambiente
const corsOptions = {
  origin: isProduction ?
    (process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : false) :
    true, // Em desenvolvimento, permitir qualquer origem
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token']
};

app.use(cors(corsOptions));
console.log(`🌐 CORS configurado: ${isProduction ? 'restritivo' : 'permissivo'}`);

// Monitoring middleware
app.use((req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const responseTime = Date.now() - start;
    monitor.recordRequest(req, res, responseTime);
  });

  next();
});

// Logging - sempre formato dev
app.use(morgan('dev'));

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));
app.use(cookieParser());
app.use(methodOverride('_method'));

// Express session - configuração baseada no ambiente
const sessionConfig = {
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  name: 'privxploit.sid', // Nome customizado para o cookie
  cookie: {
    maxAge: parseInt(process.env.SESSION_MAX_AGE) || 24 * 60 * 60 * 1000, // 24 horas por padrão
    secure: isProduction && process.env.FORCE_HTTPS === 'true', // HTTPS apenas em produção se forçado
    httpOnly: true,
    sameSite: isProduction ? 'strict' : 'lax'
  }
};

// Adicionar store de sessão se especificado
if (process.env.SESSION_STORE === 'redis' && process.env.REDIS_URL) {
  try {
    const RedisStore = require('connect-redis')(session);
    const redis = require('redis');
    const redisClient = redis.createClient(process.env.REDIS_URL);
    sessionConfig.store = new RedisStore({ client: redisClient });
    console.log('📦 Usando Redis para armazenamento de sessões');
  } catch (err) {
    console.log('⚠️  Redis não disponível, usando armazenamento em memória:', err.message);
  }
} else {
  console.log('📦 Usando armazenamento de sessões em memória');
}

app.use(session(sessionConfig));
console.log(`🔐 Sessões configuradas: ${sessionConfig.store ? 'Redis' : 'Memória'}`);

// Connect flash
app.use(flash());

// Rate limiting (após configuração de sessão)
app.use(applyRateLimiting);

// Global variables
app.use(async (req, res, next) => {
  res.locals.success_msg = req.flash('success_msg');
  res.locals.error_msg = req.flash('error_msg');
  res.locals.error = req.flash('error');
  res.locals.user = req.session.user || null;
  res.locals.rateLimitStats = req.rateLimitStats;
  res.locals.isProduction = process.env.NODE_ENV === 'production';

  // Detectar URL base automaticamente
  const protocol = req.secure || req.headers['x-forwarded-proto'] === 'https' ? 'https' : 'http';
  const host = req.headers['x-forwarded-host'] || req.headers.host || req.hostname;
  res.locals.baseUrl = process.env.BASE_URL || `${protocol}://${host}`;

  // Carregar categorias para o sidebar (apenas para rotas autenticadas)
  if (req.session && req.session.user && (req.path.startsWith('/painel') || req.path.startsWith('/admin'))) {
    try {
      const { Category, Checker, CodeReview } = require('./models');
      const categories = await Category.findAll({
        where: { status: 'active' },
        include: [
          {
            model: Checker,
            as: 'checkers',
            where: { status: 'active' },
            required: false
          }
        ],
        order: [
          ['display_order', 'ASC'],
          [{ model: Checker, as: 'checkers' }, 'display_order', 'ASC']
        ]
      });

      // Verificar aprovação de cada checker e adicionar status de aprovação
      for (const category of categories) {
        if (category.checkers && category.checkers.length > 0) {
          for (const checker of category.checkers) {
            // Verificar se o checker foi aprovado
            let isApproved = false;

            if (checker.approved_code_hash) {
              const codeReview = await CodeReview.findOne({
                where: {
                  approved_code_hash: checker.approved_code_hash,
                  status: 'approved'
                }
              });
              isApproved = !!codeReview;
            }

            // Adicionar propriedade de aprovação ao checker
            checker.dataValues.isCodeApproved = isApproved;
          }
        }
      }

      // Filtrar categorias que têm pelo menos um checker
      const categoriesWithCheckers = categories.filter(category =>
        category.checkers && category.checkers.length > 0
      );

      res.locals.categories = categoriesWithCheckers;
    } catch (error) {
      console.error('Erro ao carregar categorias:', error);
      res.locals.categories = [];
    }
  } else {
    res.locals.categories = [];
  }

  // Debug sempre ativo
  const username = (req.session && req.session.user) ? req.session.user.usuario : 'guest';
  console.log(`${req.method} ${req.url} - User: ${username} - Host: ${host}`);

  next();
});

// EJS
app.use(expressLayouts);
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Set static folder sem cache
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: '0', // Sem cache
  etag: false,
  lastModified: false
}));

// Serve markdown documentation files
app.get('/*.md', (req, res) => {
  const filePath = path.join(__dirname, req.path);
  res.sendFile(filePath, (err) => {
    if (err) {
      res.status(404).send('Documentation file not found');
    }
  });
});

// Routes
app.use('/', require('./routes/index'));
app.use('/users', require('./routes/users'));
app.use('/painel', require('./routes/painel'));
app.use('/api/checkers', require('./routes/api/checkers'));
app.use('/api/dynamic', require('./routes/api/dynamicCheckers'));
app.use('/api/payments', require('./routes/payments'));
app.use('/admin', require('./routes/admin'));
app.use('/earnings', require('./routes/earnings'));

// Health check and monitoring routes
app.use('/health', require('./routes/health'));
app.use('/api/monitoring', require('./routes/api/monitoring'));

// Error handling
app.use((req, res) => {
  console.log(`404 - Página não encontrada: ${req.url}`);
  res.status(404).render('error', {
    title: '404 - Página não encontrada',
    message: 'A página que você está procurando não existe.'
  });
});

app.use((err, req, res) => {
  console.error('Erro interno do servidor:', err.stack);
  console.error('URL:', req.url);
  console.error('Method:', req.method);
  const username = (req.session && req.session.user) ? req.session.user.usuario : 'guest';
  console.error('User:', username);

  res.status(500).render('error', {
    title: '500 - Erro interno do servidor',
    message: err.message // Sempre mostrar erro detalhado
  });
});

// Start server - configuração universal
const PORT = process.env.PORT || 80;
const HOST = process.env.HOST || '0.0.0.0';

app.listen(PORT, HOST, () => {
  console.log('='.repeat(60));
  console.log(`🚀 PrivXploit Server Started`);
  console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Server: http://${HOST}:${PORT}`);
  console.log(`📂 Static files: ${path.join(__dirname, 'public')}`);
  console.log(`🗄️  Database: ${process.env.DB_HOST || 'localhost'}:${process.env.DB_NAME || 'ofc'}`);
  console.log(`🔒 Trust proxy: ${app.get('trust proxy')}`);
  console.log(`🌍 Base URL: ${process.env.BASE_URL || 'auto-detect'}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  console.log('='.repeat(60));
});

module.exports = app;
