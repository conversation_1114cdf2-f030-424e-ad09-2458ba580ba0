#!/bin/bash

# Script para verificar se todas as correções estão funcionando
echo "🔧 Verificando correções do sistema de revisão de código..."
echo "============================================================"

# Verificar se a aplicação está rodando
echo "1. 🔍 Verificando se a aplicação está rodando..."
if curl -s http://localhost:3001 > /dev/null; then
    echo "   ✅ Aplicação rodando na porta 3001"
else
    echo "   ❌ Aplicação não está rodando"
    echo "   💡 Execute: PORT=3001 node app.js"
    exit 1
fi

# Verificar painel de code reviews
echo ""
echo "2. 🔍 Verificando painel de code reviews..."
if curl -s http://localhost:3001/admin/code-reviews > /dev/null; then
    echo "   ✅ Painel de code reviews acessível"
else
    echo "   ❌ Painel de code reviews com problema"
fi

# Verificar API de criação de revisões
echo ""
echo "3. 🔍 Verificando API de criação de revisões..."
# Nota: Precisa de autenticação, então só verificamos se a rota existe
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/admin/code-reviews/create-missing | grep -q "40[13]"; then
    echo "   ✅ API de criação de revisões disponível (requer autenticação)"
else
    echo "   ❌ API de criação de revisões com problema"
fi

# Testar script de verificação
echo ""
echo "4. 🔍 Testando script de verificação..."
cd /home/<USER>/Projetos/privxploit
if timeout 10 node test-code-review-fixed.js > /dev/null 2>&1; then
    echo "   ✅ Script de teste funcionando"
else
    echo "   ❌ Script de teste com problema"
fi

echo ""
echo "============================================================"
echo "✅ Verificação concluída!"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "1. Acesse: http://localhost:3001/admin/code-reviews"
echo "2. Faça login como administrador"
echo "3. Revise e aprove os códigos pendentes"
echo "4. Monitore o sistema regularmente"
echo ""
echo "🎉 Sistema de revisão de código totalmente funcional!"
