# 🚀 PrivXploit - Advanced Security Testing Platform

Sistema avançado de testes de segurança desenvolvido em Node.js com Express, oferecendo uma plataforma completa para validação de dados e testes de penetração.

## ✨ Características

- 🔐 Sistema de autenticação robusto
- 💳 Integração com PIX e criptomoedas
- 🎯 Múltiplos checkers de segurança
- 👥 Painel administrativo completo
- 📊 Sistema de créditos e transações
- 🌐 Interface moderna e responsiva

## 📋 Requisitos

- Node.js (v18 ou superior)
- MySQL (v8.0 ou superior)
- PM2 (para produção)

## 🚀 Instalação

1. Clone o repositório:
```bash
git clone https://github.com/seu-usuario/privxploit.git
cd privxploit
```

2. Instale as dependências:
```bash
npm install
```

3. Configure o ambiente:
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

4. Configure o banco de dados:
```bash
# Crie o banco de dados
mysql -u root -p -e "CREATE DATABASE ofc;"

# Crie o usuário
mysql -u root -p -e "CREATE USER 'cancrosoft'@'localhost' IDENTIFIED BY 'cancrosoft123';"
mysql -u root -p -e "GRANT ALL PRIVILEGES ON ofc.* TO 'cancrosoft'@'localhost';"
mysql -u root -p -e "FLUSH PRIVILEGES;"

# Importe a estrutura
mysql -u cancrosoft -pcancrosoft123 ofc < database_complete.sql
```

4. Configure as variáveis de ambiente:
   - Renomeie o arquivo `.env.example` para `.env`
   - Edite o arquivo `.env` com suas configurações de banco de dados

5. Inicie a aplicação:

**Desenvolvimento (porta 3000):**
```bash
./start-universal.sh
```

**Produção (porta 80):**
```bash
sudo ./start-port80.sh
```

**Com PM2:**
```bash
pm2 start ecosystem.config.js
pm2 save
```

## 🌐 Acesso

- **Site:** http://localhost:3000 (desenvolvimento) ou http://localhost (produção)
- **Admin:** http://localhost:3000/admin
- **Usuário padrão:** admin / admin

## 📁 Estrutura do Projeto

```
privxploit/
├── 📄 app.js                    # Aplicação principal
├── 📄 .env.example              # Configuração de exemplo
├── 📄 ecosystem.config.js       # Configuração PM2
├── 📄 package.json              # Dependências
├── 📄 database_complete.sql     # Estrutura do banco
├── 📁 config/                   # Configurações
├── 📁 middleware/               # Middlewares
├── 📁 models/                   # Modelos do banco
├── 📁 routes/                   # Rotas da aplicação
├── 📁 views/                    # Templates EJS
├── 📁 public/                   # Arquivos estáticos
├── 📁 services/                 # Serviços
└── 📁 utils/                    # Utilitários
```

## 🚀 Deploy em VPS

1. **Preparar arquivo:**
```bash
./deploy-manual.sh
```

2. **Enviar para VPS:**
```bash
scp /tmp/privxploit-deploy.tar.gz root@SEU_IP:/root/
```

3. **Configurar na VPS:**
```bash
ssh root@SEU_IP
cd /root
tar -xzf privxploit-deploy.tar.gz
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt-get install -y nodejs npm mysql-server
npm install -g pm2
npm install
# Configure MySQL e importe banco
pm2 start ecosystem.config.js
```

## 💳 Pagamentos

- **PIX:** Integração com PagFly API
- **Crypto:** Integração com NowPayments
- **Configuração:** Edite as chaves no arquivo .env

## 🔧 Desenvolvimento

```bash
# Modo desenvolvimento com recarga automática
npm run dev

# Executar testes
npm test

# Ver logs
pm2 logs

# Monitoramento
pm2 monit
```

## 📊 Funcionalidades

- ✅ Sistema de autenticação robusto
- ✅ Painel administrativo completo
- ✅ Sistema de créditos e transações
- ✅ Múltiplos checkers de segurança
- ✅ Integração com pagamentos PIX e crypto
- ✅ Interface moderna e responsiva
- ✅ Sistema de logs e monitoramento
- ✅ Deploy automatizado para VPS

## 🛡️ Segurança

- Autenticação JWT
- Proteção contra CSRF
- Rate limiting
- Validação de entrada
- Sanitização de dados
- Logs de auditoria

## 📝 Licença

Este projeto é privado e proprietário.

## 🤝 Suporte

Para suporte técnico, entre em contato através dos canais oficiais.

---

**PrivXploit** - Advanced Security Testing Platform
