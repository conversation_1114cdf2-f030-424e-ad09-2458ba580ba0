#!/bin/bash

# Script para configurar autenticação do Git com token
# Este script configura o Git para usar token de acesso pessoal em vez de username/password

echo "🔧 Configurando autenticação do Git com token..."

# Verificar se o diretório do projeto existe
PROJECT_DIR="/var/www/privxploit"

if [ ! -d "$PROJECT_DIR" ]; then
    echo "📁 Criando diretório do projeto..."
    sudo mkdir -p "$PROJECT_DIR"
    sudo chown $USER:$USER "$PROJECT_DIR"
fi

cd "$PROJECT_DIR"

# Verificar se já é um repositório Git
if [ ! -d ".git" ]; then
    echo "📥 Clonando repositório do GitHub..."
    git clone https://github.com/MatxCoder/privxploit.git .
fi

echo "⚙️  Configurando Git..."

# Configurar Git para usar credential helper
git config credential.helper store

# Configurar usuário (substitua pelos seus dados)
echo "👤 Configurando usuário Git..."
read -p "Digite seu nome de usuário GitHub: " GITHUB_USERNAME
read -p "Digite seu email GitHub: " GITHUB_EMAIL

git config user.name "$GITHUB_USERNAME"
git config user.email "$GITHUB_EMAIL"

echo ""
echo "🔑 Para configurar o token de acesso:"
echo "1. Vá para: https://github.com/settings/tokens"
echo "2. Clique em 'Generate new token (classic)'"
echo "3. Selecione os escopos: repo, workflow, write:packages"
echo "4. Copie o token gerado"
echo ""

read -p "Cole seu token de acesso pessoal aqui: " GITHUB_TOKEN

# Configurar URL remota com token
REPO_URL="https://${GITHUB_USERNAME}:${GITHUB_TOKEN}@github.com/MatxCoder/privxploit.git"
git remote set-url origin "$REPO_URL"

echo "✅ Configuração concluída!"
echo ""
echo "🧪 Testando configuração..."
git fetch origin

if [ $? -eq 0 ]; then
    echo "✅ Autenticação configurada com sucesso!"
    echo "📊 Status do repositório:"
    git status
else
    echo "❌ Erro na configuração. Verifique o token e tente novamente."
fi

echo ""
echo "💡 Comandos úteis:"
echo "   git pull origin main    # Baixar atualizações"
echo "   git push origin main    # Enviar alterações"
echo "   git status              # Ver status do repositório"
