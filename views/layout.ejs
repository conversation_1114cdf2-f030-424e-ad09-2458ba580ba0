<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title><%= typeof title !== 'undefined' ? title : 'PRIVXPLOIT' %></title>

  <link rel="shortcut icon" type="imagem/x-icon" href="/img/raio.svg"/>

  <!-- CSS Files -->
  <link href="/css/bootstrap-essential.css?v=3" rel="stylesheet">
  <link href="/css/vendor/all.css?v=3" rel="stylesheet">
  <link href="/css/nucleo-icons.css?v=3" rel="stylesheet">
  <link href="/css/modern-dashboard.css?v=3" rel="stylesheet">
  <link href="/css/modern-components.css?v=3" rel="stylesheet">
  <link href="/css/demo.css?v=3" rel="stylesheet">
  <% if (typeof user !== 'undefined' && user && (user.rank === 1 || user.role === 'admin')) { %>
  <link rel="preload" href="/css/vendor/dataTables.bootstrap4.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="/css/admin.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <% } %>
  <%- typeof extraStyles !== 'undefined' ? extraStyles : '' %>


</head>
<body class="<% if (typeof user !== 'undefined' && user && (user.rank === 1 || user.role === 'admin')) { %>admin-panel<% } %><% if (typeof user !== 'undefined' && user) { %> sidebar-present<% } %>">


  <div class="wrapper">
    <% if (typeof user !== 'undefined' && user) { %>
      <%- include('partials/modern-sidebar') %>
    <% } %>

    <div class="main-panel">
    <% if (typeof user !== 'undefined' && user) { %>
      <%- include('partials/modern-navbar') %>
    <% } %>

    <div class="content">
      <%- body %>
    </div>

    <% if (typeof showFooter !== 'undefined' && showFooter) { %>
      <footer class="footer-section">
        <h5 class="text-center text-muted">© 2024 t.me/PrivXploit Todos os direitos reservados.<br></h5>
      </footer>
    <% } %>
    </div>
  </div>

  <div class="fixed-plugin">
    <div class="dropdown show-dropdown">
      <a data-toggle="dropdown">
        <i class="fa fa-cog fa-2x"> </i>
      </a>
      <ul class="dropdown-menu">
        <li class="header-title">CORES DO MENU</li>
        <li class="adjustments-line">
          <a href="javascript:void(0)" class="switch-trigger background-color">
            <div class="badge-colors text-center">
              <span class="badge filter badge-primary active" data-color="primary"></span>
              <span class="badge filter badge-info" data-color="blue"></span>
              <span class="badge filter badge-success" data-color="green"></span>
            </div>
            <div class="clearfix"></div>
          </a>
        </li>
        <li class="adjustments-line text-center color-change">
          <span class="color-label">TEMA CLARO</span>
          <span class="badge light-badge mr-2"></span>
          <span class="badge dark-badge ml-2"></span>
          <span class="color-label">TEMA ESCURO</span>
        </li>
        <li class="button-container">
          <a href="https://t.me/PrivXploit" target="_blank" class="btn btn-primary btn-block">CONTATO TELEGRAM</a>
          <a href="https://t.me/PrivXploit" target="_blank" class="btn btn-success btn-block">GRUPO TELEGRAM</a>
        </li>
      </ul>
    </div>
  </div>

  <script src="/js/core/jquery.min.js"></script>
  <script src="/js/core/popper.min.js"></script>
  <script src="/js/core/bootstrap.min.js"></script>
  <script src="/js/plugins/perfect-scrollbar.jquery.min.js"></script>
  <script src="/js/plugins/bootstrap-notify.js"></script>
  <!-- QRCode.js Library for PIX QR Code generation -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
  <script src="/js/blackdashboard.js"></script>
  <script src="/js/modern-sidebar.js"></script>
  <script src="/js/document.js"></script>
  <script src="/js/demo.js"></script>
  <% if (typeof user !== 'undefined' && user && (user.rank === 1 || user.role === 'admin')) { %>
  <script src="/js/admin.js"></script>
  <% } %>



  <script>
    $(document).ready(function() {
      // Initialize collapse functionality
      $('[data-toggle="collapse"]').on('click', function(e) {
        e.preventDefault();

        const targetId = $(this).attr('href');
        const $target = $(targetId);
        const isExpanded = $(this).attr('aria-expanded') === 'true';

        if ($target.length) {
          if (isExpanded) {
            // Collapse
            $target.removeClass('show');
            $(this).attr('aria-expanded', 'false');
          } else {
            // Expand
            $target.addClass('show');
            $(this).attr('aria-expanded', 'true');

            // Close other open menus (accordion behavior)
            $('[data-toggle="collapse"]').not(this).each(function() {
              const otherTargetId = $(this).attr('href');
              const $otherTarget = $(otherTargetId);

              if ($otherTarget.hasClass('show')) {
                $otherTarget.removeClass('show');
                $(this).attr('aria-expanded', 'false');
              }
            });
          }

          // Animate caret
          const $caret = $(this).find('.caret');
          if ($caret.length) {
            if (isExpanded) {
              $caret.css('transform', 'rotate(0deg)');
            } else {
              $caret.css('transform', 'rotate(180deg)');
            }
          }
        }
      });

      $().ready(function() {
        $sidebar = $('.sidebar');
        $navbar = $('.navbar');
        $main_panel = $('.main-panel');

        $full_page = $('.full-page');

        $sidebar_responsive = $('body > .navbar-collapse');
        sidebar_mini_active = true;
        white_color = false;

        window_width = $(window).width();

        fixed_plugin_open = $('.sidebar .sidebar-wrapper .nav li.active a p').html();

        $('.fixed-plugin a').click(function(event) {
          if ($(this).hasClass('switch-trigger')) {
            if (event.stopPropagation) {
              event.stopPropagation();
            } else if (window.event) {
              window.event.cancelBubble = true;
            }
          }
        });

        $('.fixed-plugin .background-color span').click(function() {
          $(this).siblings().removeClass('active');
          $(this).addClass('active');

          var new_color = $(this).data('color');

          if ($sidebar.length != 0) {
            $sidebar.attr('data', new_color);
          }

          if ($main_panel.length != 0) {
            $main_panel.attr('data', new_color);
          }

          if ($full_page.length != 0) {
            $full_page.attr('filter-color', new_color);
          }

          if ($sidebar_responsive.length != 0) {
            $sidebar_responsive.attr('data', new_color);
          }
        });

        $('.light-badge').click(function() {
          $('body').addClass('white-content');
        });

        $('.dark-badge').click(function() {
          $('body').removeClass('white-content');
        });
      });
    });
  </script>

  <%- typeof extraScripts !== 'undefined' ? extraScripts : '' %>
</body>
</html>
