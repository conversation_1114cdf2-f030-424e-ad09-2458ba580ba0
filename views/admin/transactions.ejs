<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-exchange-alt text-primary"></i> <b>TRANSAÇÕES</b></h4>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>ID</th>
                <th>Usuário</th>
                <th>Tipo</th>
                <th>Valor</th>
                <th>Descrição</th>
                <th>Checker</th>
                <th>Admin</th>
                <th>Saldo Anterior</th>
                <th>Saldo Atual</th>
                <th>Data</th>
              </tr>
            </thead>
            <tbody>
              <% if (transactions && transactions.length > 0) { %>
                <% transactions.forEach(transaction => { %>
                  <tr>
                    <td><%= transaction.id %></td>
                    <td><%= transaction.user ? transaction.user.usuario : 'N/A' %></td>
                    <td>
                      <% if (transaction.type === 'credit') { %>
                        <span class="text-success">Crédito</span>
                      <% } else { %>
                        <span class="text-danger">Débito</span>
                      <% } %>
                    </td>
                    <td><%= transaction.amount.toFixed(2) %></td>
                    <td><%= transaction.description %></td>
                    <td><%= transaction.checker ? transaction.checker.name : 'N/A' %></td>
                    <td><%= transaction.admin ? transaction.admin.usuario : 'N/A' %></td>
                    <td><%= transaction.balance_before.toFixed(2) %></td>
                    <td><%= transaction.balance_after.toFixed(2) %></td>
                    <td><%= transaction.createdAt ? new Date(transaction.createdAt).toLocaleString('pt-BR') : 'N/A' %></td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="10" class="text-center">Nenhuma transação encontrada</td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
        
        <% if (pagination && pagination.totalPages > 1) { %>
          <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Navegação de páginas">
              <ul class="pagination">
                <% if (pagination.page > 1) { %>
                  <li class="page-item">
                    <a class="page-link" href="/admin/transactions?page=<%= pagination.page - 1 %>" aria-label="Anterior">
                      <span aria-hidden="true">&laquo;</span>
                    </a>
                  </li>
                <% } %>
                
                <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                  <% if (i === pagination.page) { %>
                    <li class="page-item active"><a class="page-link" href="#"><%= i %></a></li>
                  <% } else if (i === 1 || i === pagination.totalPages || (i >= pagination.page - 2 && i <= pagination.page + 2)) { %>
                    <li class="page-item"><a class="page-link" href="/admin/transactions?page=<%= i %>"><%= i %></a></li>
                  <% } else if (i === pagination.page - 3 || i === pagination.page + 3) { %>
                    <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                  <% } %>
                <% } %>
                
                <% if (pagination.page < pagination.totalPages) { %>
                  <li class="page-item">
                    <a class="page-link" href="/admin/transactions?page=<%= pagination.page + 1 %>" aria-label="Próximo">
                      <span aria-hidden="true">&raquo;</span>
                    </a>
                  </li>
                <% } %>
              </ul>
            </nav>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>
