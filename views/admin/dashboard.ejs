<div class="row fade-in">
  <!-- Hidden elements for flash messages -->
  <div id="success-message" class="d-none"><%= typeof success_msg !== 'undefined' ? success_msg : '' %></div>
  <div id="error-message" class="d-none"><%= typeof error_msg !== 'undefined' ? error_msg : '' %></div>

  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title"><i class="fa fa-tachometer-alt text-primary mr-2"></i> <b>PAINEL ADMINISTRATIVO</b></h4>
        <div class="d-flex">
          <button class="btn btn-sm btn-primary mr-2" onclick="location.reload()">
            <i class="fa fa-sync-alt"></i> Atualizar
          </button>
          <button class="btn btn-sm btn-info" id="minimizeSidebar">
            <i class="fa fa-bars"></i> Menu
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5">
                    <div class="icon-big text-center">
                      <i class="fa fa-users text-warning"></i>
                    </div>
                  </div>
                  <div class="col-7">
                    <div class="numbers">
                      <p class="card-category">Usuários</p>
                      <h3 class="card-title"><%= userCount %></h3>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <hr>
                <div class="stats d-flex justify-content-between">
                  <div><i class="fa fa-user-check"></i> <%= activeUserCount %> ativos</div>
                  <a href="/admin/users" class="text-warning">Ver todos <i class="fa fa-arrow-right"></i></a>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5">
                    <div class="icon-big text-center">
                      <i class="fa fa-check-circle text-success"></i>
                    </div>
                  </div>
                  <div class="col-7">
                    <div class="numbers">
                      <p class="card-category">Checkers</p>
                      <h3 class="card-title"><%= checkerCount %></h3>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <hr>
                <div class="stats d-flex justify-content-between">
                  <div><i class="fa fa-check"></i> <%= activeCheckerCount %> ativos</div>
                  <a href="/admin/checkers" class="text-success">Ver todos <i class="fa fa-arrow-right"></i></a>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5">
                    <div class="icon-big text-center">
                      <i class="fa fa-folder text-danger"></i>
                    </div>
                  </div>
                  <div class="col-7">
                    <div class="numbers">
                      <p class="card-category">Categorias</p>
                      <h3 class="card-title"><%= categoryCount %></h3>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <hr>
                <div class="stats d-flex justify-content-between">
                  <div><i class="fa fa-folder-open"></i> Categorias</div>
                  <a href="/admin/categories" class="text-danger">Ver todas <i class="fa fa-arrow-right"></i></a>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5">
                    <div class="icon-big text-center">
                      <i class="fa fa-exchange-alt text-primary"></i>
                    </div>
                  </div>
                  <div class="col-7">
                    <div class="numbers">
                      <p class="card-category">Transações</p>
                      <h3 class="card-title"><%= transactionCount %></h3>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <hr>
                <div class="stats d-flex justify-content-between">
                  <div><i class="fa fa-credit-card"></i> Transações</div>
                  <a href="/admin/transactions" class="text-primary">Ver todas <i class="fa fa-arrow-right"></i></a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-lg-6 col-md-12 mb-4">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title"><i class="fa fa-exchange-alt text-primary mr-2"></i> Transações Recentes</h5>
                <a href="/admin/transactions" class="btn btn-sm btn-outline-primary">Ver Todas</a>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead>
                      <tr>
                        <th>Usuário</th>
                        <th>Tipo</th>
                        <th>Valor</th>
                        <th>Data</th>
                      </tr>
                    </thead>
                    <tbody>
                      <% if (recentTransactions && recentTransactions.length > 0) { %>
                        <% recentTransactions.forEach(transaction => { %>
                          <tr>
                            <td><strong><%= transaction.user ? transaction.user.usuario : 'N/A' %></strong></td>
                            <td>
                              <span class="badge badge-<%= transaction.type === 'credit' ? 'success' : 'danger' %>">
                                <%= transaction.type === 'credit' ? 'Crédito' : 'Débito' %>
                              </span>
                            </td>
                            <td><%= transaction.amount.toFixed(2) %></td>
                            <td><%= new Date(transaction.createdAt).toLocaleString('pt-BR', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            }) %></td>
                          </tr>
                        <% }); %>
                      <% } else { %>
                        <tr>
                          <td colspan="4" class="text-center py-3">
                            <i class="fa fa-info-circle mr-1"></i> Nenhuma transação encontrada
                          </td>
                        </tr>
                      <% } %>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-6 col-md-12 mb-4">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title"><i class="fa fa-history text-primary mr-2"></i> Atividades Recentes</h5>
                <a href="/admin/logs" class="btn btn-sm btn-outline-primary">Ver Todas</a>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead>
                      <tr>
                        <th>Usuário</th>
                        <th>Ação</th>
                        <th>Data</th>
                      </tr>
                    </thead>
                    <tbody>
                      <% if (recentActivity && recentActivity.length > 0) { %>
                        <% recentActivity.forEach(activity => { %>
                          <tr>
                            <td><strong><%= activity.user ? activity.user.usuario : 'N/A' %></strong></td>
                            <td><%= activity.action.replace(/_/g, ' ') %></td>
                            <td><%= new Date(activity.createdAt).toLocaleString('pt-BR', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            }) %></td>
                          </tr>
                        <% }); %>
                      <% } else { %>
                        <tr>
                          <td colspan="3" class="text-center py-3">
                            <i class="fa fa-info-circle mr-1"></i> Nenhuma atividade encontrada
                          </td>
                        </tr>
                      <% } %>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title"><i class="fa fa-cogs text-primary mr-2"></i> Gerenciamento Rápido</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-primary text-white">
                      <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                          <div>
                            <h5 class="mb-1">Usuários</h5>
                            <p class="mb-0 small">Gerenciar contas de usuários</p>
                          </div>
                          <div class="icon-shape rounded-circle bg-white text-primary">
                            <i class="fa fa-users"></i>
                          </div>
                        </div>
                        <div class="mt-3">
                          <a href="/admin/users" class="btn btn-sm btn-outline-light">Gerenciar</a>
                          <a href="/admin/users/add" class="btn btn-sm btn-light">Adicionar</a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-success text-white">
                      <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                          <div>
                            <h5 class="mb-1">Categorias</h5>
                            <p class="mb-0 small">Organizar checkers por categoria</p>
                          </div>
                          <div class="icon-shape rounded-circle bg-white text-success">
                            <i class="fa fa-folder"></i>
                          </div>
                        </div>
                        <div class="mt-3">
                          <a href="/admin/categories" class="btn btn-sm btn-outline-light">Gerenciar</a>
                          <a href="/admin/categories/add" class="btn btn-sm btn-light">Adicionar</a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-info text-white">
                      <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                          <div>
                            <h5 class="mb-1">Checkers</h5>
                            <p class="mb-0 small">Gerenciar ferramentas de checagem</p>
                          </div>
                          <div class="icon-shape rounded-circle bg-white text-info">
                            <i class="fa fa-check-circle"></i>
                          </div>
                        </div>
                        <div class="mt-3">
                          <a href="/admin/checkers" class="btn btn-sm btn-outline-light">Gerenciar</a>
                          <a href="/admin/checkers/add" class="btn btn-sm btn-light">Adicionar</a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-warning text-white">
                      <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                          <div>
                            <h5 class="mb-1">Planos</h5>
                            <p class="mb-0 small">Gerenciar planos de assinatura</p>
                          </div>
                          <div class="icon-shape rounded-circle bg-white text-warning">
                            <i class="fa fa-tags"></i>
                          </div>
                        </div>
                        <div class="mt-3">
                          <a href="/admin/plans" class="btn btn-sm btn-outline-light">Gerenciar</a>
                          <a href="/admin/plans/add" class="btn btn-sm btn-light">Adicionar</a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Second Row -->
                <div class="row">
                  <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-dark text-white">
                      <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                          <div>
                            <h5 class="mb-1">Controle de Versão</h5>
                            <p class="mb-0 small">Gerenciar atualizações do GitHub</p>
                          </div>
                          <div class="icon-shape rounded-circle bg-white text-dark">
                            <i class="fab fa-github"></i>
                          </div>
                        </div>
                        <div class="mt-3">
                          <a href="/admin/version-control" class="btn btn-sm btn-outline-light">Gerenciar</a>
                          <button class="btn btn-sm btn-light" onclick="checkUpdatesQuick()">Verificar</button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-gradient-secondary text-white">
                      <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                          <div>
                            <h5 class="mb-1">Monitoramento</h5>
                            <p class="mb-0 small">Status do sistema em tempo real</p>
                          </div>
                          <div class="icon-shape rounded-circle bg-white text-secondary">
                            <i class="fa fa-chart-line"></i>
                          </div>
                        </div>
                        <div class="mt-3">
                          <a href="/admin/monitoring" class="btn btn-sm btn-outline-light">Ver Status</a>
                          <a href="/health/detailed" target="_blank" class="btn btn-sm btn-light">Health</a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <style>
          .icon-shape {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
          }
          .bg-gradient-primary {
            background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%) !important;
          }
          .bg-gradient-success {
            background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%) !important;
          }
          .bg-gradient-info {
            background: linear-gradient(87deg, #11cdef 0, #1171ef 100%) !important;
          }
          .bg-gradient-warning {
            background: linear-gradient(87deg, #fb6340 0, #fbb140 100%) !important;
          }
        </style>
      </div>
    </div>
  </div>
</div>

<script>
// Quick version check function
function checkUpdatesQuick() {
  $.notify({
    message: 'Verificando atualizações...'
  }, {
    type: 'info',
    placement: {
      from: 'top',
      align: 'right'
    },
    delay: 2000
  });

  fetch('/admin/version-control/check-updates', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      if (data.updateAvailable) {
        $.notify({
          message: `Nova versão disponível! ${data.behindCount} commits atrás. <a href="/admin/version-control" class="text-white"><u>Gerenciar</u></a>`
        }, {
          type: 'warning',
          placement: {
            from: 'top',
            align: 'right'
          },
          delay: 8000,
          allow_dismiss: true
        });
      } else {
        $.notify({
          message: 'Sistema está atualizado!'
        }, {
          type: 'success',
          placement: {
            from: 'top',
            align: 'right'
          },
          delay: 3000
        });
      }
    } else {
      $.notify({
        message: 'Erro ao verificar atualizações: ' + data.error
      }, {
        type: 'danger',
        placement: {
          from: 'top',
          align: 'right'
        },
        delay: 5000
      });
    }
  })
  .catch(error => {
    $.notify({
      message: 'Erro de conexão: ' + error.message
    }, {
      type: 'danger',
      placement: {
        from: 'top',
        align: 'right'
      },
      delay: 5000
    });
  });
}
</script>
