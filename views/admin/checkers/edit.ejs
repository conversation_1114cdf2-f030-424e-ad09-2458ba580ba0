<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-edit text-primary"></i> <b>EDITAR CHECKER</b></h4>
      </div>
      <div class="card-body">
        <form method="POST" action="/admin/checkers/edit/<%= checker.id %>" enctype="multipart/form-data">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="name">Nome do Checker (identificador)</label>
                <input type="text" class="form-control" id="name" name="name" value="<%= checker.name %>" required>
                <small class="form-text text-muted">Usado na URL, sem espaços ou caracteres especiais</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="title"><PERSON><PERSON><PERSON><PERSON> do Checker</label>
                <input type="text" class="form-control" id="title" name="title" value="<%= checker.title %>" required>
                <small class="form-text text-muted">Nome exibido para os usuários</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="endpoint">Endpoint da API</label>
                <input type="text" class="form-control" id="endpoint" name="endpoint" value="<%= checker.endpoint %>" required>
                <small class="form-text text-muted">Ex: /api/checkers/kabum</small>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="category_id">Categoria</label>
                <select class="form-control" id="category_id" name="category_id" required>
                  <option value="">Selecione uma categoria</option>
                  <% if (categories && categories.length > 0) { %>
                    <% categories.forEach(category => { %>
                      <option value="<%= category.id %>" <%= checker.category_id == category.id ? 'selected' : '' %>><%= category.name %></option>
                    <% }); %>
                  <% } %>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="price">Preço (créditos)</label>
                <input type="number" step="0.01" class="form-control" id="price" name="price" value="<%= checker.price %>" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="programmer_id">Programador</label>
                <select class="form-control" id="programmer_id" name="programmer_id">
                  <option value="">Selecione um programador</option>
                  <% if (programmers && programmers.length > 0) { %>
                    <% programmers.forEach(programmer => { %>
                      <option value="<%= programmer.id %>" <%= checker.programmer_id == programmer.id ? 'selected' : '' %>><%= programmer.usuario %></option>
                    <% }); %>
                  <% } %>
                </select>
                <small class="form-text text-muted">Programador que receberá 60% dos créditos gastos neste checker</small>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="description">Descrição</label>
            <textarea class="form-control" id="description" name="description" rows="3"><%= checker.description || '' %></textarea>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="status">Status</label>
                <select class="form-control" id="status" name="status">
                  <option value="active" <%= checker.status === 'active' ? 'selected' : '' %>>Ativo</option>
                  <option value="maintenance" <%= checker.status === 'maintenance' ? 'selected' : '' %>>Manutenção</option>
                  <option value="disabled" <%= checker.status === 'disabled' ? 'selected' : '' %>>Desativado</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="charge_type">Tipo de Cobrança</label>
                <select class="form-control" id="charge_type" name="charge_type">
                  <option value="per_test" <%= checker.charge_type === 'per_test' ? 'selected' : '' %>>Por teste</option>
                  <option value="per_live" <%= checker.charge_type === 'per_live' ? 'selected' : '' %>>Por live</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="required_role">Função Requerida</label>
                <select class="form-control" id="required_role" name="required_role">
                  <option value="user" <%= checker.required_role === 'user' ? 'selected' : '' %>>Usuário</option>
                  <option value="premium" <%= checker.required_role === 'premium' ? 'selected' : '' %>>Premium</option>
                  <option value="vip" <%= checker.required_role === 'vip' ? 'selected' : '' %>>VIP</option>
                  <option value="admin" <%= checker.required_role === 'admin' ? 'selected' : '' %>>Administrador</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="icon">Ícone (classe FontAwesome)</label>
                <input type="text" class="form-control" id="icon" name="icon" value="<%= checker.icon %>">
                <small class="form-text text-muted">Ex: fa fa-credit-card, fa fa-check, etc.</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="background_image">Imagem de Fundo (URL)</label>
                <input type="text" class="form-control" id="background_image" name="background_image" value="<%= checker.background_image || '' %>">
                <small class="form-text text-muted">URL da imagem de fundo (opcional)</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="display_order">Ordem de Exibição</label>
                <input type="number" class="form-control" id="display_order" name="display_order" value="<%= checker.display_order %>">
              </div>
            </div>
          </div>

          <div class="card mt-4 mb-4">
            <div class="card-header">
              <h5 class="card-title"><i class="fa fa-code"></i> Implementação do Checker</h5>
            </div>
            <div class="card-body">
              <div class="alert alert-info">
                <p><i class="fa fa-info-circle"></i> Você pode implementar o checker de duas formas:</p>
                <ol>
                  <li>Fazendo upload de um arquivo JavaScript que será carregado como módulo</li>
                  <li>Escrevendo o código diretamente no editor abaixo</li>
                </ol>
              </div>

              <div class="form-group">
                <label for="module_file">Upload de Módulo JavaScript</label>
                <input type="file" class="form-control-file" id="module_file" name="module_file" accept=".js">
                <% if (checker.module_path) { %>
                  <small class="form-text text-success">
                    <i class="fa fa-check-circle"></i> Módulo atual: <%= checker.module_path %>
                  </small>
                <% } %>
                <small class="form-text text-muted">
                  O arquivo deve exportar uma função que recebe os parâmetros (req, res, checker) e retorna uma Promise.
                </small>
              </div>

              <div class="form-group">
                <label for="custom_code">Código Personalizado</label>
                <textarea class="form-control code-editor" id="custom_code" name="custom_code" rows="15" style="font-family: monospace;"><%= checker.custom_code || `// Exemplo de implementação:
module.exports = async function(req, res, checker) {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).send('Parâmetro lista é obrigatório');
    }

    // Simular processamento
    const isApproved = Math.random() > 0.7; // 30% de chance de aprovação

    if (isApproved) {
      return res.send(\`<span style="color: green; font-weight: bold;">Aprovada</span> | \${lista}\`);
    } else {
      return res.send(\`<span style="color: red; font-weight: bold;">Reprovada</span> | \${lista}\`);
    }
  } catch (err) {
    console.error(err);
    return res.status(500).send('Erro ao processar requisição');
  }
};` %></textarea>
                <small class="form-text text-muted">
                  Este código será executado quando o endpoint do checker for chamado. Ele deve exportar uma função que recebe os parâmetros (req, res, checker).
                </small>
              </div>
            </div>
          </div>

          <div class="form-group text-center">
            <button type="submit" class="btn btn-primary">Atualizar Checker</button>
            <a href="/admin/checkers" class="btn btn-secondary">Cancelar</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
