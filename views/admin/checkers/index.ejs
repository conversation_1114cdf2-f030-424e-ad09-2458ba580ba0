<!-- Modern Checkers Management Page -->
<div class="checkers-page">
  <div class="container-fluid px-4">
    <!-- <PERSON> Header -->
    <div class="page-header mb-4">
      <div class="row align-items-center">
        <div class="col">
          <h1 class="page-title">
            <i class="fas fa-check-circle text-primary me-3"></i>
            Gerenciar Checkers
          </h1>
          <p class="page-subtitle text-muted">G<PERSON><PERSON><PERSON> todas as ferramentas de verificação do sistema</p>
        </div>
        <div class="col-auto">
          <div class="page-actions">
            <a href="/admin/checkers/add" class="btn btn-primary">
              <i class="fas fa-plus me-2"></i>Adicionar Checker
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Checkers Summary Cards -->
    <div class="checkers-summary mb-4">
      <div class="row g-4">
        <div class="col-lg-3 col-md-6">
          <div class="summary-card total-checkers">
            <div class="card-icon">
              <i class="fas fa-tools"></i>
            </div>
            <div class="card-content">
              <h3 class="card-value"><%= checkers.length %></h3>
              <p class="card-label">Total de Checkers</p>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="summary-card active-checkers">
            <div class="card-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="card-content">
              <h3 class="card-value"><%= checkers.filter(c => c.status === 'active').length %></h3>
              <p class="card-label">Checkers Ativos</p>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="summary-card maintenance-checkers">
            <div class="card-icon">
              <i class="fas fa-wrench"></i>
            </div>
            <div class="card-content">
              <h3 class="card-value"><%= checkers.filter(c => c.status === 'maintenance').length %></h3>
              <p class="card-label">Em Manutenção</p>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="summary-card inactive-checkers">
            <div class="card-icon">
              <i class="fas fa-times-circle"></i>
            </div>
            <div class="card-content">
              <h3 class="card-value"><%= checkers.filter(c => c.status === 'inactive').length %></h3>
              <p class="card-label">Desativados</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Checkers Table -->
    <div class="row">
      <div class="col-12">
        <div class="card modern-card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fas fa-list me-2"></i>
              Lista de Checkers
            </h6>
          </div>
          <div class="card-body">
            <% if (checkers && checkers.length > 0) { %>
              <div class="table-responsive">
                <table class="table modern-table" id="checkersTable">
                  <thead>
                    <tr>
                      <th>
                        <i class="fas fa-hashtag me-2"></i>
                        ID
                      </th>
                      <th>
                        <i class="fas fa-code me-2"></i>
                        Nome/Título
                      </th>
                      <th>
                        <i class="fas fa-folder me-2"></i>
                        Categoria
                      </th>
                      <th>
                        <i class="fas fa-dollar-sign me-2"></i>
                        Preço
                      </th>
                      <th>
                        <i class="fas fa-toggle-on me-2"></i>
                        Status
                      </th>
                      <th>
                        <i class="fas fa-credit-card me-2"></i>
                        Cobrança
                      </th>
                      <th>
                        <i class="fas fa-user-tag me-2"></i>
                        Função
                      </th>
                      <th class="text-center">
                        <i class="fas fa-code me-2"></i>
                        Code Review
                      </th>
                      <th class="text-center">
                        <i class="fas fa-cogs me-2"></i>
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <% checkers.forEach(checker => { %>
                      <tr class="table-row">
                        <td>
                          <span class="id-badge">#<%= checker.id %></span>
                        </td>
                        <td>
                          <div class="checker-info">
                            <div class="checker-name"><%= checker.title %></div>
                            <div class="checker-code text-muted"><%= checker.name %></div>
                          </div>
                        </td>
                        <td>
                          <span class="category-badge">
                            <i class="<%= checker.category ? checker.category.icon : 'fas fa-folder' %> me-1"></i>
                            <%= checker.category ? checker.category.name : 'N/A' %>
                          </span>
                        </td>
                        <td>
                          <div class="price-info">
                            <span class="price-value">R$ <%= checker.price.toFixed(2) %></span>
                            <small class="text-muted d-block">créditos</small>
                          </div>
                        </td>
                        <td>
                          <% if (checker.status === 'active') { %>
                            <span class="status-badge status-active">
                              <i class="fas fa-check-circle me-1"></i>
                              Ativo
                            </span>
                          <% } else if (checker.status === 'maintenance') { %>
                            <span class="status-badge status-maintenance">
                              <i class="fas fa-wrench me-1"></i>
                              Manutenção
                            </span>
                          <% } else { %>
                            <span class="status-badge status-inactive">
                              <i class="fas fa-times-circle me-1"></i>
                              Desativado
                            </span>
                          <% } %>
                        </td>
                        <td>
                          <span class="charge-type">
                            <% if (checker.charge_type === 'per_test') { %>
                              <i class="fas fa-vial me-1"></i>Por teste
                            <% } else { %>
                              <i class="fas fa-heart me-1"></i>Por live
                            <% } %>
                          </span>
                        </td>
                        <td>
                          <span class="role-badge">
                            <i class="fas fa-user-shield me-1"></i>
                            <%= checker.required_role %>
                          </span>
                        </td>
                        <td class="text-center">
                          <% if (checker.isCodeApproved) { %>
                            <span class="status-badge status-active">
                              <i class="fas fa-check-circle me-1"></i>
                              Aprovado
                            </span>
                          <% } else { %>
                            <span class="status-badge status-maintenance">
                              <i class="fas fa-clock me-1"></i>
                              Pendente
                            </span>
                          <% } %>
                        </td>
                        <td class="text-center">
                          <div class="action-buttons">
                            <a href="/admin/checkers/edit/<%= checker.id %>"
                               class="btn btn-sm btn-outline-primary"
                               title="Editar">
                              <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" action="/admin/checkers/delete/<%= checker.id %>?_method=DELETE"
                                  style="display: inline;"
                                  onsubmit="return confirm('Tem certeza que deseja excluir este checker?')">
                              <button type="submit"
                                      class="btn btn-sm btn-outline-danger"
                                      title="Excluir">
                                <i class="fas fa-trash"></i>
                              </button>
                            </form>
                          </div>
                        </td>
                      </tr>
                    <% }); %>
                  </tbody>
                </table>
              </div>
            <% } else { %>
              <div class="empty-state">
                <div class="empty-icon">
                  <i class="fas fa-check-circle"></i>
                </div>
                <h5 class="empty-title">Nenhum checker encontrado</h5>
                <p class="empty-description">Comece criando seu primeiro checker para o sistema.</p>
                <a href="/admin/checkers/add" class="btn btn-primary">
                  <i class="fas fa-plus me-2"></i>Criar Primeiro Checker
                </a>
              </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Custom Styles for Checkers Page -->
<style>
/* =========================================================
 * Modern Checkers Page Styles
 ========================================================= */
.checkers-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 1.6rem 0;
}

.page-header {
  margin-bottom: 1.6rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.4rem;
}

.page-subtitle {
  font-size: 0.9rem;
  margin-bottom: 0;
}

.page-actions .btn {
  border-radius: 20px;
  padding: 0.4rem 1.2rem;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

/* =========================================================
 * Summary Cards
 ========================================================= */
.summary-card {
  background: white;
  border-radius: 16px;
  padding: 1.6rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1.2rem;
  height: 100%;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15);
}

.summary-card .card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  flex-shrink: 0;
}

.total-checkers .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.active-checkers .card-icon {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.maintenance-checkers .card-icon {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.inactive-checkers .card-icon {
  background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.summary-card .card-value {
  font-size: 1.6rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.summary-card .card-label {
  font-size: 0.7rem;
  color: #6c757d;
  margin-bottom: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* =========================================================
 * Modern Card & Table
 ========================================================= */
.modern-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modern-card .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 1.2rem 1.6rem;
  color: white;
}

.modern-card .card-body {
  padding: 1.6rem;
}

.modern-table {
  margin-bottom: 0;
}

.modern-table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  padding: 1rem 0.8rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.4px;
}

.modern-table tbody .table-row {
  border: none;
  transition: all 0.3s ease;
}

.modern-table tbody .table-row:hover {
  background: #f8f9fa;
  transform: scale(1.005);
}

.modern-table tbody td {
  padding: 1rem 0.8rem;
  border: none;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
}

/* =========================================================
 * Table Content Styles
 ========================================================= */
.id-badge {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.checker-info .checker-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.8rem;
  margin-bottom: 0.125rem;
}

.checker-info .checker-code {
  font-size: 0.7rem;
  font-family: 'Courier New', monospace;
}

.category-badge {
  background: #f8f9fa;
  color: #495057;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 500;
  border: 1px solid #dee2e6;
}

.price-info .price-value {
  font-weight: 700;
  color: #28a745;
  font-size: 0.8rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.status-active {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-maintenance {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-inactive {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.charge-type {
  font-size: 0.75rem;
  color: #6c757d;
}

.role-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.action-buttons .btn {
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* =========================================================
 * Empty State
 ========================================================= */
.empty-state {
  text-align: center;
  padding: 3.2rem 1.6rem;
  color: #6c757d;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;
  color: white;
}

.empty-title {
  color: #495057;
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

.empty-description {
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

/* =========================================================
 * Responsive Design
 ========================================================= */
@media (max-width: 768px) {
  .page-title {
    font-size: 1.6rem;
  }

  .summary-card {
    padding: 1.2rem;
  }

  .summary-card .card-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .summary-card .card-value {
    font-size: 1.4rem;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.8rem 0.6rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }
}

@media (max-width: 576px) {
  .checkers-page {
    padding: 0.8rem 0;
  }

  .container-fluid {
    padding-left: 0.8rem;
    padding-right: 0.8rem;
  }

  .page-actions {
    margin-top: 0.8rem;
  }

  .modern-card .card-body {
    padding: 1.2rem;
  }

  .empty-state {
    padding: 2.4rem 0.8rem;
  }
}
</style>

<!-- DataTables Integration -->
<script>
$(document).ready(function() {
  if ($.fn.DataTable) {
    $('#checkersTable').DataTable({
      responsive: true,
      language: {
        url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json'
      },
      pageLength: 25,
      order: [[0, 'asc']],
      columnDefs: [
        { orderable: false, targets: [8] }
      ]
    });
  }
});
</script>
