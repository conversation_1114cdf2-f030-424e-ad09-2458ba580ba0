<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <div class="row align-items-center">
          <div class="col">
            <h4 class="card-title">
              <i class="fa fa-history text-primary"></i> 
              <b>Histórico de Revisões - Usuário #<%= userId %></b>
            </h4>
          </div>
          <div class="col-auto">
            <a href="/admin/code-reviews" class="btn btn-secondary">
              <i class="fa fa-arrow-left"></i> Voltar
            </a>
          </div>
        </div>
      </div>
      <div class="card-body">
        <!-- Estatísticas do Usuário -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card bg-primary text-white">
              <div class="card-body text-center">
                <h3 class="mb-0"><%= pagination.total %></h3>
                <small>Total de Revisões</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-success text-white">
              <div class="card-body text-center">
                <h3 class="mb-0"><%= reviews.filter(r => r.status === 'approved').length %></h3>
                <small>Aprovadas</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-danger text-white">
              <div class="card-body text-center">
                <h3 class="mb-0"><%= reviews.filter(r => r.status === 'rejected').length %></h3>
                <small>Rejeitadas</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-warning text-white">
              <div class="card-body text-center">
                <h3 class="mb-0"><%= reviews.filter(r => r.status === 'pending' || r.status === 'needs_revision').length %></h3>
                <small>Pendentes</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Filtros -->
        <div class="row mb-3">
          <div class="col-md-4">
            <select class="form-control" id="statusFilter">
              <option value="">Todos os Status</option>
              <option value="pending">Pendente</option>
              <option value="needs_revision">Necessita Revisão</option>
              <option value="approved">Aprovado</option>
              <option value="rejected">Rejeitado</option>
            </select>
          </div>
          <div class="col-md-4">
            <select class="form-control" id="typeFilter">
              <option value="">Todos os Tipos</option>
              <option value="module">Módulo</option>
              <option value="custom_code">Código Personalizado</option>
            </select>
          </div>
          <div class="col-md-4">
            <button class="btn btn-primary" onclick="refreshTable()">
              <i class="fa fa-refresh"></i> Atualizar
            </button>
          </div>
        </div>

        <!-- Tabela de Histórico -->
        <div class="table-responsive">
          <table class="table table-striped" id="historyTable">
            <thead>
              <tr>
                <th>ID</th>
                <th>Tipo</th>
                <th>Status</th>
                <th>Score Segurança</th>
                <th>Revisado por</th>
                <th>Data Submissão</th>
                <th>Data Revisão</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (reviews && reviews.length > 0) { %>
                <% reviews.forEach(review => { %>
                  <tr>
                    <td><%= review.id %></td>
                    <td>
                      <span class="badge badge-secondary">
                        <%= review.code_type === 'module' ? 'Módulo' : 'Código Personalizado' %>
                      </span>
                    </td>
                    <td>
                      <% 
                        let statusClass = 'secondary';
                        let statusText = review.status;
                        switch(review.status) {
                          case 'pending':
                            statusClass = 'warning';
                            statusText = 'Pendente';
                            break;
                          case 'approved':
                            statusClass = 'success';
                            statusText = 'Aprovado';
                            break;
                          case 'rejected':
                            statusClass = 'danger';
                            statusText = 'Rejeitado';
                            break;
                          case 'needs_revision':
                            statusClass = 'info';
                            statusText = 'Necessita Revisão';
                            break;
                        }
                      %>
                      <span class="badge badge-<%= statusClass %>"><%= statusText %></span>
                    </td>
                    <td>
                      <% if (review.security_score !== null) { %>
                        <% 
                          let scoreClass = 'danger';
                          if (review.security_score >= 70) scoreClass = 'success';
                          else if (review.security_score >= 50) scoreClass = 'warning';
                        %>
                        <span class="badge badge-<%= scoreClass %>"><%= review.security_score %>%</span>
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                    <td>
                      <% if (review.reviewer) { %>
                        <%= review.reviewer.usuario %>
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                    <td>
                      <%= new Date(review.submitted_at).toLocaleString('pt-BR') %>
                    </td>
                    <td>
                      <% if (review.reviewed_at) { %>
                        <%= new Date(review.reviewed_at).toLocaleString('pt-BR') %>
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                    <td>
                      <a href="/admin/code-reviews/<%= review.id %>" class="btn btn-sm btn-info">
                        <i class="fa fa-eye"></i> Ver
                      </a>
                      <% if (review.status === 'approved' && review.original_filename) { %>
                        <a href="/admin/code-reviews/<%= review.id %>/export" class="btn btn-sm btn-success">
                          <i class="fa fa-download"></i>
                        </a>
                      <% } %>
                    </td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="8" class="text-center">
                    <div class="empty-state py-4">
                      <i class="fa fa-history fa-3x text-muted mb-3"></i>
                      <h5 class="text-muted">Nenhuma revisão encontrada</h5>
                      <p class="text-muted">Este usuário ainda não possui histórico de revisões.</p>
                    </div>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>

        <!-- Paginação -->
        <% if (pagination && pagination.totalPages > 1) { %>
          <nav aria-label="Paginação">
            <ul class="pagination justify-content-center">
              <% if (pagination.page > 1) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= pagination.page - 1 %>">Anterior</a>
                </li>
              <% } %>
              
              <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                <li class="page-item <%= pagination.page === i ? 'active' : '' %>">
                  <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                </li>
              <% } %>
              
              <% if (pagination.page < pagination.totalPages) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= pagination.page + 1 %>">Próximo</a>
                </li>
              <% } %>
            </ul>
          </nav>
        <% } %>

        <!-- Gráfico de Estatísticas -->
        <% if (reviews && reviews.length > 0) { %>
        <div class="row mt-4">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title">Distribuição por Status</h6>
              </div>
              <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title">Evolução dos Scores de Segurança</h6>
              </div>
              <div class="card-body">
                <canvas id="scoresChart" height="200"></canvas>
              </div>
            </div>
          </div>
        </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<script>
// Filtros
document.getElementById('statusFilter').addEventListener('change', function() {
  filterTable();
});

document.getElementById('typeFilter').addEventListener('change', function() {
  filterTable();
});

function filterTable() {
  const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
  const typeFilter = document.getElementById('typeFilter').value.toLowerCase();
  const table = document.getElementById('historyTable');
  const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    const cells = row.getElementsByTagName('td');
    
    if (cells.length === 1) continue; // Skip empty state row
    
    const status = cells[2].textContent.toLowerCase();
    const type = cells[1].textContent.toLowerCase();
    
    const statusMatch = !statusFilter || status.includes(statusFilter);
    const typeMatch = !typeFilter || type.includes(typeFilter);
    
    row.style.display = statusMatch && typeMatch ? '' : 'none';
  }
}

function refreshTable() {
  location.reload();
}

// Gráficos (se houver dados)
<% if (reviews && reviews.length > 0) { %>
document.addEventListener('DOMContentLoaded', function() {
  // Gráfico de Status
  const statusData = {
    approved: <%= reviews.filter(r => r.status === 'approved').length %>,
    rejected: <%= reviews.filter(r => r.status === 'rejected').length %>,
    pending: <%= reviews.filter(r => r.status === 'pending').length %>,
    needs_revision: <%= reviews.filter(r => r.status === 'needs_revision').length %>
  };

  const statusCtx = document.getElementById('statusChart').getContext('2d');
  new Chart(statusCtx, {
    type: 'doughnut',
    data: {
      labels: ['Aprovado', 'Rejeitado', 'Pendente', 'Necessita Revisão'],
      datasets: [{
        data: [statusData.approved, statusData.rejected, statusData.pending, statusData.needs_revision],
        backgroundColor: ['#28a745', '#dc3545', '#ffc107', '#17a2b8']
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      legend: {
        position: 'bottom'
      }
    }
  });

  // Gráfico de Scores
  const scoresData = [
    <% reviews.filter(r => r.security_score !== null).forEach((review, index) => { %>
      {
        x: '<%= new Date(review.submitted_at).toLocaleDateString("pt-BR") %>',
        y: <%= review.security_score %>
      }<%= index < reviews.filter(r => r.security_score !== null).length - 1 ? ',' : '' %>
    <% }); %>
  ];

  const scoresCtx = document.getElementById('scoresChart').getContext('2d');
  new Chart(scoresCtx, {
    type: 'line',
    data: {
      datasets: [{
        label: 'Score de Segurança',
        data: scoresData,
        borderColor: '#007bff',
        backgroundColor: 'rgba(0, 123, 255, 0.1)',
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          max: 100
        }
      }
    }
  });
});
<% } %>
</script>

<style>
.empty-state {
  padding: 2rem;
}

.badge {
  font-size: 0.75em;
}

.table td {
  vertical-align: middle;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.card.bg-primary,
.card.bg-success,
.card.bg-danger,
.card.bg-warning {
  border: none;
}

.card.bg-primary .card-body,
.card.bg-success .card-body,
.card.bg-danger .card-body,
.card.bg-warning .card-body {
  padding: 1rem;
}

.card.bg-primary .card-body h3,
.card.bg-success .card-body h3,
.card.bg-danger .card-body h3,
.card.bg-warning .card-body h3 {
  font-weight: bold;
}
</style>
