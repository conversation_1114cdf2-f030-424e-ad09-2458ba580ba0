<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <div class="row align-items-center">
          <div class="col">
            <h4 class="card-title">
              <i class="fa fa-code text-primary"></i>
              <b><PERSON><PERSON>ão #<%= review.id %></b>
            </h4>
          </div>
          <div class="col-auto">
            <a href="/admin/code-reviews" class="btn btn-secondary">
              <i class="fa fa-arrow-left"></i> Voltar
            </a>
          </div>
        </div>
      </div>
      <div class="card-body">
        <!-- Informações Gerais -->
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="card bg-light">
              <div class="card-body">
                <h6 class="card-title">Informações Gerais</h6>
                <table class="table table-sm table-borderless">
                  <tr>
                    <td><strong>ID:</strong></td>
                    <td>#<%= review.id %></td>
                  </tr>
                  <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                      <%
                        let statusClass = 'secondary';
                        let statusText = review.status;
                        switch(review.status) {
                          case 'pending':
                            statusClass = 'warning';
                            statusText = 'Pendente';
                            break;
                          case 'approved':
                            statusClass = 'success';
                            statusText = 'Aprovado';
                            break;
                          case 'rejected':
                            statusClass = 'danger';
                            statusText = 'Rejeitado';
                            break;
                          case 'needs_revision':
                            statusClass = 'info';
                            statusText = 'Necessita Revisão';
                            break;
                        }
                      %>
                      <span class="badge badge-<%= statusClass %>"><%= statusText %></span>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Tipo:</strong></td>
                    <td><%= review.code_type === 'module' ? 'Módulo' : 'Código Personalizado' %></td>
                  </tr>
                  <tr>
                    <td><strong>Submetido por:</strong></td>
                    <td>
                      <% if (review.submitter) { %>
                        <%= review.submitter.usuario %> (<%= review.submitter.role %>)
                      <% } else { %>
                        N/A
                      <% } %>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Data de Submissão:</strong></td>
                    <td><%= new Date(review.submitted_at).toLocaleString('pt-BR') %></td>
                  </tr>
                  <% if (review.reviewed_at) { %>
                  <tr>
                    <td><strong>Data de Revisão:</strong></td>
                    <td><%= new Date(review.reviewed_at).toLocaleString('pt-BR') %></td>
                  </tr>
                  <% } %>
                  <% if (review.reviewer) { %>
                  <tr>
                    <td><strong>Revisado por:</strong></td>
                    <td><%= review.reviewer.usuario %></td>
                  </tr>
                  <% } %>
                </table>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card bg-light">
              <div class="card-body">
                <h6 class="card-title">Análise de Segurança</h6>
                <table class="table table-sm table-borderless">
                  <tr>
                    <td><strong>Score de Segurança:</strong></td>
                    <td>
                      <% if (review.security_score !== null) { %>
                        <%
                          let scoreClass = 'danger';
                          if (review.security_score >= 70) scoreClass = 'success';
                          else if (review.security_score >= 50) scoreClass = 'warning';
                        %>
                        <span class="badge badge-<%= scoreClass %> badge-lg"><%= review.security_score %>%</span>
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Sandbox:</strong></td>
                    <td>
                      <% if (review.execution_sandbox) { %>
                        <span class="badge badge-info"><%= review.execution_sandbox %></span>
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Tempo Máx. Execução:</strong></td>
                    <td>
                      <% if (review.max_execution_time) { %>
                        <%= review.max_execution_time %>ms
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Arquivo Original:</strong></td>
                    <td>
                      <% if (review.original_filename) { %>
                        <code><%= review.original_filename %></code>
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Problemas de Segurança -->
        <% if (review.security_issues && Array.isArray(review.security_issues) && review.security_issues.length > 0) { %>
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="card border-warning">
              <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                  <i class="fa fa-exclamation-triangle"></i>
                  Problemas de Segurança Detectados (<%= review.security_issues.length %>)
                </h6>
              </div>
              <div class="card-body">
                <div class="list-group">
                  <% review.security_issues.forEach((issue, index) => { %>
                    <% if (issue && typeof issue === 'object') { %>
                    <div class="list-group-item">
                      <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">
                          <%
                            const severity = (issue.severity && typeof issue.severity === 'string') ? issue.severity : 'low';
                            const badgeClass = severity === 'critical' ? 'danger' :
                                             severity === 'high' ? 'danger' :
                                             severity === 'medium' ? 'warning' : 'info';
                          %>
                          <span class="badge badge-<%= badgeClass %>">
                            <%= severity.toUpperCase() %>
                          </span>
                          <%= (issue.type && typeof issue.type === 'string') ? issue.type : 'Problema de Segurança' %>
                        </h6>
                        <% if (issue.line && typeof issue.line === 'number') { %>
                          <small>Linha <%= issue.line %></small>
                        <% } %>
                      </div>
                      <p class="mb-1">
                        <%= (issue.description && typeof issue.description === 'string') ? issue.description :
                            (issue.message && typeof issue.message === 'string') ? issue.message :
                            'Descrição não disponível' %>
                      </p>
                      <% if (issue.suggestion && typeof issue.suggestion === 'string') { %>
                        <small class="text-muted">
                          <strong>Sugestão:</strong> <%= issue.suggestion %>
                        </small>
                      <% } %>
                      <% if (issue.pattern && typeof issue.pattern === 'string') { %>
                        <small class="text-muted d-block">
                          <strong>Padrão:</strong> <code><%= issue.pattern %></code>
                        </small>
                      <% } %>
                    </div>
                    <% } else { %>
                    <div class="list-group-item">
                      <div class="alert alert-warning mb-0">
                        <strong>Issue #<%= index + 1 %>:</strong> Dados de segurança inválidos
                      </div>
                    </div>
                    <% } %>
                  <% }); %>
                </div>
              </div>
            </div>
          </div>
        </div>
        <% } %>

        <!-- Notas de Revisão -->
        <% if (review.review_notes) { %>
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="card border-info">
              <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                  <i class="fa fa-comment"></i>
                  Notas da Revisão
                </h6>
              </div>
              <div class="card-body">
                <p class="mb-0"><%= review.review_notes %></p>
              </div>
            </div>
          </div>
        </div>
        <% } %>

        <!-- Código -->
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="card-title mb-0">
                    <i class="fa fa-file-code"></i>
                    Código Submetido
                  </h6>
                  <div>
                    <button class="btn btn-sm btn-secondary" onclick="copyCode()">
                      <i class="fa fa-copy"></i> Copiar
                    </button>
                    <% if (review.status === 'approved') { %>
                      <a href="/admin/code-reviews/<%= review.id %>/export" class="btn btn-sm btn-success">
                        <i class="fa fa-download"></i> Exportar
                      </a>
                    <% } %>
                  </div>
                </div>
              </div>
              <div class="card-body p-0">
                <pre id="codeContent" class="code-viewer"><code><%= review.code_content %></code></pre>
              </div>
            </div>
          </div>
        </div>

        <!-- Ações -->
        <% if (review.status === 'pending' || review.status === 'needs_revision') { %>
        <div class="row">
          <div class="col-md-12">
            <div class="card bg-light">
              <div class="card-body">
                <h6 class="card-title">Ações de Revisão</h6>
                <div class="btn-group" role="group">
                  <button class="btn btn-success" onclick="approveReview()">
                    <i class="fa fa-check"></i> Aprovar
                  </button>
                  <button class="btn btn-danger" onclick="rejectReview()">
                    <i class="fa fa-times"></i> Rejeitar
                  </button>
                  <button class="btn btn-warning" onclick="requestRevision()">
                    <i class="fa fa-edit"></i> Solicitar Revisão
                  </button>
                  <button class="btn btn-info" onclick="reanalyzeCode()">
                    <i class="fa fa-refresh"></i> Reanalizar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Aprovação -->
<div class="modal fade" id="approveModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Aprovar Revisão</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="approveForm">
          <div class="form-group">
            <label for="approveNotes">Notas da Aprovação (opcional)</label>
            <textarea class="form-control" id="approveNotes" rows="3" placeholder="Comentários sobre a aprovação..."></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-success" onclick="confirmApprove()">
          <i class="fa fa-check"></i> Aprovar
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Rejeição -->
<div class="modal fade" id="rejectModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Rejeitar Revisão</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="rejectForm">
          <div class="form-group">
            <label for="rejectNotes">Motivo da Rejeição *</label>
            <textarea class="form-control" id="rejectNotes" rows="3" placeholder="Explique o motivo da rejeição..." required></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-danger" onclick="confirmReject()">
          <i class="fa fa-times"></i> Rejeitar
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Solicitar Revisão -->
<div class="modal fade" id="revisionModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Solicitar Revisão</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="revisionForm">
          <div class="form-group">
            <label for="revisionNotes">Comentários para Revisão *</label>
            <textarea class="form-control" id="revisionNotes" rows="3" placeholder="Explique o que precisa ser revisado..." required></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-warning" onclick="confirmRevision()">
          <i class="fa fa-edit"></i> Solicitar Revisão
        </button>
      </div>
    </div>
  </div>
</div>

<script>
const reviewId = <%= review.id %>;

function copyCode() {
  const codeElement = document.getElementById('codeContent');
  const textArea = document.createElement('textarea');
  textArea.value = codeElement.textContent;
  document.body.appendChild(textArea);
  textArea.select();
  document.execCommand('copy');
  document.body.removeChild(textArea);

  showAlert('Código copiado para a área de transferência!', 'success');
}

function approveReview() {
  $('#approveModal').modal('show');
}

function rejectReview() {
  $('#rejectModal').modal('show');
}

function requestRevision() {
  $('#revisionModal').modal('show');
}

async function confirmApprove() {
  const notes = document.getElementById('approveNotes').value;

  try {
    const response = await fetch(`/admin/code-reviews/${reviewId}/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notes })
    });

    const result = await response.json();

    if (result.success) {
      $('#approveModal').modal('hide');
      showAlert('Revisão aprovada com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      showAlert(result.message || 'Erro ao aprovar revisão', 'danger');
    }
  } catch (err) {
    console.error('Erro ao aprovar:', err);
    showAlert('Erro ao aprovar revisão', 'danger');
  }
}

async function confirmReject() {
  const notes = document.getElementById('rejectNotes').value.trim();

  if (!notes) {
    showAlert('Por favor, informe o motivo da rejeição', 'warning');
    return;
  }

  try {
    const response = await fetch(`/admin/code-reviews/${reviewId}/reject`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notes })
    });

    const result = await response.json();

    if (result.success) {
      $('#rejectModal').modal('hide');
      showAlert('Revisão rejeitada com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      showAlert(result.message || 'Erro ao rejeitar revisão', 'danger');
    }
  } catch (err) {
    console.error('Erro ao rejeitar:', err);
    showAlert('Erro ao rejeitar revisão', 'danger');
  }
}

async function confirmRevision() {
  const notes = document.getElementById('revisionNotes').value.trim();

  if (!notes) {
    showAlert('Por favor, informe os comentários para revisão', 'warning');
    return;
  }

  try {
    const response = await fetch(`/admin/code-reviews/${reviewId}/revision`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notes })
    });

    const result = await response.json();

    if (result.success) {
      $('#revisionModal').modal('hide');
      showAlert('Revisão solicitada com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      showAlert(result.message || 'Erro ao solicitar revisão', 'danger');
    }
  } catch (err) {
    console.error('Erro ao solicitar revisão:', err);
    showAlert('Erro ao solicitar revisão', 'danger');
  }
}

async function reanalyzeCode() {
  if (!confirm('Tem certeza que deseja reanalizar este código?')) return;

  try {
    const response = await fetch(`/admin/code-reviews/${reviewId}/reanalyze`, {
      method: 'POST'
    });

    const result = await response.json();

    if (result.success) {
      showAlert('Código reanalisado com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      showAlert(result.message || 'Erro ao reanalizar código', 'danger');
    }
  } catch (err) {
    console.error('Erro ao reanalizar:', err);
    showAlert('Erro ao reanalizar código', 'danger');
  }
}

function showAlert(message, type) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert">
      <span>&times;</span>
    </button>
  `;

  document.querySelector('.card-body').insertBefore(alertDiv, document.querySelector('.row'));

  setTimeout(() => {
    alertDiv.remove();
  }, 5000);
}
</script>

<style>
.code-viewer {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.25rem;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  max-height: 500px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.badge-lg {
  font-size: 1rem;
  padding: 0.5rem 0.75rem;
}

.list-group-item {
  border-left: 4px solid transparent;
}

.list-group-item:has(.badge-danger) {
  border-left-color: #dc3545;
}

.list-group-item:has(.badge-warning) {
  border-left-color: #ffc107;
}

.list-group-item:has(.badge-info) {
  border-left-color: #17a2b8;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.modal-content {
  border-radius: 10px;
}
</style>
