<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center">
          <i class="fa fa-code text-primary"></i> 
          <b>REVISÕES DE CÓDIGO</b>
        </h4>
        <div class="text-center mt-2">
          <span class="badge badge-warning">Pendentes: <%= stats.pending %></span>
          <span class="badge badge-info">Necessitam Revisão: <%= stats.needsRevision %></span>
          <% if (stats.approved) { %>
            <span class="badge badge-success">Aprovados: <%= stats.approved %></span>
          <% } %>
          <% if (stats.rejected) { %>
            <span class="badge badge-danger">Rejeitados: <%= stats.rejected %></span>
          <% } %>
          <% if (stats.checkersWithoutApproval > 0) { %>
            <span class="badge badge-dark">Checkers sem Aprovação: <%= stats.checkersWithoutApproval %></span>
          <% } %>
        </div>


      </div>
      <div class="card-body">
        <!-- Filtros e Ações -->
        <div class="row mb-3">
          <div class="col-md-3">
            <select class="form-control" id="statusFilter">
              <option value="">Todos os Status</option>
              <option value="pendente">Pendente</option>
              <option value="necessita revisão">Necessita Revisão</option>
              <option value="aprovado">Aprovado</option>
              <option value="rejeitado">Rejeitado</option>
            </select>
          </div>
          <div class="col-md-4">
            <input type="text" class="form-control" id="searchInput" placeholder="Buscar por usuário...">
          </div>
          <div class="col-md-5 text-right">
            <button class="btn btn-primary btn-sm" onclick="refreshTable()">
              <i class="fa fa-refresh"></i> Atualizar
            </button>
            <% if (stats.checkersWithoutApproval > 0) { %>
              <button class="btn btn-warning btn-sm ml-2" onclick="createMissingReviews()">
                <i class="fa fa-plus"></i> Criar Revisões (<%= stats.checkersWithoutApproval %>)
              </button>
            <% } %>
          </div>
        </div>

        <!-- Tabela de Revisões -->
        <div class="table-responsive">
          <table class="table table-striped" id="reviewsTable">
            <thead>
              <tr>
                <th>ID</th>
                <th>Usuário</th>
                <th>Tipo</th>
                <th>Status</th>
                <th>Score Segurança</th>
                <th>Submetido em</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (reviews && reviews.length > 0) { %>
                <% reviews.forEach(review => { %>
                  <tr>
                    <td><%= review.id %></td>
                    <td>
                      <% if (review.submitter) { %>
                        <%= review.submitter.usuario %>
                        <small class="text-muted d-block"><%= review.submitter.role %></small>
                      <% } else { %>
                        N/A
                      <% } %>
                    </td>
                    <td>
                      <span class="badge badge-secondary">
                        <%= review.code_type === 'module' ? 'Módulo' : 'Código Personalizado' %>
                      </span>
                    </td>
                    <td>
                      <% 
                        let statusClass = 'secondary';
                        let statusText = review.status;
                        switch(review.status) {
                          case 'pending':
                            statusClass = 'warning';
                            statusText = 'Pendente';
                            break;
                          case 'approved':
                            statusClass = 'success';
                            statusText = 'Aprovado';
                            break;
                          case 'rejected':
                            statusClass = 'danger';
                            statusText = 'Rejeitado';
                            break;
                          case 'needs_revision':
                            statusClass = 'info';
                            statusText = 'Necessita Revisão';
                            break;
                        }
                      %>
                      <span class="badge badge-<%= statusClass %>"><%= statusText %></span>
                    </td>
                    <td>
                      <% if (review.security_score !== null) { %>
                        <% 
                          let scoreClass = 'danger';
                          if (review.security_score >= 70) scoreClass = 'success';
                          else if (review.security_score >= 50) scoreClass = 'warning';
                        %>
                        <span class="badge badge-<%= scoreClass %>"><%= review.security_score %>%</span>
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                    <td>
                      <%= new Date(review.submitted_at).toLocaleString('pt-BR') %>
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <a href="/admin/code-reviews/<%= review.id %>" class="btn btn-sm btn-info" title="Ver detalhes">
                          <i class="fa fa-eye"></i>
                        </a>
                        <% if (review.status === 'pending' || review.status === 'needs_revision') { %>
                          <button class="btn btn-sm btn-success" onclick="approveReview(<%= review.id %>)" title="Aprovar código">
                            <i class="fa fa-check"></i>
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="rejectReview(<%= review.id %>)" title="Rejeitar código">
                            <i class="fa fa-times"></i>
                          </button>
                          <button class="btn btn-sm btn-warning" onclick="requestRevision(<%= review.id %>)" title="Solicitar revisão">
                            <i class="fa fa-edit"></i>
                          </button>
                        <% } %>
                      </div>
                    </td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="7" class="text-center">
                    <div class="empty-state py-4">
                      <i class="fa fa-code fa-3x text-muted mb-3"></i>
                      <h5 class="text-muted">Nenhuma revisão encontrada</h5>
                      <p class="text-muted">Não há revisões de código pendentes no momento.</p>
                    </div>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>

        <!-- Paginação -->
        <% if (pagination && pagination.totalPages > 1) { %>
          <nav aria-label="Paginação">
            <ul class="pagination justify-content-center">
              <% if (pagination.page > 1) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= pagination.page - 1 %>">Anterior</a>
                </li>
              <% } %>
              
              <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                <li class="page-item <%= pagination.page === i ? 'active' : '' %>">
                  <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                </li>
              <% } %>
              
              <% if (pagination.page < pagination.totalPages) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= pagination.page + 1 %>">Próximo</a>
                </li>
              <% } %>
            </ul>
          </nav>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Aprovação -->
<div class="modal fade" id="approveModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Aprovar Revisão</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="approveForm">
          <div class="form-group">
            <label for="approveNotes">Notas da Aprovação (opcional)</label>
            <textarea class="form-control" id="approveNotes" rows="3" placeholder="Comentários sobre a aprovação..."></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-success" onclick="confirmApprove()">
          <i class="fa fa-check"></i> Aprovar
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Rejeição -->
<div class="modal fade" id="rejectModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Rejeitar Revisão</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="rejectForm">
          <div class="form-group">
            <label for="rejectNotes">Motivo da Rejeição *</label>
            <textarea class="form-control" id="rejectNotes" rows="3" placeholder="Explique o motivo da rejeição..." required></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-danger" onclick="confirmReject()">
          <i class="fa fa-times"></i> Rejeitar
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Solicitar Revisão -->
<div class="modal fade" id="revisionModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Solicitar Revisão</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="revisionForm">
          <div class="form-group">
            <label for="revisionNotes">Comentários da Revisão *</label>
            <textarea class="form-control" id="revisionNotes" rows="3" placeholder="Explique o que precisa ser revisado..." required></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-warning" onclick="confirmRevision()">
          <i class="fa fa-edit"></i> Solicitar Revisão
        </button>
      </div>
    </div>
  </div>
</div>

<script>
let currentReviewId = null;

// Filtros
document.getElementById('statusFilter').addEventListener('change', function() {
  filterTable();
});

document.getElementById('searchInput').addEventListener('keyup', function() {
  filterTable();
});

function filterTable() {
  const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
  const searchFilter = document.getElementById('searchInput').value.toLowerCase();
  const table = document.getElementById('reviewsTable');
  const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    const cells = row.getElementsByTagName('td');
    
    if (cells.length === 1) continue; // Skip empty state row
    
    const status = cells[3].textContent.toLowerCase();
    const user = cells[1].textContent.toLowerCase();
    const type = cells[2].textContent.toLowerCase();
    
    const statusMatch = !statusFilter || status.includes(statusFilter);
    const searchMatch = !searchFilter || user.includes(searchFilter) || type.includes(searchFilter);
    
    row.style.display = statusMatch && searchMatch ? '' : 'none';
  }
}

function refreshTable() {
  location.reload();
}

function approveReview(reviewId) {
  currentReviewId = reviewId;
  $('#approveModal').modal('show');
}

function createMissingReviews() {
  if (!confirm('Isso criará revisões de código para todos os checkers sem aprovação. Continuar?')) {
    return;
  }

  const btn = event.target;
  const originalText = btn.innerHTML;
  btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Criando...';
  btn.disabled = true;

  fetch('/admin/code-reviews/create-missing', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(`Sucesso! ${data.processedCheckers} checkers processados.`);
      location.reload();
    } else {
      alert('Erro: ' + (data.message || 'Erro desconhecido'));
    }
  })
  .catch(error => {
    console.error('Erro:', error);
    alert('Erro ao criar revisões: ' + error.message);
  })
  .finally(() => {
    btn.innerHTML = originalText;
    btn.disabled = false;
  });
}

function rejectReview(reviewId) {
  currentReviewId = reviewId;
  $('#rejectModal').modal('show');
}

function requestRevision(reviewId) {
  currentReviewId = reviewId;
  $('#revisionModal').modal('show');
}

async function confirmApprove() {
  if (!currentReviewId) return;

  const notes = document.getElementById('approveNotes').value;
  const btn = document.querySelector('#approveModal .btn-success');
  const originalText = btn.innerHTML;

  // Mostrar loading
  btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Aprovando...';
  btn.disabled = true;

  try {
    const response = await fetch(`/admin/code-reviews/${currentReviewId}/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notes })
    });

    if (response.ok) {
      $('#approveModal').modal('hide');
      showAlert('Revisão aprovada com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      const errorText = await response.text();
      showAlert('Erro ao aprovar revisão: ' + errorText, 'danger');
    }
  } catch (err) {
    console.error('Erro ao aprovar:', err);
    showAlert('Erro ao aprovar revisão: ' + err.message, 'danger');
  } finally {
    // Restaurar botão
    btn.innerHTML = originalText;
    btn.disabled = false;
  }
}

async function confirmReject() {
  if (!currentReviewId) return;

  const notes = document.getElementById('rejectNotes').value.trim();

  if (!notes) {
    showAlert('Por favor, informe o motivo da rejeição', 'warning');
    return;
  }

  const btn = document.querySelector('#rejectModal .btn-danger');
  const originalText = btn.innerHTML;

  // Mostrar loading
  btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Rejeitando...';
  btn.disabled = true;

  try {
    const response = await fetch(`/admin/code-reviews/${currentReviewId}/reject`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ reason: notes }) // Corrigido: usar 'reason' em vez de 'notes'
    });

    if (response.ok) {
      $('#rejectModal').modal('hide');
      showAlert('Revisão rejeitada com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      const errorText = await response.text();
      showAlert('Erro ao rejeitar revisão: ' + errorText, 'danger');
    }
  } catch (err) {
    console.error('Erro ao rejeitar:', err);
    showAlert('Erro ao rejeitar revisão: ' + err.message, 'danger');
  } finally {
    // Restaurar botão
    btn.innerHTML = originalText;
    btn.disabled = false;
  }
}

async function confirmRevision() {
  if (!currentReviewId) return;

  const notes = document.getElementById('revisionNotes').value.trim();

  if (!notes) {
    showAlert('Por favor, informe os comentários da revisão', 'warning');
    return;
  }

  const btn = document.querySelector('#revisionModal .btn-warning');
  const originalText = btn.innerHTML;

  // Mostrar loading
  btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Solicitando...';
  btn.disabled = true;

  try {
    const response = await fetch(`/admin/code-reviews/${currentReviewId}/request-revision`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notes: notes })
    });

    if (response.ok) {
      $('#revisionModal').modal('hide');
      showAlert('Revisão solicitada com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      const errorText = await response.text();
      showAlert('Erro ao solicitar revisão: ' + errorText, 'danger');
    }
  } catch (err) {
    console.error('Erro ao solicitar revisão:', err);
    showAlert('Erro ao solicitar revisão: ' + err.message, 'danger');
  } finally {
    // Restaurar botão
    btn.innerHTML = originalText;
    btn.disabled = false;
  }
}

function showAlert(message, type) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert">
      <span>&times;</span>
    </button>
  `;
  
  document.querySelector('.card-body').insertBefore(alertDiv, document.querySelector('.row'));
  
  setTimeout(() => {
    alertDiv.remove();
  }, 5000);
}
</script>

<style>
.empty-state {
  padding: 2rem;
}

.badge {
  font-size: 0.75em;
  margin-right: 5px;
}

.btn-group .btn {
  margin-right: 2px;
  border-radius: 4px;
}

.btn-group .btn:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group .btn:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.table td {
  vertical-align: middle;
  padding: 0.75rem 0.5rem;
}

.table th {
  border-top: none;
  font-weight: 600;
  background-color: #f8f9fa;
}

.modal-content {
  border-radius: 10px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 10px 10px 0 0;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
  border-radius: 10px 10px 0 0;
}

.alert {
  border-radius: 8px;
  border: none;
}

.form-control {
  border-radius: 6px;
}

.btn {
  border-radius: 6px;
  font-weight: 500;
}

.table-responsive {
  border-radius: 8px;
  overflow: hidden;
}

.pagination .page-link {
  border-radius: 6px;
  margin: 0 2px;
  border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

/* Responsividade */
@media (max-width: 768px) {
  .btn-group {
    display: flex;
    flex-direction: column;
  }

  .btn-group .btn {
    margin-bottom: 2px;
    border-radius: 6px !important;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .badge {
    font-size: 0.7em;
  }
}
</style>
