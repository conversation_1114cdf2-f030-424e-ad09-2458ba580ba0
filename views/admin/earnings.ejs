<div class="container-fluid mt-4">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title"><i class="fa fa-money-bill-wave text-primary mr-2"></i> <b>TODOS OS GANHOS</b></h4>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Relatório de Ganhos</h1>
          </div>

      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <h5 class="card-title">Total de Ganhos</h5>
              <h2 class="card-text">R$ <%= totalEarnings.toFixed(2) %></h2>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <h5 class="card-title">Ganhos de Programadores</h5>
              <h2 class="card-text">R$ <%= totalProgrammerEarnings.toFixed(2) %></h2>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-warning text-dark">
            <div class="card-body">
              <h5 class="card-title">Ganhos de Afiliados</h5>
              <h2 class="card-text">R$ <%= totalAffiliateEarnings.toFixed(2) %></h2>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <h5 class="card-title">Ganhos do Site</h5>
              <h2 class="card-text">R$ <%= totalSiteEarnings.toFixed(2) %></h2>
            </div>
          </div>
        </div>
      </div>

      <ul class="nav nav-tabs" id="earningsTab" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="all-tab" data-toggle="tab" href="#all" role="tab" aria-controls="all" aria-selected="true">Todos</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="programmer-tab" data-toggle="tab" href="#programmer" role="tab" aria-controls="programmer" aria-selected="false">Programadores</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="affiliate-tab" data-toggle="tab" href="#affiliate" role="tab" aria-controls="affiliate" aria-selected="false">Afiliados</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="site-tab" data-toggle="tab" href="#site" role="tab" aria-controls="site" aria-selected="false">Site</a>
        </li>
      </ul>
      <div class="tab-content" id="earningsTabContent">
        <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
          <div class="table-responsive mt-3">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Data</th>
                  <th>Usuário</th>
                  <th>Tipo</th>
                  <th>Checker</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
                <% if (earnings.length === 0) { %>
                  <tr>
                    <td colspan="5" class="text-center">Nenhum ganho registrado</td>
                  </tr>
                <% } else { %>
                  <% earnings.forEach(earning => { %>
                    <tr>
                      <td><%= new Date(earning.created_at).toLocaleString() %></td>
                      <td><%= earning.user ? earning.user.usuario : 'N/A' %></td>
                      <td>
                        <% if (earning.source_type === 'programmer') { %>
                          <span class="badge badge-info">Programador</span>
                        <% } else if (earning.source_type === 'affiliate') { %>
                          <span class="badge badge-warning">Afiliado</span>
                        <% } else { %>
                          <span class="badge badge-primary">Site</span>
                        <% } %>
                      </td>
                      <td><%= earning.checker ? earning.checker.title : 'N/A' %></td>
                      <td>R$ <%= earning.amount.toFixed(2) %></td>
                    </tr>
                  <% }); %>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
        <div class="tab-pane fade" id="programmer" role="tabpanel" aria-labelledby="programmer-tab">
          <div class="table-responsive mt-3">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Data</th>
                  <th>Programador</th>
                  <th>Checker</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
                <% if (programmerEarnings.length === 0) { %>
                  <tr>
                    <td colspan="4" class="text-center">Nenhum ganho de programador registrado</td>
                  </tr>
                <% } else { %>
                  <% programmerEarnings.forEach(earning => { %>
                    <tr>
                      <td><%= new Date(earning.created_at).toLocaleString() %></td>
                      <td><%= earning.user ? earning.user.usuario : 'N/A' %></td>
                      <td><%= earning.checker ? earning.checker.title : 'N/A' %></td>
                      <td>R$ <%= earning.amount.toFixed(2) %></td>
                    </tr>
                  <% }); %>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
        <div class="tab-pane fade" id="affiliate" role="tabpanel" aria-labelledby="affiliate-tab">
          <div class="table-responsive mt-3">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Data</th>
                  <th>Afiliado</th>
                  <th>Checker</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
                <% if (affiliateEarnings.length === 0) { %>
                  <tr>
                    <td colspan="4" class="text-center">Nenhum ganho de afiliado registrado</td>
                  </tr>
                <% } else { %>
                  <% affiliateEarnings.forEach(earning => { %>
                    <tr>
                      <td><%= new Date(earning.created_at).toLocaleString() %></td>
                      <td><%= earning.user ? earning.user.usuario : 'N/A' %></td>
                      <td><%= earning.checker ? earning.checker.title : 'N/A' %></td>
                      <td>R$ <%= earning.amount.toFixed(2) %></td>
                    </tr>
                  <% }); %>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
        <div class="tab-pane fade" id="site" role="tabpanel" aria-labelledby="site-tab">
          <div class="table-responsive mt-3">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Data</th>
                  <th>Checker</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
                <% if (siteEarnings.length === 0) { %>
                  <tr>
                    <td colspan="3" class="text-center">Nenhum ganho do site registrado</td>
                  </tr>
                <% } else { %>
                  <% siteEarnings.forEach(earning => { %>
                    <tr>
                      <td><%= new Date(earning.created_at).toLocaleString() %></td>
                      <td><%= earning.checker ? earning.checker.title : 'N/A' %></td>
                      <td>R$ <%= earning.amount.toFixed(2) %></td>
                    </tr>
                  <% }); %>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
        </div>
      </div>
    </div>
  </div>
</div>
