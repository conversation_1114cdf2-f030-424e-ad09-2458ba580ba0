<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-history text-primary"></i> <b>LOGS DE ATIVIDADE</b></h4>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>ID</th>
                <th>Usuário</th>
                <th>Ação</th>
                <th>Detalhes</th>
                <th>Tipo de Recurso</th>
                <th>ID do Recurso</th>
                <th>IP</th>
                <th>Data</th>
              </tr>
            </thead>
            <tbody>
              <% if (logs && logs.length > 0) { %>
                <% logs.forEach(log => { %>
                  <tr>
                    <td><%= log.id %></td>
                    <td><%= log.user ? log.user.usuario : 'N/A' %></td>
                    <td><%= log.action %></td>
                    <td><%= log.details %></td>
                    <td><%= log.resource_type || 'N/A' %></td>
                    <td><%= log.resource_id || 'N/A' %></td>
                    <td><%= log.ip_address %></td>
                    <td><%= log.createdAt ? new Date(log.createdAt).toLocaleString('pt-BR') : 'N/A' %></td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="8" class="text-center">Nenhum log encontrado</td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
        
        <% if (pagination && pagination.totalPages > 1) { %>
          <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Navegação de páginas">
              <ul class="pagination">
                <% if (pagination.page > 1) { %>
                  <li class="page-item">
                    <a class="page-link" href="/admin/logs?page=<%= pagination.page - 1 %>" aria-label="Anterior">
                      <span aria-hidden="true">&laquo;</span>
                    </a>
                  </li>
                <% } %>
                
                <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                  <% if (i === pagination.page) { %>
                    <li class="page-item active"><a class="page-link" href="#"><%= i %></a></li>
                  <% } else if (i === 1 || i === pagination.totalPages || (i >= pagination.page - 2 && i <= pagination.page + 2)) { %>
                    <li class="page-item"><a class="page-link" href="/admin/logs?page=<%= i %>"><%= i %></a></li>
                  <% } else if (i === pagination.page - 3 || i === pagination.page + 3) { %>
                    <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                  <% } %>
                <% } %>
                
                <% if (pagination.page < pagination.totalPages) { %>
                  <li class="page-item">
                    <a class="page-link" href="/admin/logs?page=<%= pagination.page + 1 %>" aria-label="Próximo">
                      <span aria-hidden="true">&raquo;</span>
                    </a>
                  </li>
                <% } %>
              </ul>
            </nav>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>
