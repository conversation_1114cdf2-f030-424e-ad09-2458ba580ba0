<div class="content">
  <div class="container-fluid">

    <!-- Header -->
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title">
              <i class="fab fa-github text-primary mr-2"></i>
              <b>CONTROLE DE VERSÃO</b>
            </h4>
            <div class="d-flex">
              <button class="btn btn-sm btn-warning mr-2" onclick="showGitHubTokenModal()">
                <i class="fa fa-key"></i> Configurar API Key
              </button>
              <button class="btn btn-sm btn-info mr-2" onclick="checkForUpdates()">
                <i class="fa fa-sync-alt"></i> Verificar Atualizações
              </button>
              <button class="btn btn-sm btn-success mr-2" onclick="createBackup()">
                <i class="fa fa-save"></i> Criar <PERSON>
              </button>
              <a href="<%= githubUrl %>" target="_blank" class="btn btn-sm btn-dark">
                <i class="fab fa-github"></i> Ver no GitHub
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- GitHub Token Status -->
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="mb-1">
                  <i class="fa fa-key text-warning mr-2"></i>
                  Status da API Key do GitHub
                </h6>
                <p class="mb-0 text-muted" id="tokenStatus">Verificando...</p>
              </div>
              <div>
                <span class="badge badge-secondary" id="tokenBadge">Carregando...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Cards -->
    <div class="row">
      <!-- Current Version -->
      <div class="col-lg-6 col-md-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title">
              <i class="fa fa-code-branch text-success mr-2"></i>
              Versão Atual
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <p><strong>Branch:</strong> <span class="badge badge-primary"><%= currentVersion.branch %></span></p>
                <p><strong>Commit:</strong> <code><%= currentVersion.commit %></code></p>
                <p><strong>Data:</strong> <%= currentVersion.lastDate.toLocaleString('pt-BR') %></p>
              </div>
              <div class="col-md-6">
                <p><strong>Status:</strong>
                  <% if (currentVersion.hasChanges) { %>
                    <span class="badge badge-warning">Modificado</span>
                  <% } else { %>
                    <span class="badge badge-success">Limpo</span>
                  <% } %>
                </p>
                <p><strong>Última Mensagem:</strong></p>
                <small class="text-muted"><%= currentVersion.lastMessage %></small>
              </div>
            </div>

            <% if (currentVersion.hasChanges) { %>
            <div class="alert alert-warning mt-3">
              <strong>Atenção:</strong> Existem alterações locais não commitadas.
              <details class="mt-2">
                <summary>Ver alterações</summary>
                <pre class="mt-2"><%= currentVersion.status %></pre>
              </details>
            </div>
            <% } %>
          </div>
        </div>
      </div>

      <!-- Remote Version -->
      <div class="col-lg-6 col-md-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title">
              <i class="fab fa-github text-info mr-2"></i>
              Versão Remota (GitHub)
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <p><strong>Repositório:</strong> <code><%= githubRepo %></code></p>
                <p><strong>Commit:</strong> <code><%= remoteVersion.commit %></code></p>
                <p><strong>Data:</strong> <%= remoteVersion.lastDate.toLocaleString('pt-BR') %></p>
              </div>
              <div class="col-md-6">
                <p><strong>Status:</strong>
                  <% if (updateAvailable) { %>
                    <span class="badge badge-warning">Atualização Disponível</span>
                  <% } else { %>
                    <span class="badge badge-success">Atualizado</span>
                  <% } %>
                </p>
                <p><strong>Commits Atrás:</strong>
                  <span class="badge badge-<%= remoteVersion.behindCount > 0 ? 'danger' : 'success' %>">
                    <%= remoteVersion.behindCount %>
                  </span>
                </p>
              </div>
            </div>

            <p><strong>Última Mensagem:</strong></p>
            <small class="text-muted"><%= remoteVersion.lastMessage %></small>

            <% if (updateAvailable) { %>
            <div class="alert alert-info mt-3">
              <strong>Nova versão disponível!</strong>
              <div class="mt-2">
                <button class="btn btn-sm btn-warning" onclick="pullUpdates()"
                        <%= currentVersion.hasChanges ? 'disabled title="Faça backup das alterações locais primeiro"' : '' %>>
                  <i class="fa fa-download"></i> Atualizar Agora
                </button>
              </div>
            </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title">
              <i class="fa fa-cogs text-warning mr-2"></i>
              Ações do Sistema
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <button class="btn btn-block btn-info" onclick="checkForUpdates()">
                  <i class="fa fa-sync-alt"></i><br>
                  Verificar Atualizações
                </button>
              </div>
              <div class="col-md-3">
                <button class="btn btn-block btn-warning" onclick="pullUpdates()"
                        <%= currentVersion.hasChanges ? 'disabled' : '' %>>
                  <i class="fa fa-download"></i><br>
                  Baixar Atualizações
                </button>
              </div>
              <div class="col-md-3">
                <button class="btn btn-block btn-success" onclick="createBackup()">
                  <i class="fa fa-save"></i><br>
                  Criar Backup
                </button>
              </div>
              <div class="col-md-3">
                <button class="btn btn-block btn-danger" onclick="restartApplication()">
                  <i class="fa fa-power-off"></i><br>
                  Reiniciar App
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Commit History -->
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title">
              <i class="fa fa-history text-secondary mr-2"></i>
              Histórico de Commits
            </h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Hash</th>
                    <th>Mensagem</th>
                    <th>Autor</th>
                    <th>Data</th>
                  </tr>
                </thead>
                <tbody>
                  <% commitHistory.forEach(commit => { %>
                  <tr>
                    <td><code><%= commit.hash %></code></td>
                    <td><%= commit.message %></td>
                    <td><%= commit.author %></td>
                    <td><%= commit.date.toLocaleString('pt-BR') %></td>
                  </tr>
                  <% }) %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-sm" role="document">
    <div class="modal-content">
      <div class="modal-body text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Carregando...</span>
        </div>
        <p class="mt-2 mb-0" id="loadingText">Processando...</p>
      </div>
    </div>
  </div>
</div>

<!-- GitHub Token Modal -->
<div class="modal fade" id="githubTokenModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fab fa-github text-primary mr-2"></i>
          Configurar GitHub API Key
        </h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="alert alert-info">
          <strong>🔑 GitHub Token Necessário:</strong> Para acessar o repositório privado, configure um GitHub Personal Access Token.
          <br>
          <a href="/GITHUB-TOKEN-QUICK-SETUP.md" target="_blank" class="btn btn-sm btn-outline-info mt-2">
            <i class="fa fa-book"></i> Ver Guia Rápido
          </a>
          <a href="/GITHUB-TOKEN-SETUP.md" target="_blank" class="btn btn-sm btn-outline-secondary mt-2 ml-2">
            <i class="fa fa-file-text"></i> Guia Completo
          </a>
        </div>

        <form id="githubTokenForm">
          <div class="form-group">
            <label for="githubToken">GitHub Personal Access Token:</label>
            <div class="input-group">
              <input type="password" class="form-control" id="githubToken"
                     placeholder="ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                     autocomplete="new-password">
              <div class="input-group-append">
                <button type="button" class="btn btn-outline-secondary" onclick="toggleTokenVisibility()">
                  <i class="fa fa-eye" id="toggleIcon"></i>
                </button>
              </div>
            </div>
            <small class="form-text text-muted">
              O token deve ter permissões de <code>repo</code> para acessar repositórios privados.
            </small>
          </div>

          <div class="form-group">
            <div class="d-flex justify-content-between">
              <button type="button" class="btn btn-info" onclick="testGitHubToken()">
                <i class="fa fa-flask"></i> Testar Token
              </button>
              <div>
                <button type="button" class="btn btn-danger mr-2" onclick="removeGitHubToken()">
                  <i class="fa fa-trash"></i> Remover
                </button>
                <button type="submit" class="btn btn-success">
                  <i class="fa fa-save"></i> Salvar
                </button>
              </div>
            </div>
          </div>
        </form>

        <div id="tokenTestResult" class="mt-3" style="display: none;"></div>

        <hr>

        <div class="row">
          <div class="col-md-6">
            <h6><i class="fa fa-info-circle text-info mr-2"></i>Como criar um token:</h6>
            <ol class="small">
              <li>Acesse <a href="https://github.com/settings/tokens" target="_blank">GitHub Settings > Tokens</a></li>
              <li>Clique em "Generate new token (classic)"</li>
              <li>Marque a permissão <code>repo</code></li>
              <li>Copie o token gerado</li>
            </ol>
          </div>
          <div class="col-md-6">
            <h6><i class="fa fa-shield-alt text-success mr-2"></i>Segurança:</h6>
            <ul class="small">
              <li>O token é armazenado criptografado</li>
              <li>Apenas admins podem ver/editar</li>
              <li>Use tokens com escopo mínimo</li>
              <li>Configure expiração adequada</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Version Control JavaScript
const VersionControl = {
  showLoading: function(text = 'Processando...') {
    document.getElementById('loadingText').textContent = text;
    $('#loadingModal').modal('show');
  },

  hideLoading: function() {
    $('#loadingModal').modal('hide');
  },

  showNotification: function(message, type = 'success') {
    $.notify({
      message: message
    }, {
      type: type,
      placement: {
        from: 'top',
        align: 'right'
      },
      delay: 5000
    });
  }
};

function checkForUpdates() {
  VersionControl.showLoading('Verificando atualizações...');

  fetch('/admin/version-control/check-updates', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    VersionControl.hideLoading();

    if (data.success) {
      if (data.updateAvailable) {
        VersionControl.showNotification(
          `Nova versão disponível! ${data.behindCount} commits atrás.`,
          'warning'
        );
      } else {
        VersionControl.showNotification('Sistema está atualizado!', 'success');
      }

      // Reload page to show updated info
      setTimeout(() => location.reload(), 2000);
    } else {
      VersionControl.showNotification('Erro: ' + data.error, 'danger');
    }
  })
  .catch(error => {
    VersionControl.hideLoading();
    VersionControl.showNotification('Erro de conexão: ' + error.message, 'danger');
  });
}

function pullUpdates() {
  if (!confirm('Tem certeza que deseja atualizar o sistema? A aplicação será reiniciada.')) {
    return;
  }

  VersionControl.showLoading('Baixando atualizações...');

  fetch('/admin/version-control/pull-updates', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    VersionControl.hideLoading();

    if (data.success) {
      VersionControl.showNotification('Sistema atualizado com sucesso!', 'success');

      // Auto restart after update
      setTimeout(() => {
        restartApplication();
      }, 3000);
    } else {
      VersionControl.showNotification('Erro na atualização: ' + data.error, 'danger');

      if (data.output) {
        console.error('Git output:', data.output);
      }
    }
  })
  .catch(error => {
    VersionControl.hideLoading();
    VersionControl.showNotification('Erro de conexão: ' + error.message, 'danger');
  });
}

function createBackup() {
  VersionControl.showLoading('Criando backup...');

  fetch('/admin/version-control/backup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    VersionControl.hideLoading();

    if (data.success) {
      VersionControl.showNotification('Backup criado: ' + data.backupName, 'success');
    } else {
      VersionControl.showNotification('Erro ao criar backup: ' + data.error, 'danger');
    }
  })
  .catch(error => {
    VersionControl.hideLoading();
    VersionControl.showNotification('Erro de conexão: ' + error.message, 'danger');
  });
}

function restartApplication() {
  if (!confirm('Tem certeza que deseja reiniciar a aplicação? Todos os usuários serão desconectados temporariamente.')) {
    return;
  }

  VersionControl.showLoading('Reiniciando aplicação...');

  fetch('/admin/version-control/restart-app', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      VersionControl.showNotification('Aplicação reiniciada!', 'success');

      // Reload page after restart
      setTimeout(() => {
        location.reload();
      }, 5000);
    } else {
      VersionControl.hideLoading();
      VersionControl.showNotification('Erro ao reiniciar: ' + data.error, 'danger');
    }
  })
  .catch(error => {
    // Expected when app restarts
    VersionControl.showNotification('Aplicação reiniciando...', 'info');

    // Try to reload after restart
    setTimeout(() => {
      location.reload();
    }, 10000);
  });
}

// GitHub Token Management
function showGitHubTokenModal() {
  $('#githubTokenModal').modal('show');
  loadTokenStatus();
}

function toggleTokenVisibility() {
  const tokenInput = document.getElementById('githubToken');
  const toggleIcon = document.getElementById('toggleIcon');

  if (tokenInput.type === 'password') {
    tokenInput.type = 'text';
    toggleIcon.className = 'fa fa-eye-slash';
  } else {
    tokenInput.type = 'password';
    toggleIcon.className = 'fa fa-eye';
  }
}

function testGitHubToken() {
  const token = document.getElementById('githubToken').value;
  const resultDiv = document.getElementById('tokenTestResult');

  if (!token) {
    resultDiv.innerHTML = '<div class="alert alert-warning">Digite um token para testar</div>';
    resultDiv.style.display = 'block';
    return;
  }

  resultDiv.innerHTML = '<div class="alert alert-info"><i class="fa fa-spinner fa-spin"></i> Testando token...</div>';
  resultDiv.style.display = 'block';

  fetch('/admin/version-control/test-github-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ token })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      resultDiv.innerHTML = `
        <div class="alert alert-success">
          <strong>✅ Token válido!</strong><br>
          <small>
            Repositório: <code>${data.repository.name}</code><br>
            Privado: ${data.repository.private ? 'Sim' : 'Não'}<br>
            Proprietário: ${data.repository.owner}
          </small>
        </div>
      `;
    } else {
      resultDiv.innerHTML = `
        <div class="alert alert-danger">
          <strong>❌ Token inválido</strong><br>
          <small>${data.error}</small>
        </div>
      `;
    }
  })
  .catch(error => {
    resultDiv.innerHTML = `
      <div class="alert alert-danger">
        <strong>❌ Erro de conexão</strong><br>
        <small>${error.message}</small>
      </div>
    `;
  });
}

function saveGitHubToken() {
  const token = document.getElementById('githubToken').value;

  if (!token) {
    VersionControl.showNotification('Digite um token para salvar', 'warning');
    return;
  }

  VersionControl.showLoading('Salvando token...');

  fetch('/admin/version-control/save-github-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ token })
  })
  .then(response => response.json())
  .then(data => {
    VersionControl.hideLoading();

    if (data.success) {
      VersionControl.showNotification('Token salvo com sucesso!', 'success');
      $('#githubTokenModal').modal('hide');
      loadTokenStatus();
    } else {
      VersionControl.showNotification('Erro: ' + data.error, 'danger');
    }
  })
  .catch(error => {
    VersionControl.hideLoading();
    VersionControl.showNotification('Erro de conexão: ' + error.message, 'danger');
  });
}

function removeGitHubToken() {
  if (!confirm('Tem certeza que deseja remover o token GitHub?')) {
    return;
  }

  VersionControl.showLoading('Removendo token...');

  fetch('/admin/version-control/remove-github-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    VersionControl.hideLoading();

    if (data.success) {
      VersionControl.showNotification('Token removido com sucesso!', 'success');
      document.getElementById('githubToken').value = '';
      loadTokenStatus();
    } else {
      VersionControl.showNotification('Erro: ' + data.error, 'danger');
    }
  })
  .catch(error => {
    VersionControl.hideLoading();
    VersionControl.showNotification('Erro de conexão: ' + error.message, 'danger');
  });
}

function loadTokenStatus() {
  fetch('/admin/version-control/github-token-status')
  .then(response => response.json())
  .then(data => {
    const statusElement = document.getElementById('tokenStatus');
    const badgeElement = document.getElementById('tokenBadge');

    if (data.success) {
      if (data.hasToken) {
        statusElement.textContent = `Token configurado: ${data.tokenPreview}`;
        badgeElement.textContent = 'Configurado';
        badgeElement.className = 'badge badge-success';
      } else {
        statusElement.textContent = 'Nenhum token configurado';
        badgeElement.textContent = 'Não configurado';
        badgeElement.className = 'badge badge-warning';
      }
    } else {
      statusElement.textContent = 'Erro ao verificar status';
      badgeElement.textContent = 'Erro';
      badgeElement.className = 'badge badge-danger';
    }
  })
  .catch(error => {
    document.getElementById('tokenStatus').textContent = 'Erro de conexão';
    document.getElementById('tokenBadge').textContent = 'Erro';
    document.getElementById('tokenBadge').className = 'badge badge-danger';
  });
}

// GitHub Token Form Handler
document.getElementById('githubTokenForm').addEventListener('submit', function(e) {
  e.preventDefault();
  saveGitHubToken();
});

// Load token status on page load
document.addEventListener('DOMContentLoaded', function() {
  loadTokenStatus();
});

// Auto-check for updates every 5 minutes
setInterval(checkForUpdates, 5 * 60 * 1000);
</script>
