<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-tags text-primary"></i> <b>GERENCIAR PLANOS</b></h4>
        <div class="text-right">
          <a href="/admin/plans/add" class="btn btn-primary">
            <i class="fa fa-plus"></i> Adicionar Plano
          </a>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>Preço</th>
                <th>Créditos</th>
                <th>Duração</th>
                <th>Função Concedida</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (plans && plans.length > 0) { %>
                <% plans.forEach(plan => { %>
                  <tr>
                    <td><%= plan.id %></td>
                    <td><%= plan.name %></td>
                    <td>R$ <%= plan.price.toFixed(2) %></td>
                    <td><%= plan.credits %></td>
                    <td><%= plan.duration_days %> dias</td>
                    <td><%= plan.role_granted %></td>
                    <td>
                      <% if (plan.status === 'active') { %>
                        <span class="badge badge-success">Ativo</span>
                      <% } else { %>
                        <span class="badge badge-danger">Desativado</span>
                      <% } %>
                    </td>
                    <td>
                      <a href="/admin/plans/edit/<%= plan.id %>" class="btn btn-sm btn-primary">
                        <i class="fa fa-edit"></i>
                      </a>
                      <form method="POST" action="/admin/plans/delete/<%= plan.id %>?_method=DELETE" style="display: inline;">
                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Tem certeza que deseja excluir este plano?')">
                          <i class="fa fa-trash"></i>
                        </button>
                      </form>
                    </td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="8" class="text-center">Nenhum plano encontrado</td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
