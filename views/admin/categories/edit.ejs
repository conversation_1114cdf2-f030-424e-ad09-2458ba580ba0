<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-folder-open text-primary"></i> <b>EDITAR CATEGORIA</b></h4>
      </div>
      <div class="card-body">
        <form method="POST" action="/admin/categories/edit/<%= category.id %>">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="name">Nome da Categoria</label>
                <input type="text" class="form-control" id="name" name="name" value="<%= category.name %>" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="icon">Ícone (classe FontAwesome)</label>
                <input type="text" class="form-control" id="icon" name="icon" value="<%= category.icon %>">
                <small class="form-text text-muted">Ex: fa fa-credit-card, fa fa-check, etc.</small>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label for="description">Descrição</label>
            <textarea class="form-control" id="description" name="description" rows="3"><%= category.description || '' %></textarea>
          </div>
          
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="status">Status</label>
                <select class="form-control" id="status" name="status">
                  <option value="active" <%= category.status === 'active' ? 'selected' : '' %>>Ativo</option>
                  <option value="disabled" <%= category.status === 'disabled' ? 'selected' : '' %>>Desativado</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="display_order">Ordem de Exibição</label>
                <input type="number" class="form-control" id="display_order" name="display_order" value="<%= category.display_order %>">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="required_role">Função Requerida</label>
                <select class="form-control" id="required_role" name="required_role">
                  <option value="user" <%= category.required_role === 'user' ? 'selected' : '' %>>Usuário</option>
                  <option value="premium" <%= category.required_role === 'premium' ? 'selected' : '' %>>Premium</option>
                  <option value="vip" <%= category.required_role === 'vip' ? 'selected' : '' %>>VIP</option>
                  <option value="admin" <%= category.required_role === 'admin' ? 'selected' : '' %>>Administrador</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="form-group text-center">
            <button type="submit" class="btn btn-primary">Atualizar Categoria</button>
            <a href="/admin/categories" class="btn btn-secondary">Cancelar</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
