<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-folder text-primary"></i> <b>GERENCIAR CATEGORIAS</b></h4>
        <div class="text-right">
          <a href="/admin/categories/add" class="btn btn-primary">
            <i class="fa fa-plus"></i> Adicionar Categoria
          </a>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>Ícone</th>
                <th>Descrição</th>
                <th>Status</th>
                <th>Ordem</th>
                <th>Função Requerida</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (categories && categories.length > 0) { %>
                <% categories.forEach(category => { %>
                  <tr>
                    <td><%= category.id %></td>
                    <td><%= category.name %></td>
                    <td><i class="<%= category.icon %>"></i> <%= category.icon %></td>
                    <td><%= category.description || 'N/A' %></td>
                    <td>
                      <% if (category.status === 'active') { %>
                        <span class="badge badge-success">Ativo</span>
                      <% } else { %>
                        <span class="badge badge-danger">Desativado</span>
                      <% } %>
                    </td>
                    <td><%= category.display_order %></td>
                    <td><%= category.required_role %></td>
                    <td>
                      <a href="/admin/categories/edit/<%= category.id %>" class="btn btn-sm btn-primary">
                        <i class="fa fa-edit"></i>
                      </a>
                      <form method="POST" action="/admin/categories/delete/<%= category.id %>?_method=DELETE" style="display: inline;">
                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Tem certeza que deseja excluir esta categoria?')">
                          <i class="fa fa-trash"></i>
                        </button>
                      </form>
                    </td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="8" class="text-center">Nenhuma categoria encontrada</td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
