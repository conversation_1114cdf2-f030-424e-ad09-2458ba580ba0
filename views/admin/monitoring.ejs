<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center">
          <i class="fa fa-chart-line text-primary"></i>
          <b>MONITORAMENTO DO SISTEMA</b>
        </h4>
      </div>
      <div class="card-body">
        <!-- <PERSON><PERSON><PERSON> Ativos -->
        <div class="row mb-4">
          <div class="col-md-12">
            <div id="alerts-container" class="alert-container">
              <!-- Alertas serão carregados aqui via JavaScript -->
            </div>
          </div>
        </div>

        <!-- Métricas Principais -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5">
                    <div class="icon-big text-center icon-warning">
                      <i class="fa fa-server text-primary"></i>
                    </div>
                  </div>
                  <div class="col-7">
                    <div class="numbers">
                      <p class="card-category">Uptime</p>
                      <h4 class="card-title" id="uptime-metric">--</h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-3">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5">
                    <div class="icon-big text-center icon-warning">
                      <i class="fa fa-memory text-success"></i>
                    </div>
                  </div>
                  <div class="col-7">
                    <div class="numbers">
                      <p class="card-category">Memória</p>
                      <h4 class="card-title" id="memory-metric">--</h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-3">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5">
                    <div class="icon-big text-center icon-warning">
                      <i class="fa fa-tachometer-alt text-info"></i>
                    </div>
                  </div>
                  <div class="col-7">
                    <div class="numbers">
                      <p class="card-category">Requests/min</p>
                      <h4 class="card-title" id="requests-metric">--</h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-3">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5">
                    <div class="icon-big text-center icon-warning">
                      <i class="fa fa-exclamation-triangle text-danger"></i>
                    </div>
                  </div>
                  <div class="col-7">
                    <div class="numbers">
                      <p class="card-category">Taxa de Erro</p>
                      <h4 class="card-title" id="error-rate-metric">--</h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Gráficos -->
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Uso de Memória</h5>
              </div>
              <div class="card-body">
                <canvas id="memory-chart" height="200"></canvas>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Requests por Minuto</h5>
              </div>
              <div class="card-body">
                <canvas id="requests-chart" height="200"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Tabelas de Status -->
        <div class="row">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Status dos Serviços</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-sm">
                    <thead>
                      <tr>
                        <th>Serviço</th>
                        <th>Status</th>
                        <th>Última Verificação</th>
                      </tr>
                    </thead>
                    <tbody id="services-table">
                      <!-- Dados carregados via JavaScript -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Cache e Rate Limiting</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <h6>Cache Redis</h6>
                    <p>Hit Rate: <span id="cache-hit-rate" class="badge badge-info">--</span></p>
                    <p>Chaves: <span id="cache-keys" class="badge badge-secondary">--</span></p>
                  </div>
                  <div class="col-md-6">
                    <h6>Rate Limiting</h6>
                    <p>Bloqueios: <span id="rate-limit-blocks" class="badge badge-warning">--</span></p>
                    <p>Usuários Ativos: <span id="active-users" class="badge badge-success">--</span></p>
                  </div>
                </div>

                <div class="mt-3">
                  <button class="btn btn-sm btn-warning" onclick="clearCache()">
                    <i class="fa fa-trash"></i> Limpar Cache
                  </button>
                  <button class="btn btn-sm btn-info" onclick="refreshMetrics()">
                    <i class="fa fa-refresh"></i> Atualizar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Revisões de Código Pendentes -->
        <div class="row mt-4">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">
                  Revisões de Código Pendentes
                  <span id="pending-reviews-count" class="badge badge-warning">0</span>
                </h5>
              </div>
              <div class="card-body">
                <div id="pending-reviews-container">
                  <!-- Revisões pendentes carregadas via JavaScript -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.alert-container {
  max-height: 200px;
  overflow-y: auto;
}

.card-stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-stats .card-body {
  padding: 15px;
}

.card-stats .numbers {
  text-align: right;
}

.card-stats .card-category {
  color: rgba(255,255,255,0.8);
  font-size: 12px;
  margin-bottom: 5px;
}

.card-stats .card-title {
  color: white;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-healthy {
  background-color: #28a745;
}

.status-warning {
  background-color: #ffc107;
}

.status-error {
  background-color: #dc3545;
}

.metric-card {
  transition: transform 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
}
</style>

<script>
let metricsChart, requestsChart;
let eventSource;

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
  initializeCharts();
  loadInitialData();
  startRealTimeUpdates();
});

// Inicializar gráficos
function initializeCharts() {
  // Gráfico de memória
  const memoryCtx = document.getElementById('memory-chart').getContext('2d');
  metricsChart = new Chart(memoryCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: 'Heap Used (MB)',
        data: [],
        borderColor: '#e14eca',
        backgroundColor: 'rgba(225, 78, 202, 0.1)',
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Gráfico de requests
  const requestsCtx = document.getElementById('requests-chart').getContext('2d');
  requestsChart = new Chart(requestsCtx, {
    type: 'bar',
    data: {
      labels: [],
      datasets: [{
        label: 'Requests',
        data: [],
        backgroundColor: '#1f8ef1'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}

// Carregar dados iniciais
async function loadInitialData() {
  try {
    await Promise.all([
      loadMetrics(),
      loadServices(),
      loadPendingReviews()
    ]);
  } catch (err) {
    console.error('Erro ao carregar dados iniciais:', err);
    showAlert('Erro ao carregar dados do monitoramento', 'danger');
  }
}

// Carregar métricas
async function loadMetrics() {
  try {
    const response = await fetch('/api/monitoring/metrics');
    const data = await response.json();

    updateMetricsDisplay(data);
    updateCharts(data);
  } catch (err) {
    console.error('Erro ao carregar métricas:', err);
  }
}

// Atualizar exibição das métricas
function updateMetricsDisplay(data) {
  try {
    // Uptime
    const uptime = formatUptime(data.system?.uptime || 0);
    const uptimeElement = document.getElementById('uptime-metric');
    if (uptimeElement) uptimeElement.textContent = uptime;

    // Memória
    const memoryUsage = data.system?.memoryUsage || [];
    const memoryElement = document.getElementById('memory-metric');
    if (memoryElement) {
      if (memoryUsage.length > 0) {
        const latest = memoryUsage[memoryUsage.length - 1];
        const memoryPercent = latest.percentage || 0;
        memoryElement.textContent = `${memoryPercent.toFixed(1)}%`;
      } else {
        memoryElement.textContent = '--';
      }
    }

    // Requests
    const requestsPerMin = calculateRequestsPerMinute(data.requests || {});
    const requestsElement = document.getElementById('requests-metric');
    if (requestsElement) requestsElement.textContent = requestsPerMin;

    // Taxa de erro
    const errorRate = calculateErrorRate(data.requests || {});
    const errorElement = document.getElementById('error-rate-metric');
    if (errorElement) errorElement.textContent = `${errorRate.toFixed(2)}%`;

    // Cache
    const cacheHitElement = document.getElementById('cache-hit-rate');
    const cacheKeysElement = document.getElementById('cache-keys');

    if (cacheHitElement && data.cache) {
      cacheHitElement.textContent = `${(data.cache.hitRate || 0).toFixed(1)}%`;
    }
    if (cacheKeysElement && data.cache) {
      cacheKeysElement.textContent = data.cache.stats?.totalKeys || 0;
    }

    // Rate limiting
    const rateLimitElement = document.getElementById('rate-limit-blocks');
    if (rateLimitElement && data.security) {
      rateLimitElement.textContent = data.security.rateLimitHits || 0;
    }

    // Usuários ativos
    const activeUsersElement = document.getElementById('active-users');
    if (activeUsersElement) {
      activeUsersElement.textContent = data.requests?.byUser?.size || 0;
    }

    // Alertas
    updateAlerts(data.alerts || []);

  } catch (err) {
    console.error('Erro ao atualizar métricas:', err);
  }
}

// Atualizar gráficos
function updateCharts(data) {
  // Gráfico de memória
  if (data.system.memoryUsage && data.system.memoryUsage.length > 0) {
    const memoryData = data.system.memoryUsage.slice(-20); // Últimos 20 pontos

    metricsChart.data.labels = memoryData.map(item =>
      new Date(item.timestamp).toLocaleTimeString()
    );
    metricsChart.data.datasets[0].data = memoryData.map(item =>
      Math.round(item.heap / 1024 / 1024)
    );
    metricsChart.update();
  }

  // Gráfico de requests (simulado - você pode implementar coleta real)
  const now = new Date();
  const labels = [];
  const requestData = [];

  for (let i = 9; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 60000);
    labels.push(time.toLocaleTimeString());
    requestData.push(Math.floor(Math.random() * 100)); // Dados simulados
  }

  requestsChart.data.labels = labels;
  requestsChart.data.datasets[0].data = requestData;
  requestsChart.update();
}

// Carregar status dos serviços
async function loadServices() {
  try {
    const response = await fetch('/health/services');

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    const tbody = document.getElementById('services-table');
    if (!tbody) {
      console.warn('Elemento services-table não encontrado');
      return;
    }

    tbody.innerHTML = '';

    if (data.services && typeof data.services === 'object') {
      Object.entries(data.services).forEach(([service, info]) => {
        const row = document.createElement('tr');
        const statusClass = info.status === 'healthy' ? 'status-healthy' :
                           info.status === 'warning' ? 'status-warning' : 'status-error';

        const serviceName = service.charAt(0).toUpperCase() + service.slice(1);
        const statusText = info.status.charAt(0).toUpperCase() + info.status.slice(1);
        const lastCheck = info.lastCheck ? new Date(info.lastCheck).toLocaleString('pt-BR') : 'N/A';

        row.innerHTML = `
          <td>${serviceName}</td>
          <td>
            <span class="status-indicator ${statusClass}"></span>
            ${statusText}
          </td>
          <td>${lastCheck}</td>
        `;

        tbody.appendChild(row);
      });
    } else {
      // Mostrar linha de erro se não há dados de serviços
      const row = document.createElement('tr');
      row.innerHTML = `
        <td colspan="3" class="text-center text-muted">
          <i class="fa fa-exclamation-triangle"></i>
          Nenhum serviço encontrado
        </td>
      `;
      tbody.appendChild(row);
    }
  } catch (err) {
    console.error('Erro ao carregar serviços:', err);

    // Mostrar erro na tabela
    const tbody = document.getElementById('services-table');
    if (tbody) {
      tbody.innerHTML = `
        <tr>
          <td colspan="3" class="text-center text-danger">
            <i class="fa fa-exclamation-circle"></i>
            Erro ao carregar status dos serviços
          </td>
        </tr>
      `;
    }
  }
}

// Carregar revisões pendentes
async function loadPendingReviews() {
  try {
    const response = await fetch('/api/monitoring/code-reviews');

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Atualizar contador
    const pendingCount = data.codeReviews?.pending || 0;
    const countElement = document.getElementById('pending-reviews-count');
    if (countElement) {
      countElement.textContent = pendingCount;
    }

    // Mostrar lista se houver revisões pendentes
    const container = document.getElementById('pending-reviews-container');
    if (container) {
      if (pendingCount > 0) {
        container.innerHTML = `
          <div class="alert alert-warning">
            <i class="fa fa-exclamation-triangle"></i>
            Existem ${pendingCount} revisões de código aguardando aprovação.
            <a href="/admin/code-reviews" class="btn btn-sm btn-warning ml-2">
              Ver Revisões
            </a>
          </div>
        `;
      } else {
        container.innerHTML = `
          <div class="alert alert-success">
            <i class="fa fa-check"></i>
            Todas as revisões de código estão em dia!
          </div>
        `;
      }
    }
  } catch (err) {
    console.error('Erro ao carregar revisões:', err);

    // Mostrar erro no container
    const container = document.getElementById('pending-reviews-container');
    if (container) {
      container.innerHTML = `
        <div class="alert alert-danger">
          <i class="fa fa-exclamation-circle"></i>
          Erro ao carregar revisões de código. Verifique se o serviço está funcionando.
        </div>
      `;
    }

    // Definir contador como erro
    const countElement = document.getElementById('pending-reviews-count');
    if (countElement) {
      countElement.textContent = '?';
    }
  }
}

// Iniciar atualizações em tempo real
function startRealTimeUpdates() {
  if (eventSource) {
    eventSource.close();
  }

  eventSource = new EventSource('/api/monitoring/dashboard/stream');

  eventSource.onmessage = function(event) {
    try {
      const data = JSON.parse(event.data);
      if (data.type === 'metrics') {
        updateMetricsDisplay(data.data);
        updateCharts(data.data);
      }
    } catch (err) {
      console.error('Erro ao processar dados em tempo real:', err);
    }
  };

  eventSource.onerror = function(event) {
    console.error('Erro na conexão SSE:', event);
    // Tentar reconectar após 5 segundos
    setTimeout(() => {
      startRealTimeUpdates();
    }, 5000);
  };
}

// Funções utilitárias
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}d ${hours}h`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

function calculateRequestsPerMinute(requests) {
  // Implementar cálculo real baseado nos dados
  return requests.total || 0;
}

function calculateErrorRate(requests) {
  if (!requests.total || requests.total === 0) return 0;
  return (requests.failed / requests.total) * 100;
}

function updateAlerts(alerts) {
  const container = document.getElementById('alerts-container');

  if (alerts.length === 0) {
    container.innerHTML = '';
    return;
  }

  container.innerHTML = alerts.map(alert => `
    <div class="alert alert-${alert.data.severity === 'critical' ? 'danger' : 'warning'} alert-dismissible">
      <button type="button" class="close" onclick="acknowledgeAlert(${alert.id})">
        <span>&times;</span>
      </button>
      <strong>${alert.type}:</strong> ${alert.data.message || 'Alerta do sistema'}
      <small class="d-block">${new Date(alert.timestamp).toLocaleString()}</small>
    </div>
  `).join('');
}

// Ações
async function clearCache() {
  if (!confirm('Tem certeza que deseja limpar todo o cache?')) return;

  try {
    const response = await fetch('/api/monitoring/cache?pattern=*', {
      method: 'DELETE'
    });

    if (response.ok) {
      showAlert('Cache limpo com sucesso', 'success');
      await loadMetrics();
    } else {
      showAlert('Erro ao limpar cache', 'danger');
    }
  } catch (err) {
    console.error('Erro ao limpar cache:', err);
    showAlert('Erro ao limpar cache', 'danger');
  }
}

async function refreshMetrics() {
  await loadInitialData();
  showAlert('Métricas atualizadas', 'info');
}

async function acknowledgeAlert(alertId) {
  try {
    const response = await fetch(`/api/monitoring/alerts/${alertId}/acknowledge`, {
      method: 'POST'
    });

    if (response.ok) {
      await loadMetrics();
    }
  } catch (err) {
    console.error('Erro ao reconhecer alerta:', err);
  }
}

function showAlert(message, type) {
  // Implementar sistema de notificação
  console.log(`${type.toUpperCase()}: ${message}`);
}

// Limpar recursos quando sair da página
window.addEventListener('beforeunload', function() {
  if (eventSource) {
    eventSource.close();
  }
});
</script>
