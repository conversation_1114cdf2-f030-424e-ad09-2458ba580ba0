<!-- Modern Payment Transactions Page -->
<div class="payment-transactions-page">
  <div class="container-fluid px-4">
    <!-- Page Header -->
    <div class="page-header mb-4">
      <div class="row align-items-center">
        <div class="col">
          <h1 class="page-title">
            <i class="fas fa-credit-card text-primary me-3"></i>
            Transações de Pagamento
          </h1>
          <p class="page-subtitle text-muted">Monitore todos os pagamentos PIX e criptomoedas</p>
        </div>
        <div class="col-auto">
          <div class="page-actions">
            <button class="btn btn-outline-primary" onclick="window.location.reload()">
              <i class="fas fa-sync me-2"></i>Atualizar
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="payment-summary mb-4">
      <div class="row g-4">
        <div class="col-lg-3 col-md-6">
          <div class="summary-card total-payments">
            <div class="card-icon">
              <i class="fas fa-receipt"></i>
            </div>
            <div class="card-content">
              <h3 class="card-value"><%= paymentTransactions.length %></h3>
              <p class="card-label">Total de Pagamentos</p>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="summary-card pending-payments">
            <div class="card-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="card-content">
              <h3 class="card-value"><%= paymentTransactions.filter(p => p.status === 'pending').length %></h3>
              <p class="card-label">Pendentes</p>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="summary-card paid-payments">
            <div class="card-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="card-content">
              <h3 class="card-value"><%= paymentTransactions.filter(p => p.status === 'paid').length %></h3>
              <p class="card-label">Pagos</p>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6">
          <div class="summary-card total-amount">
            <div class="card-icon">
              <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="card-content">
              <h3 class="card-value">R$ <%= paymentTransactions.filter(p => p.status === 'paid').reduce((sum, p) => sum + parseFloat(p.amount), 0).toFixed(2) %></h3>
              <p class="card-label">Total Arrecadado</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Transactions Table -->
    <div class="row">
      <div class="col-12">
        <div class="card modern-card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fas fa-list me-2"></i>
              Transações de Pagamento
            </h6>
          </div>
          <div class="card-body">
            <% if (paymentTransactions && paymentTransactions.length > 0) { %>
              <div class="table-responsive">
                <table class="table modern-table" id="paymentTransactionsTable">
                  <thead>
                    <tr>
                      <th>
                        <i class="fas fa-hashtag me-2"></i>
                        ID
                      </th>
                      <th>
                        <i class="fas fa-user me-2"></i>
                        Usuário
                      </th>
                      <th>
                        <i class="fas fa-credit-card me-2"></i>
                        Método
                      </th>
                      <th>
                        <i class="fas fa-dollar-sign me-2"></i>
                        Valor
                      </th>
                      <th>
                        <i class="fas fa-coins me-2"></i>
                        Créditos
                      </th>
                      <th>
                        <i class="fas fa-toggle-on me-2"></i>
                        Status
                      </th>
                      <th>
                        <i class="fas fa-calendar me-2"></i>
                        Data
                      </th>
                      <th class="text-center">
                        <i class="fas fa-cogs me-2"></i>
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <% paymentTransactions.forEach(payment => { %>
                      <tr class="table-row">
                        <td>
                          <span class="id-badge">#<%= payment.id %></span>
                        </td>
                        <td>
                          <div class="user-info">
                            <div class="user-name"><%= payment.user ? payment.user.usuario : 'N/A' %></div>
                            <div class="user-email text-muted"><%= payment.user ? payment.user.email : 'N/A' %></div>
                          </div>
                        </td>
                        <td>
                          <% if (payment.payment_method === 'pix') { %>
                            <span class="method-badge pix">
                              <i class="fab fa-pix me-1"></i>
                              PIX
                            </span>
                          <% } else { %>
                            <span class="method-badge crypto">
                              <i class="fab fa-bitcoin me-1"></i>
                              <%= payment.crypto_currency || 'CRYPTO' %>
                            </span>
                          <% } %>
                        </td>
                        <td>
                          <div class="amount-info">
                            <span class="amount-value">R$ <%= parseFloat(payment.amount).toFixed(2) %></span>
                          </div>
                        </td>
                        <td>
                          <span class="credits-badge">
                            <i class="fas fa-coins me-1"></i>
                            <%= payment.credits %>
                          </span>
                        </td>
                        <td>
                          <% if (payment.status === 'pending') { %>
                            <span class="status-badge status-pending">
                              <i class="fas fa-clock me-1"></i>
                              Pendente
                            </span>
                          <% } else if (payment.status === 'paid') { %>
                            <span class="status-badge status-paid">
                              <i class="fas fa-check-circle me-1"></i>
                              Pago
                            </span>
                          <% } else if (payment.status === 'expired') { %>
                            <span class="status-badge status-expired">
                              <i class="fas fa-times-circle me-1"></i>
                              Expirado
                            </span>
                          <% } else if (payment.status === 'cancelled') { %>
                            <span class="status-badge status-cancelled">
                              <i class="fas fa-ban me-1"></i>
                              Cancelado
                            </span>
                          <% } else { %>
                            <span class="status-badge status-failed">
                              <i class="fas fa-exclamation-triangle me-1"></i>
                              Falhou
                            </span>
                          <% } %>
                        </td>
                        <td>
                          <div class="date-info">
                            <div class="date-primary">
                              <%= new Date(payment.createdAt).toLocaleDateString('pt-BR') %>
                            </div>
                            <div class="date-secondary">
                              <%= new Date(payment.createdAt).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }) %>
                            </div>
                          </div>
                        </td>
                        <td class="text-center">
                          <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-info" 
                                    onclick="viewPaymentDetails('<%= payment.external_id %>')" 
                                    title="Ver Detalhes">
                              <i class="fas fa-eye"></i>
                            </button>
                            <% if (payment.status === 'pending') { %>
                              <button class="btn btn-sm btn-outline-primary" 
                                      onclick="checkPaymentStatus('<%= payment.external_id %>')" 
                                      title="Verificar Status">
                                <i class="fas fa-sync"></i>
                              </button>
                            <% } %>
                          </div>
                        </td>
                      </tr>
                    <% }); %>
                  </tbody>
                </table>
              </div>
            <% } else { %>
              <div class="empty-state">
                <div class="empty-icon">
                  <i class="fas fa-credit-card"></i>
                </div>
                <h5 class="empty-title">Nenhuma transação de pagamento encontrada</h5>
                <p class="empty-description">As transações de pagamento aparecerão aqui quando os usuários comprarem créditos.</p>
              </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>

    <!-- API Transactions (if available) -->
    <% if (apiTransactions && apiTransactions.transactions) { %>
    <div class="row mt-4">
      <div class="col-12">
        <div class="card modern-card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fas fa-cloud me-2"></i>
              Transações da API PagFly (Últimas 20)
            </h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table modern-table">
                <thead>
                  <tr>
                    <th>ID Externo</th>
                    <th>Valor</th>
                    <th>Método</th>
                    <th>Status</th>
                    <th>Data</th>
                  </tr>
                </thead>
                <tbody>
                  <% apiTransactions.transactions.forEach(transaction => { %>
                    <tr>
                      <td><%= transaction.externalId %></td>
                      <td>R$ <%= parseFloat(transaction.amount).toFixed(2) %></td>
                      <td><%= transaction.paymentMethod.toUpperCase() %></td>
                      <td>
                        <span class="status-badge status-<%= transaction.status %>">
                          <%= transaction.status %>
                        </span>
                      </td>
                      <td><%= new Date(transaction.createdAt).toLocaleString('pt-BR') %></td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <% } %>
  </div>
</div>

<!-- Payment Details Modal -->
<div class="modal fade" id="paymentDetailsModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-info-circle me-2"></i>
          Detalhes do Pagamento
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div id="paymentDetailsContent">
          <!-- Conteúdo será carregado dinamicamente -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Custom Styles -->
<style>
/* =========================================================
 * Payment Transactions Page Styles
 ========================================================= */
.payment-transactions-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 1.6rem 0;
}

.page-header {
  margin-bottom: 1.6rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.4rem;
}

.page-subtitle {
  font-size: 0.9rem;
  margin-bottom: 0;
}

/* Summary Cards */
.summary-card {
  background: white;
  border-radius: 16px;
  padding: 1.6rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1.2rem;
  height: 100%;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15);
}

.summary-card .card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  flex-shrink: 0;
}

.total-payments .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pending-payments .card-icon {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.paid-payments .card-icon {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.total-amount .card-icon {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* Table Styles */
.modern-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modern-card .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 1.2rem 1.6rem;
  color: white;
}

.modern-table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  padding: 1rem 0.8rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.4px;
}

.modern-table tbody td {
  padding: 1rem 0.8rem;
  border: none;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
}

/* Badges and Status */
.method-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.method-badge.pix {
  background: rgba(50, 188, 173, 0.1);
  color: #32bcad;
  border: 1px solid rgba(50, 188, 173, 0.3);
}

.method-badge.crypto {
  background: rgba(247, 147, 26, 0.1);
  color: #f7931a;
  border: 1px solid rgba(247, 147, 26, 0.3);
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.status-pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-paid {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-expired, .status-cancelled, .status-failed {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.credits-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Other Elements */
.id-badge {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.8rem;
}

.user-email {
  font-size: 0.7rem;
}

.amount-value {
  font-weight: 700;
  color: #28a745;
  font-size: 0.8rem;
}

.date-primary {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.7rem;
}

.date-secondary {
  font-size: 0.6rem;
  color: #6c757d;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3.2rem 1.6rem;
  color: #6c757d;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;
  color: white;
}
</style>

<!-- JavaScript -->
<script>
$(document).ready(function() {
  if ($.fn.DataTable) {
    $('#paymentTransactionsTable').DataTable({
      responsive: true,
      language: {
        url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json'
      },
      pageLength: 25,
      order: [[0, 'desc']],
      columnDefs: [
        { orderable: false, targets: [7] }
      ]
    });
  }
});

async function viewPaymentDetails(externalId) {
  try {
    const response = await fetch(`/api/payments/details/${externalId}`);
    const result = await response.json();
    
    if (result.success) {
      const modal = new bootstrap.Modal(document.getElementById('paymentDetailsModal'));
      document.getElementById('paymentDetailsContent').innerHTML = `
        <div class="payment-details">
          <h6>Informações da Transação</h6>
          <pre>${JSON.stringify(result.data, null, 2)}</pre>
        </div>
      `;
      modal.show();
    } else {
      alert('Erro ao carregar detalhes do pagamento');
    }
  } catch (error) {
    console.error('Erro:', error);
    alert('Erro ao carregar detalhes do pagamento');
  }
}

async function checkPaymentStatus(externalId) {
  try {
    const response = await fetch(`/api/payments/admin-status/${externalId}`);
    const result = await response.json();
    
    if (result.success) {
      alert(`Status atualizado: ${result.status}`);
      window.location.reload();
    } else {
      alert('Erro ao verificar status do pagamento');
    }
  } catch (error) {
    console.error('Erro:', error);
    alert('Erro ao verificar status do pagamento');
  }
}
</script>
