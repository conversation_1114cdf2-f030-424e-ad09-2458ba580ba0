<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-user-edit text-primary"></i> <b>EDITAR USUÁRIO</b></h4>
      </div>
      <div class="card-body">
        <form method="POST" action="/admin/users/edit/<%= editUser.id %>">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="usuario">Nome de Usuário</label>
                <input type="text" class="form-control" id="usuario" name="usuario" value="<%= editUser.usuario %>" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="email">Email</label>
                <input type="email" class="form-control" id="email" name="email" value="<%= editUser.email || '' %>">
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="senha">Nova Senha (deixe em branco para manter a atual)</label>
                <input type="password" class="form-control" id="senha" name="senha">
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="role">Função</label>
                <select class="form-control" id="role" name="role">
                  <option value="user" <%= editUser.role === 'user' ? 'selected' : '' %>>Usuário</option>
                  <option value="premium" <%= editUser.role === 'premium' ? 'selected' : '' %>>Premium</option>
                  <option value="vip" <%= editUser.role === 'vip' ? 'selected' : '' %>>VIP</option>
                  <option value="affiliate" <%= editUser.role === 'affiliate' ? 'selected' : '' %>>Afiliado</option>
                  <option value="programmer" <%= editUser.role === 'programmer' ? 'selected' : '' %>>Programador</option>
                  <option value="admin" <%= editUser.role === 'admin' || editUser.rank === 1 ? 'selected' : '' %>>Administrador</option>
                </select>
                <small class="form-text text-muted">
                  <ul class="mt-2">
                    <li><strong>Afiliado:</strong> Acesso apenas ao painel de ganhos</li>
                    <li><strong>Programador:</strong> Acesso ao painel de admin (exceto logs e transações)</li>
                    <li><strong>Administrador:</strong> Acesso completo a todas as funcionalidades</li>
                  </ul>
                </small>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="saldo">Saldo</label>
                <input type="number" class="form-control" id="saldo" name="saldo" value="<%= editUser.saldo %>" step="0.1">
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="status">Status</label>
                <select class="form-control" id="status" name="status">
                  <option value="active" <%= editUser.status === 'active' ? 'selected' : '' %>>Ativo</option>
                  <option value="suspended" <%= editUser.status === 'suspended' ? 'selected' : '' %>>Suspenso</option>
                  <option value="banned" <%= editUser.status === 'banned' ? 'selected' : '' %>>Banido</option>
                </select>
              </div>
            </div>
          </div>

          <div class="form-group text-center">
            <button type="submit" class="btn btn-primary">Atualizar Usuário</button>
            <a href="/admin/users" class="btn btn-secondary">Cancelar</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
