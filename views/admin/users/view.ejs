<div class="row fade-in">
  <!-- Hidden elements for flash messages -->
  <div id="success-message" class="d-none"><%= typeof success_msg !== 'undefined' ? success_msg : '' %></div>
  <div id="error-message" class="d-none"><%= typeof error_msg !== 'undefined' ? error_msg : '' %></div>

  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title"><i class="fa fa-users text-primary mr-2"></i> <b>VISUALIZAR USUÁRIOS</b></h4>
      </div>

      <div class="card-body">
        <!-- Filter Controls -->
        <div class="row mb-4">
          <div class="col-md-3 mb-2">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fa fa-search"></i></span>
              </div>
              <input type="text" id="userSearch" class="form-control" placeholder="Buscar usuário...">
            </div>
          </div>

          <div class="col-md-3 mb-2">
            <select id="roleFilter" class="form-control">
              <option value="">Todas as funções</option>
              <option value="admin">Administrador</option>
              <option value="vip">VIP</option>
              <option value="premium">Premium</option>
              <option value="user">Usuário</option>
            </select>
          </div>

          <div class="col-md-3 mb-2">
            <select id="statusFilter" class="form-control">
              <option value="">Todos os status</option>
              <option value="active">Ativo</option>
              <option value="suspended">Suspenso</option>
              <option value="banned">Banido</option>
            </select>
          </div>

          <div class="col-md-3 mb-2 text-right">
            <button id="refreshTable" class="btn btn-primary">
              <i class="fa fa-sync-alt"></i> Atualizar
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-hover data-table" id="usersTable">
            <thead>
              <tr>
                <th>ID</th>
                <th>Usuário</th>
                <th>Email</th>
                <th>Função</th>
                <th>Saldo</th>
                <th>Status</th>
                <th>Criado por</th>
                <th>Último Login</th>
              </tr>
            </thead>
            <tbody>
              <% if (users && users.length > 0) { %>
                <% users.forEach(user => { %>
                  <tr data-user-id="<%= user.id %>" data-user-role="<%= user.role || 'user' %>" data-user-status="<%= user.status || 'active' %>">
                    <td><%= user.id %></td>
                    <td>
                      <div class="d-flex align-items-center">
                        <img src="https://www.gravatar.com/avatar/<%= user.gravatarHash %>?s=30&d=mp" class="rounded-circle mr-2" width="30" alt="<%= user.usuario %>">
                        <span><%= user.usuario %></span>
                      </div>
                    </td>
                    <td><%= user.email || 'N/A' %></td>
                    <td>
                      <% if (user.rank === 1 || user.role === 'admin') { %>
                        <span class="badge badge-danger">Admin</span>
                      <% } else if (user.role === 'premium') { %>
                        <span class="badge badge-warning">Premium</span>
                      <% } else if (user.role === 'vip') { %>
                        <span class="badge badge-info">VIP</span>
                      <% } else if (user.role === 'programmer') { %>
                        <span class="badge badge-primary">Programador</span>
                      <% } else if (user.role === 'affiliate') { %>
                        <span class="badge badge-success">Afiliado</span>
                      <% } else { %>
                        <span class="badge badge-secondary">Usuário</span>
                      <% } %>
                    </td>
                    <td><%= user.saldo.toFixed(2) %></td>
                    <td>
                      <% if (user.status === 'active') { %>
                        <span class="badge badge-success">Ativo</span>
                      <% } else if (user.status === 'suspended') { %>
                        <span class="badge badge-warning">Suspenso</span>
                      <% } else { %>
                        <span class="badge badge-danger">Banido</span>
                      <% } %>
                    </td>
                    <td><%= user.criador || 'N/A' %></td>
                    <td><%= user.lastLogin ? new Date(user.lastLogin).toLocaleString('pt-BR', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    }) : 'Nunca' %></td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="8" class="text-center py-4">
                    <i class="fa fa-users fa-3x mb-3 text-muted"></i>
                    <p>Nenhum usuário encontrado</p>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    // Initialize DataTable
    const table = $('#usersTable').DataTable({
      language: {
        url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json'
      },
      pageLength: 25,
      order: [[0, 'desc']], // Sort by ID descending
    });

    // Search functionality
    $('#userSearch').on('keyup', function() {
      table.search(this.value).draw();
    });

    // Role filter
    $('#roleFilter').on('change', function() {
      const role = $(this).val();
      table.column(3).search(role).draw();
    });

    // Status filter
    $('#statusFilter').on('change', function() {
      const status = $(this).val();
      table.column(5).search(status).draw();
    });

    // Refresh table
    $('#refreshTable').on('click', function() {
      location.reload();
    });
  });
</script>
