<div class="row">
  <div class="col-md-12">
    <div class="card modern-card">
      <div class="card-header">
        <h4 class="card-title text-center">
          <i class="fa fa-users text-primary"></i>
          <b>GERENCIAR USUÁRIOS</b>
        </h4>
        <div class="text-center mt-2">
          <a href="/admin/users/add" class="btn btn-primary btn-sm">
            <i class="fa fa-plus"></i> Adicionar Usuário
          </a>
        </div>
      </div>

      <div class="card-body">
        <!-- Filter Controls -->
        <div class="row mb-4">
          <div class="col-md-3 mb-2">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fa fa-search"></i></span>
              </div>
              <input type="text" id="userSearch" class="form-control" placeholder="Buscar usuário...">
            </div>
          </div>

          <div class="col-md-2 mb-2">
            <select id="roleFilter" class="form-control">
              <option value="">Todas as funções</option>
              <option value="admin">Administrador</option>
              <option value="vip">VIP</option>
              <option value="premium">Premium</option>
              <option value="user">Usuário</option>
            </select>
          </div>

          <div class="col-md-2 mb-2">
            <select id="statusFilter" class="form-control">
              <option value="">Todos os status</option>
              <option value="active">Ativo</option>
              <option value="suspended">Suspenso</option>
              <option value="banned">Banido</option>
            </select>
          </div>

          <div class="col-md-2 mb-2">
            <div class="alert alert-info mb-0 text-center">
              <strong><%= users.length %></strong> usuários
            </div>
          </div>

          <div class="col-md-2 mb-2 text-right">
            <button id="refreshTable" class="btn btn-primary">
              <i class="fa fa-sync-alt"></i> Atualizar
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-hover" id="usersTable">
            <thead>
              <tr>
                <th>ID</th>
                <th>Usuário</th>
                <th>Função</th>
                <th>Saldo</th>
                <th>Status</th>
                <th>Criado por</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (users && users.length > 0) { %>
                <% users.forEach(user => { %>
                  <tr>
                    <td><%= user.id %></td>
                    <td><%= user.usuario %></td>
                    <td>
                      <% if (user.rank === 1 || user.role === 'admin') { %>
                        <span class="badge badge-danger">Admin</span>
                      <% } else if (user.role === 'premium') { %>
                        <span class="badge badge-warning">Premium</span>
                      <% } else if (user.role === 'vip') { %>
                        <span class="badge badge-info">VIP</span>
                      <% } else { %>
                        <span class="badge badge-secondary">Usuário</span>
                      <% } %>
                    </td>
                    <td>R$ <%= user.saldo.toFixed(2) %></td>
                    <td>
                      <% if (user.status === 'active') { %>
                        <span class="badge badge-success">Ativo</span>
                      <% } else if (user.status === 'suspended') { %>
                        <span class="badge badge-warning">Suspenso</span>
                      <% } else { %>
                        <span class="badge badge-danger">Banido</span>
                      <% } %>
                    </td>
                    <td><%= user.criador || 'Sistema' %></td>
                    <td>
                      <a href="/admin/users/edit/<%= user.id %>" class="btn btn-sm btn-primary" title="Editar">
                        <i class="fa fa-edit"></i>
                      </a>
                      <button class="btn btn-sm btn-success add-credits-btn" data-user-id="<%= user.id %>" data-username="<%= user.usuario %>" title="Adicionar Créditos">
                        <i class="fa fa-plus"></i>
                      </button>
                      <% if (user.status === 'active') { %>
                        <button class="btn btn-sm btn-warning suspend-user-btn" data-user-id="<%= user.id %>" data-username="<%= user.usuario %>" title="Suspender">
                          <i class="fa fa-pause"></i>
                        </button>
                      <% } else { %>
                        <button class="btn btn-sm btn-info activate-user-btn" data-user-id="<%= user.id %>" data-username="<%= user.usuario %>" title="Ativar">
                          <i class="fa fa-play"></i>
                        </button>
                      <% } %>
                    </td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="7" class="text-center py-4">
                    <i class="fa fa-users fa-3x mb-3 text-muted"></i>
                    <p>Nenhum usuário encontrado</p>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Credits Modal -->
<div class="modal fade" id="addCreditsModal" tabindex="-1" role="dialog" aria-labelledby="addCreditsModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addCreditsModalLabel">Adicionar Créditos</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="addCreditsForm" method="POST" action="/admin/users/add-credits">
        <div class="modal-body">
          <input type="hidden" id="creditUserId" name="userId">
          <div class="form-group">
            <label for="creditUsername">Usuário</label>
            <input type="text" class="form-control" id="creditUsername" readonly>
          </div>
          <div class="form-group">
            <label for="creditAmount">Quantidade de Créditos</label>
            <input type="number" class="form-control" id="creditAmount" name="amount" step="0.01" min="0.01" required>
          </div>
          <div class="form-group">
            <label for="creditDescription">Descrição</label>
            <textarea class="form-control" id="creditDescription" name="description" rows="2" required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
          <button type="submit" class="btn btn-primary">Adicionar</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    // Initialize DataTable simples
    const table = $('#usersTable').DataTable({
      language: {
        url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json'
      },
      pageLength: 25,
      order: [[0, 'desc']] // Sort by ID descending
    });

    // Search functionality
    $('#userSearch').on('keyup', function() {
      table.search(this.value).draw();
    });

    // Role filter
    $('#roleFilter').on('change', function() {
      const role = $(this).val();
      table.column(2).search(role).draw();
    });

    // Status filter
    $('#statusFilter').on('change', function() {
      const status = $(this).val();
      table.column(4).search(status).draw();
    });

    // Refresh table
    $('#refreshTable').on('click', function() {
      location.reload();
    });



    // Add credits modal
    $('.add-credits-btn').on('click', function(e) {
      e.preventDefault();
      const userId = $(this).data('user-id');
      const username = $(this).data('username');

      $('#creditUserId').val(userId);
      $('#creditUsername').val(username);
      $('#addCreditsModal').modal('show');
    });

    // Delete user confirmation
    $('.delete-user-btn').on('click', function(e) {
      e.preventDefault();
      const userId = $(this).data('user-id');
      const username = $(this).data('username');

      if (confirm(`Tem certeza que deseja excluir o usuário ${username}? Esta ação não pode ser desfeita.`)) {
        window.location.href = `/admin/users/delete/${userId}?_method=DELETE`;
      }
    });
  });
</script>
