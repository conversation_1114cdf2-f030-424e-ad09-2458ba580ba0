<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-user-plus text-primary"></i> <b>ADICIONAR USUÁRIO</b></h4>
      </div>
      <div class="card-body">
        <form method="POST" action="/admin/users/add">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="usuario">Nome de Usuário</label>
                <input type="text" class="form-control" id="usuario" name="usuario" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="email">Email</label>
                <input type="email" class="form-control" id="email" name="email">
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="senha">Senha</label>
                <input type="password" class="form-control" id="senha" name="senha" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="role">Função</label>
                <select class="form-control" id="role" name="role">
                  <option value="user">Usuário</option>
                  <option value="premium">Premium</option>
                  <option value="vip">VIP</option>
                  <option value="affiliate">Afiliado</option>
                  <option value="programmer">Programador</option>
                  <option value="admin">Administrador</option>
                </select>
                <small class="form-text text-muted">
                  <ul class="mt-2">
                    <li><strong>Afiliado:</strong> Acesso apenas ao painel de ganhos</li>
                    <li><strong>Programador:</strong> Acesso ao painel de admin (exceto logs e transações)</li>
                    <li><strong>Administrador:</strong> Acesso completo a todas as funcionalidades</li>
                  </ul>
                </small>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="saldo">Saldo Inicial</label>
                <input type="number" class="form-control" id="saldo" name="saldo" value="0" step="0.1">
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="status">Status</label>
                <select class="form-control" id="status" name="status">
                  <option value="active">Ativo</option>
                  <option value="suspended">Suspenso</option>
                  <option value="banned">Banido</option>
                </select>
              </div>
            </div>
          </div>

          <div class="form-group text-center">
            <button type="submit" class="btn btn-primary">Adicionar Usuário</button>
            <a href="/admin/users" class="btn btn-secondary">Cancelar</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
