<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-users text-primary"></i> <b>USUÁRIOS CADASTRADOS</b></h4>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Usuário</th>
                <th>Rank</th>
                <th>Saldo</th>
                <th>Lives</th>
                <th>Criador</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% users.forEach(user => { %>
                <tr>
                  <td><%= user.id %></td>
                  <td><%= user.usuario %></td>
                  <td><%= user.rank === 1 ? 'Admin' : 'Usuário' %></td>
                  <td><%= user.saldo %></td>
                  <td><%= user.lives %></td>
                  <td><%= user.criador %></td>
                  <td>
                    <button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#editModal<%= user.id %>">Editar</button>
                    <form method="POST" action="/users/delete/<%= user.id %>?_method=DELETE" style="display: inline;">
                      <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Tem certeza que deseja excluir este usuário?')">Excluir</button>
                    </form>
                  </td>
                </tr>
                
                <!-- Edit Modal -->
                <div class="modal fade" id="editModal<%= user.id %>" tabindex="-1" role="dialog" aria-labelledby="editModalLabel<%= user.id %>" aria-hidden="true">
                  <div class="modal-dialog" role="document">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title" id="editModalLabel<%= user.id %>">Editar Usuário: <%= user.usuario %></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                          <span aria-hidden="true">&times;</span>
                        </button>
                      </div>
                      <form method="POST" action="/users/update/<%= user.id %>">
                        <div class="modal-body">
                          <div class="form-group">
                            <label for="usuario<%= user.id %>">Usuário</label>
                            <input type="text" id="usuario<%= user.id %>" name="usuario" class="form-control" value="<%= user.usuario %>" required>
                          </div>
                          <div class="form-group">
                            <label for="senha<%= user.id %>">Nova Senha (deixe em branco para manter a atual)</label>
                            <input type="password" id="senha<%= user.id %>" name="senha" class="form-control" placeholder="Nova senha">
                          </div>
                          <div class="form-group">
                            <label for="rank<%= user.id %>">Rank</label>
                            <select id="rank<%= user.id %>" name="rank" class="form-control">
                              <option value="0" <%= user.rank === 0 ? 'selected' : '' %>>Usuário</option>
                              <option value="1" <%= user.rank === 1 ? 'selected' : '' %>>Administrador</option>
                            </select>
                          </div>
                          <div class="form-group">
                            <label for="saldo<%= user.id %>">Saldo</label>
                            <input type="number" id="saldo<%= user.id %>" name="saldo" class="form-control" value="<%= user.saldo %>" step="0.1" required>
                          </div>
                        </div>
                        <div class="modal-footer">
                          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                          <button type="submit" class="btn btn-primary">Salvar</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              <% }); %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
