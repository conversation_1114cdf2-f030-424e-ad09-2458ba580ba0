<!-- Dashboard Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="dashboard-header">
      <div class="welcome-section">
        <h2 class="welcome-title">
          <i class="fas fa-tachometer-alt text-gradient"></i>
          Bem-vindo, <span class="user-name"><%= user.usuario %></span>
        </h2>
        <p class="welcome-subtitle">
          <% if (user.rank === 1 || user.role === 'admin') { %>
            Painel Administrativo - Controle total do sistema
          <% } else if (user.role === 'programmer') { %>
            Painel do Programador - Gerencie suas ferramentas
          <% } else if (user.role === 'affiliate') { %>
            Painel do Afiliado - Acompanhe seus ganhos
          <% } else { %>
            Painel do Cliente - Acesse suas ferramentas
          <% } %>
        </p>
      </div>
      <div class="user-badge">
        <% if (user.rank === 1 || user.role === 'admin') { %>
          <span class="role-badge admin"><i class="fas fa-crown"></i> Administrador</span>
        <% } else if (user.role === 'programmer') { %>
          <span class="role-badge programmer"><i class="fas fa-code"></i> Programador</span>
        <% } else if (user.role === 'affiliate') { %>
          <span class="role-badge affiliate"><i class="fas fa-handshake"></i> Afiliado</span>
        <% } else if (user.role === 'premium') { %>
          <span class="role-badge premium"><i class="fas fa-star"></i> Premium</span>
        <% } else if (user.role === 'vip') { %>
          <span class="role-badge vip"><i class="fas fa-gem"></i> VIP</span>
        <% } else { %>
          <span class="role-badge user"><i class="fas fa-user"></i> Cliente</span>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
  <div class="col-lg-3 col-md-6 mb-3">
    <div class="stat-card user-card">
      <div class="stat-icon">
        <i class="fas fa-user"></i>
      </div>
      <div class="stat-content">
        <h3 class="stat-value"><%= user.usuario %></h3>
        <p class="stat-label">Usuário Ativo</p>
      </div>
      <div class="stat-trend">
        <i class="fas fa-check-circle text-success"></i>
      </div>
    </div>
  </div>
  <div class="col-lg-3 col-md-6 mb-3">
    <div class="stat-card balance-card">
      <div class="stat-icon">
        <i class="fas fa-credit-card"></i>
      </div>
      <div class="stat-content">
        <h3 class="stat-value"><%= user.saldo.toFixed(2) %></h3>
        <p class="stat-label">Créditos Disponíveis</p>
      </div>
      <div class="stat-trend">
        <i class="fas fa-wallet text-success"></i>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6 mb-3">
    <div class="stat-card users-card">
      <div class="stat-icon">
        <i class="fas fa-users"></i>
      </div>
      <div class="stat-content">
        <h3 class="stat-value"><%= activeUsers %></h3>
        <p class="stat-label">Usuários Ativos</p>
      </div>
      <div class="stat-trend">
        <i class="fas fa-arrow-up text-success"></i>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6 mb-3">
    <div class="stat-card success-card">
      <div class="stat-icon">
        <i class="fas fa-check-circle"></i>
      </div>
      <div class="stat-content">
        <h3 class="stat-value"><%= user.lives || 0 %></h3>
        <p class="stat-label">Aprovações Totais</p>
      </div>
      <div class="stat-trend">
        <i class="fas fa-thumbs-up text-success"></i>
      </div>
    </div>
  </div>
</div>

<!-- Recent Transactions -->
<div class="row">
  <div class="col-12">
    <div class="card modern-card">
      <div class="card-header">
        <div class="header-content">
          <h5 class="card-title">
            <i class="fas fa-exchange-alt text-primary"></i>
            Transações Recentes
          </h5>
          <a href="/painel/transactions" class="btn btn-sm btn-outline-primary">
            <i class="fas fa-eye"></i> Ver Todas
          </a>
        </div>
      </div>
      <div class="card-body">
        <% if (recentTransactions && recentTransactions.length > 0) { %>
          <div class="transactions-list">
            <% recentTransactions.forEach(transaction => { %>
              <div class="transaction-item">
                <div class="transaction-icon">
                  <i class="fas fa-<%= transaction.type === 'credit' ? 'plus-circle text-success' : 'minus-circle text-danger' %>"></i>
                </div>
                <div class="transaction-details">
                  <div class="transaction-main">
                    <span class="transaction-description"><%= transaction.description %></span>
                    <span class="transaction-amount <%= transaction.type === 'credit' ? 'positive' : 'negative' %>">
                      <%= transaction.type === 'credit' ? '+' : '-' %><%= Math.abs(transaction.amount).toFixed(2) %>
                    </span>
                  </div>
                  <div class="transaction-meta">
                    <span class="transaction-checker">
                      <% if (transaction.checker) { %>
                        <i class="fas fa-check-circle"></i> <%= transaction.checker.title %>
                      <% } else { %>
                        <i class="fas fa-cog"></i> Sistema
                      <% } %>
                    </span>
                    <span class="transaction-date">
                      <i class="fas fa-clock"></i> <%= new Date(transaction.createdAt).toLocaleString('pt-BR') %>
                    </span>
                  </div>
                </div>
                <div class="transaction-status">
                  <span class="status-badge <%= transaction.type === 'credit' ? 'success' : 'danger' %>">
                    <%= transaction.type === 'credit' ? 'Crédito' : 'Débito' %>
                  </span>
                </div>
              </div>
            <% }); %>
          </div>
        <% } else { %>
          <div class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-receipt"></i>
            </div>
            <h6 class="empty-title">Nenhuma transação encontrada</h6>
            <p class="empty-description">Suas transações aparecerão aqui quando você começar a usar o sistema.</p>
            <a href="/painel/credits" class="btn btn-primary btn-sm">
              <i class="fas fa-plus"></i> Comprar Créditos
            </a>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>
