<!-- Credits Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card modern-card">
      <div class="card-header">
        <div class="header-content">
          <h5 class="card-title">
            <i class="fas fa-credit-card text-primary"></i>
            Comprar Créditos
          </h5>
        </div>
      </div>
      <div class="card-body">
        <div class="credits-info">
          <div class="row">
            <div class="col-lg-8">
              <h6 class="credits-title">Adicione créditos à sua conta</h6>
              <p class="credits-description">
                Os créditos são utilizados para acessar as ferramentas de verificação.
                Cada ferramenta tem um custo específico em créditos.
              </p>
              <div class="current-balance">
                <span class="balance-label">Saldo atual:</span>
                <span class="balance-amount">
                  <i class="fas fa-coins"></i> <%= user.saldo.toFixed(2) %> créditos
                </span>
              </div>
            </div>
            <div class="col-lg-4">
              <div class="balance-card">
                <div class="balance-icon">
                  <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                  <h4 class="balance-value"><%= user.saldo.toFixed(2) %></h4>
                  <p class="balance-text">Créditos Disponíveis</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Quick Credit Options -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card modern-card">
      <div class="card-header">
        <div class="header-content">
          <h5 class="card-title">
            <i class="fas fa-bolt text-primary"></i>
            Opções Rápidas
          </h5>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="quick-option" data-amount="10">
              <div class="quick-amount">R$ 10,00</div>
              <div class="quick-credits">100 créditos</div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="quick-option" data-amount="25">
              <div class="quick-amount">R$ 25,00</div>
              <div class="quick-credits">250 créditos</div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="quick-option" data-amount="50">
              <div class="quick-amount">R$ 50,00</div>
              <div class="quick-credits">500 créditos</div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="quick-option" data-amount="100">
              <div class="quick-amount">R$ 100,00</div>
              <div class="quick-credits">1000 créditos</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Payment Methods -->
<div class="row">
  <div class="col-12">
    <div class="card modern-card">
      <div class="card-header">
        <div class="header-content">
          <h5 class="card-title">
            <i class="fas fa-payment text-primary"></i>
            Formas de Pagamento
          </h5>
        </div>
      </div>
      <div class="card-body">
        <div class="payment-methods">
          <div class="row">
            <div class="col-lg-6 col-md-6 mb-3">
              <div class="payment-method active" data-payment="pix">
                <div class="payment-icon pix">
                  <i class="fab fa-pix"></i>
                </div>
                <div class="payment-info">
                  <h6 class="payment-name">PIX</h6>
                  <p class="payment-description">Pagamento instantâneo via QR Code</p>
                  <small class="payment-time">Aprovação em até 5 minutos</small>
                </div>
                <div class="payment-check">
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
            </div>
            <div class="col-lg-6 col-md-6 mb-3">
              <div class="payment-method" data-payment="crypto">
                <div class="payment-icon crypto">
                  <i class="fab fa-bitcoin"></i>
                </div>
                <div class="payment-info">
                  <h6 class="payment-name">Criptomoedas</h6>
                  <p class="payment-description">Bitcoin, Ethereum, USDT e outras</p>
                  <small class="payment-time">Aprovação em até 30 minutos</small>
                </div>
                <div class="payment-check">
                  <i class="fas fa-check-circle"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- Crypto Currency Selection -->
          <div id="cryptoSelection" class="crypto-selection" style="display: none;">
            <h6 class="mb-3">Escolha a Criptomoeda:</h6>
            <div class="row">
              <div class="col-lg-2 col-md-4 col-6 mb-2">
                <div class="crypto-option" data-crypto="BTC">
                  <i class="fab fa-bitcoin"></i>
                  <span>Bitcoin</span>
                </div>
              </div>
              <div class="col-lg-2 col-md-4 col-6 mb-2">
                <div class="crypto-option" data-crypto="ETH">
                  <i class="fab fa-ethereum"></i>
                  <span>Ethereum</span>
                </div>
              </div>
              <div class="col-lg-2 col-md-4 col-6 mb-2">
                <div class="crypto-option" data-crypto="USDT">
                  <i class="fas fa-dollar-sign"></i>
                  <span>USDT</span>
                </div>
              </div>
              <div class="col-lg-2 col-md-4 col-6 mb-2">
                <div class="crypto-option" data-crypto="LTC">
                  <i class="fab fa-bitcoin"></i>
                  <span>Litecoin</span>
                </div>
              </div>
              <div class="col-lg-2 col-md-4 col-6 mb-2">
                <div class="crypto-option" data-crypto="BCH">
                  <i class="fab fa-bitcoin"></i>
                  <span>Bitcoin Cash</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Custom Amount Section -->
        <div class="custom-amount-section mt-4">
          <h6 class="mb-3">Ou escolha um valor personalizado:</h6>
          <div class="row align-items-end">
            <div class="col-md-6">
              <label for="customAmount" class="form-label">Valor (R$)</label>
              <input type="number" class="form-control" id="customAmount" min="5" max="1000" step="0.01" placeholder="Ex: 50.00">
              <small class="form-text text-muted">Mínimo: R$ 5,00 | Máximo: R$ 1.000,00</small>
            </div>
            <div class="col-md-6">
              <div class="credits-preview">
                <span class="credits-label">Créditos que você receberá:</span>
                <span class="credits-amount" id="creditsPreview">0</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Customer Information Section -->
        <div class="customer-info-section mt-4">
          <h6 class="mb-3">Informações do Cliente:</h6>
          <div class="row">
            <div class="col-md-6">
              <label for="customerName" class="form-label">Nome Completo</label>
              <input type="text" class="form-control" id="customerName" placeholder="Seu nome completo" value="<%= user.usuario || '' %>">
            </div>
            <div class="col-md-6">
              <label for="customerDocument" class="form-label">CPF</label>
              <input type="text" class="form-control" id="customerDocument" placeholder="000.000.000-00" maxlength="14">
              <small class="form-text text-muted">Necessário para pagamentos PIX</small>
            </div>
          </div>
        </div>

        <!-- Payment Button -->
        <div class="payment-action mt-4">
          <button class="btn btn-primary btn-lg" id="paymentBtn" disabled>
            <i class="fas fa-credit-card me-2"></i>
            Prosseguir com Pagamento
          </button>
        </div>

        <div class="payment-info-section mt-4">
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>Informações importantes:</strong>
            <ul class="mb-0 mt-2">
              <li>Os créditos são adicionados automaticamente após a confirmação do pagamento</li>
              <li><strong>PIX:</strong> Créditos liberados em até 5 minutos</li>
              <li><strong>Criptomoedas:</strong> Créditos liberados em até 30 minutos</li>
              <li>Conversão: 1 real = 10 créditos</li>
              <li>Todos os pagamentos são processados de forma segura</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Payment Modal Overlay -->
<div id="paymentOverlay" class="payment-overlay" style="display: none;">
  <div class="payment-modal">
    <div class="payment-modal-header">
      <h5 class="payment-modal-title">
        <i class="fab fa-pix me-2"></i>
        Pagamento PIX
      </h5>
      <button type="button" class="payment-close-btn" onclick="closePaymentModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="payment-modal-body">
      <div id="paymentContent">
        <!-- Conteúdo será carregado dinamicamente -->
      </div>
    </div>
  </div>
</div>

<style>
/* Quick Options Styles */
.quick-option {
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.quick-option:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.quick-option.selected {
  border-color: var(--primary);
  background: rgba(102, 126, 234, 0.1);
}

.quick-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.quick-credits {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Payment Methods Styles */
.payment-method {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.payment-method:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.payment-method.active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.payment-check {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #667eea;
  font-size: 1.2rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.payment-method.active .payment-check {
  opacity: 1;
}

.payment-icon.pix {
  background: linear-gradient(135deg, #32bcad 0%, #1e7e34 100%);
}

.payment-icon.crypto {
  background: linear-gradient(135deg, #f7931a 0%, #ff6b35 100%);
}

.payment-time {
  color: #28a745;
  font-weight: 500;
}

/* Crypto Selection */
.crypto-selection {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 12px;
  margin-top: 1rem;
}

.crypto-option {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.crypto-option:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.crypto-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.crypto-option i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.crypto-option span {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Custom Amount */
.credits-preview {
  background: #e3f2fd;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.credits-label {
  display: block;
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.credits-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1976d2;
}

.payment-action {
  text-align: center;
}

#paymentBtn {
  min-width: 250px;
  padding: 0.75rem 2rem;
  border-radius: 25px;
}

#paymentBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Payment Modal Overlay - Seguindo design original */
.payment-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.payment-modal {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
}

.payment-modal-header {
  background: var(--primary-gradient);
  color: var(--text-primary);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.payment-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.payment-close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.payment-modal-body {
  padding: 2rem;
}

.pix-container {
  text-align: center;
}

.pix-header {
  margin-bottom: 2rem;
}

.pix-header h6 {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.pix-header p {
  color: var(--text-secondary);
  margin: 0;
}

.qr-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 2rem;
  margin-bottom: 1.5rem;
}

.qr-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
}

.qr-container img {
  border-radius: var(--radius-md);
  border: 2px solid var(--border-color);
}

.qr-instruction {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.pix-code-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.pix-code-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.pix-input-group {
  display: flex;
  gap: 0.5rem;
}

.pix-code-input {
  flex: 1;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0.75rem;
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  word-break: break-all;
}

.pix-copy-btn {
  background: var(--success);
  border: none;
  border-radius: var(--radius-md);
  color: white;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  white-space: nowrap;
}

.pix-copy-btn:hover {
  background: #3d8b5c;
  transform: translateY(-1px);
}

.payment-details {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  color: var(--text-primary);
  font-weight: 600;
}

.payment-info {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: var(--radius-lg);
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.payment-info p {
  color: var(--text-primary);
  margin: 0;
  font-size: 0.875rem;
}

.payment-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: rgba(237, 137, 54, 0.1);
  border: 1px solid rgba(237, 137, 54, 0.2);
  border-radius: var(--radius-lg);
  color: var(--warning);
  font-weight: 500;
}

.payment-status i {
  margin-right: 0.5rem;
  animation: pulse 2s infinite;
}

.payment-status.success {
  background: rgba(72, 187, 120, 0.1);
  border-color: rgba(72, 187, 120, 0.2);
  color: var(--success);
}

.payment-status.error {
  background: rgba(245, 101, 101, 0.1);
  border-color: rgba(245, 101, 101, 0.2);
  color: var(--danger);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Responsive */
@media (max-width: 768px) {
  .payment-modal {
    width: 95%;
    margin: 1rem;
  }

  .payment-modal-body {
    padding: 1rem;
  }

  .qr-section,
  .pix-code-section,
  .payment-details {
    padding: 1rem;
  }

  .pix-input-group {
    flex-direction: column;
  }

  .qr-container img {
    max-width: 200px;
    height: auto;
  }
}
</style>

<script>
let selectedPaymentMethod = 'pix';
let selectedAmount = null;
let selectedCrypto = null;

// Selecionar método de pagamento
function selectPaymentMethod(method) {
  selectedPaymentMethod = method;

  // Atualizar visual
  document.querySelectorAll('.payment-method').forEach(el => {
    el.classList.remove('active');
  });

  // Encontrar o elemento clicado e ativá-lo
  document.querySelector(`[data-payment="${method}"]`).classList.add('active');

  // Mostrar/esconder seleção de crypto
  const cryptoSelection = document.getElementById('cryptoSelection');
  if (method === 'crypto') {
    cryptoSelection.style.display = 'block';
  } else {
    cryptoSelection.style.display = 'none';
    selectedCrypto = null;
  }

  updatePaymentButton();
}

// Selecionar opção rápida
function selectQuickOption(amount) {
  selectedAmount = amount;

  // Limpar valor customizado
  document.getElementById('customAmount').value = '';
  document.getElementById('creditsPreview').textContent = (amount * 10).toString();

  // Atualizar visual das opções rápidas
  document.querySelectorAll('.quick-option').forEach(el => {
    el.classList.remove('selected');
  });

  // Encontrar a opção clicada e ativá-la
  const quickOption = document.querySelector(`[data-amount="${amount}"]`);
  if (quickOption) {
    quickOption.classList.add('selected');
  }

  updatePaymentButton();
}

// Carregar moedas disponíveis
async function loadAvailableCurrencies() {
  try {
    const response = await fetch('/api/payments/crypto-currencies');
    const result = await response.json();

    if (result.success) {
      const cryptoOptions = document.getElementById('cryptoOptions');
      cryptoOptions.innerHTML = '';

      result.data.forEach(currency => {
        const col = document.createElement('div');
        col.className = 'col-lg-2 col-md-4 col-6 mb-2';

        col.innerHTML = `
          <div class="crypto-option" data-crypto="${currency.code}">
            <i class="${currency.icon}"></i>
            <span>${currency.name}</span>
          </div>
        `;

        cryptoOptions.appendChild(col);
      });

      // Adicionar event listeners
      setupCryptoEventListeners();
    } else {
      console.error('Erro ao carregar moedas:', result.message);
      // Fallback para moedas padrão
      loadDefaultCurrencies();
    }
  } catch (error) {
    console.error('Erro ao carregar moedas:', error);
    loadDefaultCurrencies();
  }
}

// Carregar moedas padrão em caso de erro
function loadDefaultCurrencies() {
  const cryptoOptions = document.getElementById('cryptoOptions');
  const defaultCurrencies = [
    { code: 'BTC', name: 'Bitcoin', icon: 'fab fa-bitcoin' },
    { code: 'ETH', name: 'Ethereum', icon: 'fab fa-ethereum' },
    { code: 'USDT', name: 'USDT', icon: 'fas fa-dollar-sign' },
    { code: 'LTC', name: 'Litecoin', icon: 'fab fa-bitcoin' }
  ];

  cryptoOptions.innerHTML = '';

  defaultCurrencies.forEach(currency => {
    const col = document.createElement('div');
    col.className = 'col-lg-2 col-md-4 col-6 mb-2';

    col.innerHTML = `
      <div class="crypto-option" data-crypto="${currency.code}">
        <i class="${currency.icon}"></i>
        <span>${currency.name}</span>
      </div>
    `;

    cryptoOptions.appendChild(col);
  });

  setupCryptoEventListeners();
}

// Configurar event listeners para crypto
function setupCryptoEventListeners() {
  document.querySelectorAll('.crypto-option').forEach(option => {
    option.addEventListener('click', function() {
      selectedCrypto = this.dataset.crypto;

      // Atualizar visual
      document.querySelectorAll('.crypto-option').forEach(el => {
        el.classList.remove('selected');
      });
      this.classList.add('selected');

      updatePaymentButton();
    });
  });
}

// Função para aplicar máscara de CPF
function applyCpfMask(value) {
  return value
    .replace(/\D/g, '')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d{1,2})/, '$1-$2')
    .replace(/(-\d{2})\d+?$/, '$1');
}

// Função para validar CPF
function isValidCpf(cpf) {
  cpf = cpf.replace(/[^\d]+/g, '');
  if (cpf.length !== 11 || !!cpf.match(/(\d)\1{10}/)) return false;

  let sum = 0;
  let remainder;

  for (let i = 1; i <= 9; i++) {
    sum = sum + parseInt(cpf.substring(i-1, i)) * (11 - i);
  }

  remainder = (sum * 10) % 11;
  if ((remainder === 10) || (remainder === 11)) remainder = 0;
  if (remainder !== parseInt(cpf.substring(9, 10))) return false;

  sum = 0;
  for (let i = 1; i <= 10; i++) {
    sum = sum + parseInt(cpf.substring(i-1, i)) * (12 - i);
  }

  remainder = (sum * 10) % 11;
  if ((remainder === 10) || (remainder === 11)) remainder = 0;
  if (remainder !== parseInt(cpf.substring(10, 11))) return false;

  return true;
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
  // Carregar moedas disponíveis
  loadAvailableCurrencies();

  // Event listeners para opções rápidas
  document.querySelectorAll('.quick-option').forEach(option => {
    option.addEventListener('click', function() {
      const amount = parseFloat(this.dataset.amount);
      selectQuickOption(amount);
    });
  });

  // Event listeners para métodos de pagamento
  document.querySelectorAll('.payment-method').forEach(method => {
    method.addEventListener('click', function() {
      const paymentType = this.dataset.payment;
      selectPaymentMethod(paymentType);
    });
  });

  // Event listener para botão de pagamento
  document.getElementById('paymentBtn').addEventListener('click', function() {
    processPayment();
  });

  // Máscara de CPF
  document.getElementById('customerDocument').addEventListener('input', function() {
    this.value = applyCpfMask(this.value);
    updatePaymentButton();
  });

  // Validação do nome
  document.getElementById('customerName').addEventListener('input', function() {
    updatePaymentButton();
  });

  // Valor customizado
  document.getElementById('customAmount').addEventListener('input', function() {
    const amount = parseFloat(this.value) || 0;
    const credits = Math.floor(amount * 10);
    document.getElementById('creditsPreview').textContent = credits;

    // Limpar seleção de opção rápida
    selectedAmount = null;
    document.querySelectorAll('.quick-option').forEach(el => {
      el.classList.remove('selected');
    });

    updatePaymentButton();
  });
});

// Atualizar botão de pagamento
function updatePaymentButton() {
  const btn = document.getElementById('paymentBtn');
  const customAmount = parseFloat(document.getElementById('customAmount').value) || 0;
  const customerName = document.getElementById('customerName').value.trim();
  const customerDocument = document.getElementById('customerDocument').value.trim();

  let canProceed = false;

  // Verificar se tem opção rápida ou valor customizado
  const hasValidAmount = selectedAmount || (customAmount >= 5 && customAmount <= 1000);

  // Verificar dados do cliente
  const hasValidCustomer = customerName.length >= 3;

  if (hasValidAmount && hasValidCustomer) {
    if (selectedPaymentMethod === 'pix') {
      // Para PIX, precisa de CPF válido
      canProceed = isValidCpf(customerDocument);
    } else if (selectedPaymentMethod === 'crypto' && selectedCrypto) {
      // Para crypto, não precisa de CPF
      canProceed = true;
    }
  }

  btn.disabled = !canProceed;

  if (canProceed) {
    btn.innerHTML = `<i class="fas fa-credit-card me-2"></i>Pagar via ${selectedPaymentMethod.toUpperCase()}`;
  } else {
    let message = 'Prosseguir com Pagamento';
    if (!hasValidAmount) {
      message = 'Selecione um valor ou digite um valor personalizado';
    } else if (!hasValidCustomer) {
      message = 'Preencha seu nome completo';
    } else if (selectedPaymentMethod === 'pix' && !isValidCpf(customerDocument)) {
      message = 'CPF inválido para PIX';
    } else if (selectedPaymentMethod === 'crypto' && !selectedCrypto) {
      message = 'Selecione uma criptomoeda';
    }
    btn.innerHTML = `<i class="fas fa-credit-card me-2"></i>${message}`;
  }
}

// Processar pagamento
async function processPayment() {
  console.log('processPayment chamado');
  const btn = document.getElementById('paymentBtn');
  const originalText = btn.innerHTML;

  try {
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processando...';

    const customAmount = parseFloat(document.getElementById('customAmount').value) || 0;
    const customerName = document.getElementById('customerName').value.trim();
    const customerDocument = document.getElementById('customerDocument').value.replace(/[^\d]/g, '');

    const finalAmount = selectedAmount || customAmount;

    const paymentData = {
      customAmount: finalAmount,
      customerName: customerName,
      customerDocument: customerDocument
    };

    console.log('Dados do pagamento:', paymentData);
    console.log('Método selecionado:', selectedPaymentMethod);

    let endpoint = '/api/payments/create-pix';
    if (selectedPaymentMethod === 'crypto') {
      endpoint = '/api/payments/create-crypto';
      paymentData.cryptoCurrency = selectedCrypto;
    }

    console.log('Fazendo requisição para:', endpoint);
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(paymentData)
    });

    console.log('Status da resposta:', response.status);
    const result = await response.json();
    console.log('Resultado:', result);

    if (result.success) {
      showPaymentModal(result.data);
    } else {
      throw new Error(result.message || 'Erro ao processar pagamento');
    }

  } catch (error) {
    console.error('Erro:', error);

    if (typeof $.notify !== 'undefined') {
      $.notify({
        icon: 'fas fa-exclamation-triangle',
        message: error.message || 'Erro ao processar pagamento'
      }, {
        type: 'danger',
        timer: 5000
      });
    } else {
      alert(error.message || 'Erro ao processar pagamento');
    }
  } finally {
    btn.disabled = false;
    btn.innerHTML = originalText;
  }
}

// Mostrar modal de pagamento
function showPaymentModal(paymentData) {
  const overlay = document.getElementById('paymentOverlay');
  const content = document.getElementById('paymentContent');

  if (selectedPaymentMethod === 'pix') {
    content.innerHTML = `
      <div class="pix-container">
        <div class="pix-header">
          <h6><i class="fab fa-pix me-2"></i>Pagamento PIX</h6>
          <p>Escaneie o QR Code ou copie o código para realizar o pagamento</p>
        </div>

        <div class="qr-section">
          <div class="qr-container" id="qrContainer">
            <!-- QR Code será gerado aqui -->
          </div>
          <p class="qr-instruction">
            <i class="fas fa-mobile-alt me-1"></i>
            Abra seu app bancário e escaneie o código
          </p>
        </div>

        <div class="pix-code-section">
          <div class="pix-code-label">
            <i class="fas fa-copy me-1"></i>
            Ou copie o código PIX:
          </div>
          <div class="pix-input-group">
            <input type="text" class="pix-code-input" value="${paymentData.qrCodeText || paymentData.paymentUrl}" readonly id="pixCode">
            <button class="pix-copy-btn" id="copyPixBtn">
              <i class="fas fa-copy me-1"></i> Copiar
            </button>
          </div>
        </div>

        <div class="payment-details">
          <div class="detail-row">
            <span class="detail-label">Valor:</span>
            <span class="detail-value">R$ ${paymentData.amount.toFixed(2)}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Créditos:</span>
            <span class="detail-value">${paymentData.credits} créditos</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Expira em:</span>
            <span class="detail-value">${new Date(paymentData.expiresAt).toLocaleString('pt-BR')}</span>
          </div>
        </div>

        <div class="payment-info">
          <p><i class="fas fa-info-circle me-2"></i><strong>Pagamento automático:</strong> Os créditos serão adicionados à sua conta em até 5 minutos após a confirmação do pagamento.</p>
        </div>

        <div class="payment-status" id="paymentStatus">
          <i class="fas fa-clock"></i>
          Aguardando pagamento...
        </div>
      </div>
    `;

    // Gerar QR Code em base64
    generateQRCodeBase64(paymentData.qrCodeText || paymentData.paymentUrl);
  } else {
    content.innerHTML = `
      <div class="pix-container">
        <div class="pix-header">
          <h6><i class="fab fa-bitcoin me-2"></i>Pagamento ${paymentData.cryptoCurrency}</h6>
          <p>Envie o valor exato para o endereço abaixo</p>
        </div>

        <div class="payment-details">
          <div class="detail-row">
            <span class="detail-label">Valor em ${paymentData.cryptoCurrency}:</span>
            <span class="detail-value">${paymentData.cryptoAmount}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Valor em Reais:</span>
            <span class="detail-value">R$ ${paymentData.amount.toFixed(2)}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Créditos:</span>
            <span class="detail-value">${paymentData.credits} créditos</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Expira em:</span>
            <span class="detail-value">${new Date(paymentData.expiresAt).toLocaleString('pt-BR')}</span>
          </div>
        </div>

        <div class="pix-code-section">
          <div class="pix-code-label">
            <i class="fas fa-wallet me-1"></i>
            Endereço para envio:
          </div>
          <div class="pix-input-group">
            <input type="text" class="pix-code-input" value="${paymentData.paymentUrl}" readonly id="cryptoAddress">
            <button class="pix-copy-btn" id="copyCryptoBtn">
              <i class="fas fa-copy me-1"></i> Copiar
            </button>
          </div>
        </div>

        <div class="payment-info">
          <p><i class="fas fa-exclamation-triangle me-2"></i><strong>Importante:</strong> Envie exatamente o valor especificado para o endereço acima. Os créditos serão adicionados após a confirmação da transação.</p>
        </div>

        <div class="payment-status" id="paymentStatus">
          <i class="fas fa-clock"></i>
          Aguardando pagamento...
        </div>
      </div>
    `;
  }

  // Mostrar overlay
  overlay.style.display = 'flex';

  // Adicionar event listeners para botões de copiar
  setTimeout(() => {
    if (selectedPaymentMethod === 'pix') {
      const copyBtn = document.getElementById('copyPixBtn');
      if (copyBtn) copyBtn.addEventListener('click', copyPixCode);
    } else {
      const copyBtn = document.getElementById('copyCryptoBtn');
      if (copyBtn) copyBtn.addEventListener('click', copyCryptoAddress);
    }
  }, 100);

  // Iniciar verificação de status
  startPaymentStatusCheck(paymentData.externalId);
}

// Gerar QR Code em base64
function generateQRCodeBase64(text) {
  if (typeof QRCode !== 'undefined') {
    try {
      QRCode.toDataURL(text, {
        width: 280,
        height: 280,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#ffffff'
        }
      }, function (error, url) {
        const qrContainer = document.getElementById('qrContainer');
        if (error) {
          console.error('Erro ao gerar QR Code:', error);
          // Fallback para API externa
          qrContainer.innerHTML = `<img src="https://api.qrserver.com/v1/create-qr-code/?size=280x280&data=${encodeURIComponent(text)}" alt="QR Code PIX">`;
        } else {
          // Usar base64 gerado
          qrContainer.innerHTML = `<img src="${url}" alt="QR Code PIX">`;
        }
      });
    } catch (error) {
      console.error('Erro ao usar QRCode.js:', error);
      // Fallback para API externa
      const qrContainer = document.getElementById('qrContainer');
      qrContainer.innerHTML = `<img src="https://api.qrserver.com/v1/create-qr-code/?size=280x280&data=${encodeURIComponent(text)}" alt="QR Code PIX">`;
    }
  } else {
    // Fallback: usar API externa para gerar QR Code
    const qrContainer = document.getElementById('qrContainer');
    qrContainer.innerHTML = `<img src="https://api.qrserver.com/v1/create-qr-code/?size=280x280&data=${encodeURIComponent(text)}" alt="QR Code PIX">`;
  }
}

// Fechar modal de pagamento
function closePaymentModal() {
  const overlay = document.getElementById('paymentOverlay');
  overlay.style.display = 'none';
}

// Copiar código PIX
function copyPixCode() {
  const input = document.getElementById('pixCode');
  input.select();
  document.execCommand('copy');

  if (typeof $.notify !== 'undefined') {
    $.notify({
      icon: 'fas fa-check',
      message: 'Código PIX copiado!'
    }, {
      type: 'success',
      timer: 3000
    });
  }
}

// Copiar endereço crypto
function copyCryptoAddress() {
  const input = document.getElementById('cryptoAddress');
  input.select();
  document.execCommand('copy');

  if (typeof $.notify !== 'undefined') {
    $.notify({
      icon: 'fas fa-check',
      message: 'Endereço copiado!'
    }, {
      type: 'success',
      timer: 3000
    });
  }
}

// Verificar status do pagamento
function startPaymentStatusCheck(externalId) {
  const checkInterval = setInterval(async () => {
    try {
      const response = await fetch(`/api/payments/admin-status/${externalId}`);
      const result = await response.json();

      if (result.success && result.status === 'paid') {
        clearInterval(checkInterval);

        // Atualizar indicador de status
        const statusElement = document.getElementById('paymentStatus');
        if (statusElement) {
          statusElement.className = 'payment-status success';
          statusElement.innerHTML = `
            <i class="fas fa-check-circle"></i>
            Pagamento confirmado! Processando créditos...
          `;
        }

        // Aguardar 2 segundos antes de fechar
        setTimeout(() => {
          // Fechar modal
          closePaymentModal();

          // Mostrar sucesso
          if (typeof $.notify !== 'undefined') {
            $.notify({
              icon: 'fas fa-check-circle',
              message: 'Pagamento confirmado! Créditos adicionados à sua conta.'
            }, {
              type: 'success',
              timer: 5000
            });
          }

          // Recarregar página após 2 segundos
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        }, 2000);
      }
    } catch (error) {
      console.error('Erro ao verificar status:', error);

      // Atualizar indicador em caso de erro
      const statusElement = document.getElementById('paymentStatus');
      if (statusElement) {
        statusElement.className = 'payment-status error';
        statusElement.innerHTML = `
          <i class="fas fa-exclamation-triangle"></i>
          Erro ao verificar status. Tentando novamente...
        `;
      }
    }
  }, 10000); // Verificar a cada 10 segundos

  // Parar verificação após 30 minutos
  setTimeout(() => {
    clearInterval(checkInterval);

    // Atualizar indicador quando expirar
    const statusElement = document.getElementById('paymentStatus');
    if (statusElement) {
      statusElement.className = 'payment-status';
      statusElement.innerHTML = `
        <i class="fas fa-clock"></i>
        Verificação automática encerrada. Recarregue a página para verificar manualmente.
      `;
    }
  }, 30 * 60 * 1000);
}
</script>
