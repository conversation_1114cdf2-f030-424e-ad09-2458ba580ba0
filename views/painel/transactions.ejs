<!-- Transactions Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card modern-card">
      <div class="card-header">
        <div class="header-content">
          <h5 class="card-title">
            <i class="fas fa-history text-primary"></i>
            Histórico de Transações
          </h5>
          <div class="header-actions">
            <span class="transaction-count">
              <i class="fas fa-list"></i> <%= pagination.count %> transações
            </span>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="transactions-summary">
          <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
              <div class="summary-item">
                <div class="summary-icon credit">
                  <i class="fas fa-plus-circle"></i>
                </div>
                <div class="summary-content">
                  <h6 class="summary-value">
                    <%= transactions.filter(t => t.type === 'credit').reduce((sum, t) => sum + t.amount, 0).toFixed(2) %>
                  </h6>
                  <p class="summary-label">Total Créditos</p>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
              <div class="summary-item">
                <div class="summary-icon debit">
                  <i class="fas fa-minus-circle"></i>
                </div>
                <div class="summary-content">
                  <h6 class="summary-value">
                    <%= Math.abs(transactions.filter(t => t.type === 'debit').reduce((sum, t) => sum + t.amount, 0)).toFixed(2) %>
                  </h6>
                  <p class="summary-label">Total Débitos</p>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
              <div class="summary-item">
                <div class="summary-icon balance">
                  <i class="fas fa-wallet"></i>
                </div>
                <div class="summary-content">
                  <h6 class="summary-value"><%= user.saldo.toFixed(2) %></h6>
                  <p class="summary-label">Saldo Atual</p>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
              <div class="summary-item">
                <div class="summary-icon count">
                  <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="summary-content">
                  <h6 class="summary-value"><%= pagination.count %></h6>
                  <p class="summary-label">Total Transações</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Transactions List -->
<div class="row">
  <div class="col-12">
    <div class="card modern-card">
      <div class="card-body">
        <% if (transactions && transactions.length > 0) { %>
          <div class="transactions-list">
            <% transactions.forEach(transaction => { %>
              <div class="transaction-item">
                <div class="transaction-icon">
                  <i class="fas fa-<%= transaction.type === 'credit' ? 'plus-circle text-success' : 'minus-circle text-danger' %>"></i>
                </div>
                <div class="transaction-details">
                  <div class="transaction-main">
                    <span class="transaction-description"><%= transaction.description %></span>
                    <span class="transaction-amount <%= transaction.type === 'credit' ? 'positive' : 'negative' %>">
                      <%= transaction.type === 'credit' ? '+' : '-' %><%= Math.abs(transaction.amount).toFixed(2) %>
                    </span>
                  </div>
                  <div class="transaction-meta">
                    <span class="transaction-checker">
                      <% if (transaction.checker) { %>
                        <i class="fas fa-check-circle"></i> <%= transaction.checker.title %>
                      <% } else { %>
                        <i class="fas fa-cog"></i> Sistema
                      <% } %>
                    </span>
                    <span class="transaction-date">
                      <i class="fas fa-clock"></i> <%= new Date(transaction.createdAt).toLocaleString('pt-BR') %>
                    </span>
                    <% if (transaction.admin) { %>
                    <span class="transaction-admin">
                      <i class="fas fa-user-shield"></i> Por: <%= transaction.admin.usuario %>
                    </span>
                    <% } %>
                  </div>
                  <% if (transaction.balance_before !== null && transaction.balance_after !== null) { %>
                  <div class="transaction-balance">
                    <small class="text-muted">
                      Saldo: <%= transaction.balance_before.toFixed(2) %> → <%= transaction.balance_after.toFixed(2) %>
                    </small>
                  </div>
                  <% } %>
                </div>
                <div class="transaction-status">
                  <span class="status-badge <%= transaction.type === 'credit' ? 'success' : 'danger' %>">
                    <%= transaction.type === 'credit' ? 'Crédito' : 'Débito' %>
                  </span>
                </div>
              </div>
            <% }); %>
          </div>
          
          <!-- Pagination -->
          <% if (pagination.totalPages > 1) { %>
          <div class="pagination-container mt-4">
            <nav aria-label="Navegação de páginas">
              <ul class="pagination justify-content-center">
                <% if (pagination.page > 1) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= pagination.page - 1 %>">
                    <i class="fas fa-chevron-left"></i> Anterior
                  </a>
                </li>
                <% } %>
                
                <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                  <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                </li>
                <% } %>
                
                <% if (pagination.page < pagination.totalPages) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= pagination.page + 1 %>">
                    Próxima <i class="fas fa-chevron-right"></i>
                  </a>
                </li>
                <% } %>
              </ul>
            </nav>
            <div class="pagination-info text-center mt-2">
              <small class="text-muted">
                Página <%= pagination.page %> de <%= pagination.totalPages %> 
                (<%= pagination.count %> transações no total)
              </small>
            </div>
          </div>
          <% } %>
          
        <% } else { %>
          <div class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-receipt"></i>
            </div>
            <h6 class="empty-title">Nenhuma transação encontrada</h6>
            <p class="empty-description">
              Você ainda não possui transações em sua conta. 
              Suas transações aparecerão aqui quando você começar a usar o sistema.
            </p>
            <div class="empty-actions">
              <a href="/painel/credits" class="btn btn-primary">
                <i class="fas fa-plus"></i> Comprar Créditos
              </a>
              <a href="/painel" class="btn btn-secondary ml-2">
                <i class="fas fa-home"></i> Voltar ao Dashboard
              </a>
            </div>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>
