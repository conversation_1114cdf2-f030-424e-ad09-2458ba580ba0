<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-sync text-primary"></i> <b>RENOVAR ACESSO</b></h4>
      </div>
      <div class="card-body">
        <form method="POST" action="/users/update">
          <div class="form-group">
            <label for="usuario">Usuário</label>
            <select id="usuario" name="id" class="form-control" required>
              <option value="">Selecione um usuário</option>
              <% users.forEach(user => { %>
                <option value="<%= user.id %>"><%= user.usuario %> (Saldo atual: <%= user.saldo %>)</option>
              <% }); %>
            </select>
          </div>
          <div class="form-group">
            <label for="saldo">Novo Saldo</label>
            <input type="number" id="saldo" name="saldo" class="form-control" placeholder="Novo saldo" step="0.1" required>
          </div>
          <div class="form-group">
            <button type="submit" class="btn btn-primary btn-block">RENOVAR</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<% extraScripts = `
<script>
  // Fetch users for the dropdown
  $(document).ready(function() {
    $.ajax({
      url: '/api/users',
      type: 'GET',
      success: function(users) {
        const select = $('#usuario');
        select.empty();
        select.append('<option value="">Selecione um usuário</option>');
        
        users.forEach(user => {
          select.append('<option value="' + user.id + '">' + user.usuario + ' (Saldo atual: ' + user.saldo + ')</option>');
        });
      },
      error: function() {
        alert('Erro ao carregar usuários');
      }
    });
  });
</script>
` %>
