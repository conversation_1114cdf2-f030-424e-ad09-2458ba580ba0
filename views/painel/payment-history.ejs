<!-- Payment History Page -->
<div class="payment-history-page">
  <div class="container-fluid px-4">
    <!-- Flash Messages -->
    <% if (typeof success_msg !== 'undefined' && success_msg) { %>
      <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <%= success_msg %>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <% } %>

    <% if (typeof error_msg !== 'undefined' && error_msg) { %>
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <%= error_msg %>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <% } %>

    <!-- Page Header -->
    <div class="page-header mb-4">
      <div class="row align-items-center">
        <div class="col">
          <h1 class="page-title">
            <i class="fas fa-history text-primary me-3"></i>
            Histórico de Pagamentos
          </h1>
          <p class="page-subtitle text-muted">Visualize todas as suas transações e resumo de faturamento</p>
        </div>
        <div class="col-auto">
          <a href="/painel/credits" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            Novo Pagamento
          </a>
        </div>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
      <div class="col-lg-3 col-md-6">
        <div class="summary-card total">
          <div class="summary-icon">
            <i class="fas fa-receipt"></i>
          </div>
          <div class="summary-content">
            <h3 class="summary-value"><%= summary.total_transactions || 0 %></h3>
            <p class="summary-label">Total de Transações</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="summary-card amount">
          <div class="summary-icon">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="summary-content">
            <h3 class="summary-value">R$ <%= (summary.total_amount || 0).toFixed(2) %></h3>
            <p class="summary-label">Total Gasto</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="summary-card credits">
          <div class="summary-icon">
            <i class="fas fa-coins"></i>
          </div>
          <div class="summary-content">
            <h3 class="summary-value"><%= summary.total_credits || 0 %></h3>
            <p class="summary-label">Créditos Adquiridos</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="summary-card balance">
          <div class="summary-icon">
            <i class="fas fa-wallet"></i>
          </div>
          <div class="summary-content">
            <h3 class="summary-value"><%= user.saldo.toFixed(2) %></h3>
            <p class="summary-label">Saldo Atual</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Methods Summary -->
    <% if (paymentMethodSummary && paymentMethodSummary.length > 0) { %>
    <div class="row mb-4">
      <div class="col-12">
        <div class="card modern-card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fas fa-chart-pie me-2"></i>
              Resumo por Método de Pagamento
            </h6>
          </div>
          <div class="card-body">
            <div class="row">
              <% paymentMethodSummary.forEach(method => { %>
              <div class="col-lg-6 col-md-6 mb-3">
                <div class="payment-method-summary">
                  <div class="method-icon <%= method.payment_method %>">
                    <% if (method.payment_method === 'pix') { %>
                      <i class="fab fa-pix"></i>
                    <% } else { %>
                      <i class="fab fa-bitcoin"></i>
                    <% } %>
                  </div>
                  <div class="method-info">
                    <h6 class="method-name">
                      <%= method.payment_method === 'pix' ? 'PIX' : 'Criptomoedas' %>
                    </h6>
                    <p class="method-stats">
                      <%= method.count %> transações • R$ <%= parseFloat(method.total_amount || 0).toFixed(2) %>
                    </p>
                  </div>
                </div>
              </div>
              <% }); %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <% } %>

    <!-- Transactions Table -->
    <div class="row">
      <div class="col-12">
        <div class="card modern-card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fas fa-list me-2"></i>
              Histórico de Transações
            </h6>
          </div>
          <div class="card-body">
            <% if (transactions && transactions.length > 0) { %>
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Data</th>
                      <th>Método</th>
                      <th>Valor</th>
                      <th>Créditos</th>
                      <th>Status</th>
                      <th>Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% transactions.forEach(transaction => { %>
                    <tr>
                      <td>
                        <div class="transaction-date">
                          <strong><%= new Date(transaction.createdAt).toLocaleDateString('pt-BR') %></strong>
                          <small class="text-muted d-block">
                            <%= new Date(transaction.createdAt).toLocaleTimeString('pt-BR') %>
                          </small>
                        </div>
                      </td>
                      <td>
                        <div class="payment-method-badge">
                          <% if (transaction.payment_method === 'pix') { %>
                            <span class="badge badge-pix">
                              <i class="fab fa-pix me-1"></i> PIX
                            </span>
                          <% } else { %>
                            <span class="badge badge-crypto">
                              <i class="fab fa-bitcoin me-1"></i>
                              <%= transaction.crypto_currency || 'Crypto' %>
                            </span>
                          <% } %>
                        </div>
                      </td>
                      <td>
                        <strong class="transaction-amount">
                          R$ <%= parseFloat(transaction.amount).toFixed(2) %>
                        </strong>
                      </td>
                      <td>
                        <span class="credits-amount">
                          <i class="fas fa-coins me-1"></i>
                          <%= transaction.credits %>
                        </span>
                      </td>
                      <td>
                        <% if (transaction.status === 'paid') { %>
                          <span class="status-badge success">
                            <i class="fas fa-check-circle me-1"></i> Pago
                          </span>
                        <% } else if (transaction.status === 'pending') { %>
                          <span class="status-badge warning">
                            <i class="fas fa-clock me-1"></i> Pendente
                          </span>
                        <% } else if (transaction.status === 'expired') { %>
                          <span class="status-badge danger">
                            <i class="fas fa-times-circle me-1"></i> Expirado
                          </span>
                        <% } else { %>
                          <span class="status-badge secondary">
                            <i class="fas fa-question-circle me-1"></i> <%= transaction.status %>
                          </span>
                        <% } %>
                      </td>
                      <td>
                        <div class="action-buttons">
                          <% if (transaction.status === 'pending' && transaction.payment_method === 'pix') { %>
                            <button class="btn btn-sm btn-outline-primary" onclick="showPixDetails('<%= transaction.external_id %>')">
                              <i class="fas fa-qrcode me-1"></i> Ver QR
                            </button>
                          <% } %>
                          <button class="btn btn-sm btn-outline-secondary" onclick="showTransactionDetails('<%= transaction.id %>')">
                            <i class="fas fa-eye me-1"></i> Detalhes
                          </button>
                        </div>
                      </td>
                    </tr>
                    <% }); %>
                  </tbody>
                </table>
              </div>

              <!-- Pagination -->
              <% if (pagination.totalPages > 1) { %>
              <nav aria-label="Navegação de páginas" class="mt-4">
                <ul class="pagination justify-content-center">
                  <% if (pagination.hasPrev) { %>
                    <li class="page-item">
                      <a class="page-link" href="?page=<%= pagination.page - 1 %>">
                        <i class="fas fa-chevron-left"></i> Anterior
                      </a>
                    </li>
                  <% } %>

                  <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                    <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                      <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                    </li>
                  <% } %>

                  <% if (pagination.hasNext) { %>
                    <li class="page-item">
                      <a class="page-link" href="?page=<%= pagination.page + 1 %>">
                        Próxima <i class="fas fa-chevron-right"></i>
                      </a>
                    </li>
                  <% } %>
                </ul>
              </nav>
              <% } %>
            <% } else { %>
              <div class="empty-state">
                <div class="empty-icon">
                  <i class="fas fa-receipt"></i>
                </div>
                <h6 class="empty-title">Nenhuma transação encontrada</h6>
                <p class="empty-description">
                  Você ainda não realizou nenhum pagamento.
                  <a href="/painel/credits" class="text-primary">Clique aqui para comprar créditos</a>.
                </p>
              </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Custom Styles for Payment History Page -->
<style>
/* =========================================================
 * Payment History Page Styles
 ========================================================= */
.payment-history-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 1.6rem 0;
}

.page-header {
  margin-bottom: 1.6rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.4rem;
}

.page-subtitle {
  font-size: 0.9rem;
  margin-bottom: 0;
}

/* =========================================================
 * Summary Cards
 ========================================================= */
.summary-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
}

.summary-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.summary-card.total .summary-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.summary-card.amount .summary-icon {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.summary-card.credits .summary-icon {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
}

.summary-card.balance .summary-icon {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.summary-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.summary-label {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

/* =========================================================
 * Payment Method Summary
 ========================================================= */
.payment-method-summary {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.method-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.method-icon.pix {
  background: linear-gradient(135deg, #32bcad 0%, #1e7e34 100%);
}

.method-icon.crypto {
  background: linear-gradient(135deg, #f7931a 0%, #ff6b35 100%);
}

.method-name {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.method-stats {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

/* =========================================================
 * Modern Card Styles
 ========================================================= */
.modern-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modern-card .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 1.2rem 1.6rem;
  color: white;
}

.modern-card .card-body {
  padding: 1.6rem;
}

/* =========================================================
 * Table Styles
 ========================================================= */
.table {
  margin-bottom: 0;
}

.table th {
  border-top: none;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 1rem 0.75rem;
}

.table td {
  border-top: 1px solid #f1f3f4;
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

.transaction-date strong {
  color: #2c3e50;
}

.transaction-amount {
  color: #28a745;
  font-size: 1rem;
}

.credits-amount {
  color: #ffc107;
  font-weight: 500;
}

/* =========================================================
 * Badges
 ========================================================= */
.badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.badge-pix {
  background: linear-gradient(135deg, #32bcad 0%, #1e7e34 100%);
  color: white;
}

.badge-crypto {
  background: linear-gradient(135deg, #f7931a 0%, #ff6b35 100%);
  color: white;
}

.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.status-badge.success {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-badge.warning {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-badge.danger {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-badge.secondary {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

/* =========================================================
 * Action Buttons
 ========================================================= */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 6px;
}

/* =========================================================
 * Empty State
 ========================================================= */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.empty-title {
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.empty-description {
  font-size: 0.875rem;
}

/* =========================================================
 * Pagination
 ========================================================= */
.pagination {
  margin-bottom: 0;
}

.page-link {
  border: 1px solid #dee2e6;
  color: #667eea;
  padding: 0.5rem 0.75rem;
}

.page-link:hover {
  background-color: #667eea;
  border-color: #667eea;
  color: white;
}

.page-item.active .page-link {
  background-color: #667eea;
  border-color: #667eea;
}

/* =========================================================
 * Responsive Design
 ========================================================= */
@media (max-width: 768px) {
  .page-title {
    font-size: 1.6rem;
  }

  .summary-card {
    flex-direction: column;
    text-align: center;
  }

  .payment-method-summary {
    flex-direction: column;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

@media (max-width: 576px) {
  .payment-history-page {
    padding: 0.8rem 0;
  }

  .container-fluid {
    padding-left: 0.8rem;
    padding-right: 0.8rem;
  }

  .summary-card {
    padding: 1rem;
  }

  .summary-icon {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }

  .summary-value {
    font-size: 1.5rem;
  }
}
</style>

<script>
// Função para mostrar detalhes do PIX
function showPixDetails(externalId) {
  // Implementar modal para mostrar QR code do PIX pendente
  console.log('Mostrar detalhes PIX:', externalId);
}

// Função para mostrar detalhes da transação
function showTransactionDetails(transactionId) {
  // Implementar modal para mostrar detalhes completos da transação
  console.log('Mostrar detalhes da transação:', transactionId);
}
</script>
