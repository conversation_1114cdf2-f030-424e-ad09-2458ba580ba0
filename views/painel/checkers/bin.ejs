<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center"><i class="fa fa-credit-card text-primary"></i> <b>BIN CHECKER</b></h4>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="lista">LISTA DE BINS</label>
              <textarea id="lista" class="form-control form-checker" rows="10" placeholder="Insira as BINs aqui (uma por linha)"></textarea>
            </div>
            <div class="form-group">
              <button id="btnCheck" class="btn btn-primary btn-block btn-play">VERIFICAR</button>
              <button id="btnStop" class="btn btn-danger btn-block btn-stop" disabled>PARAR</button>
            </div>
            <div class="form-group">
              <div class="alert alert-info">
                <p><b>SALDO:</b> <%= user.saldo %></p>
                <p><b>CARREGADAS:</b> <span class="carregadas">0</span></p>
                <p><b>TESTADAS:</b> <span class="testadas">0</span></p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="resultado">RESULTADO</label>
              <div id="resultado" class="form-control" style="height: 400px; overflow-y: auto; background-color: #1e1e2f; color: white;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<% extraScripts = `
<script src="/js/vendor/sweetalert2.min.js"></script>
<script>
  $(document).ready(function() {
    $('.btn-play').click(function() {
      var lista = $('.form-checker').val().trim();
      var array = lista.split('\\n');
      var lives = 0,
      dies = 0,
      testadas = 0,
      txt = '';

      if (!lista) {
        Swal.fire({
          title: 'Erro: Lista Vazia!', icon: 'error', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
        });
        return false;
      }

      Swal.fire({
        title: 'Teste Iniciado!', icon: 'success', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
      });

      var line = array.filter(function(value) {
        if (value.trim() !== "") {
          txt += value.trim() + '\\n';
          return value.trim();
        }
      });

      var total = line.length;

      $('.form-checker').val(txt.trim());

      if (total > 1200) {
        Swal.fire({
          title: 'Limite de Linhas Excedido!', icon: 'warning', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
        });
        return false;
      }

      $('.carregadas').text(total);
      $('.btn-play').attr('disabled', true);
      $('.btn-stop').attr('disabled', false);

      line.forEach(function(value, index) {
        setTimeout(
          function() {
            $.ajax({
              url: '/api/checkers/bin?lista=' + value,
              type: 'GET',
              success: function(resultado) {
                testadas++;
                $('.testadas').text(testadas);
                $('#resultado').append(resultado + '<br>');
                $('#resultado').scrollTop($('#resultado')[0].scrollHeight);
              },
              error: function() {
                testadas++;
                $('.testadas').text(testadas);
                $('#resultado').append('<font color="red">ERRO AO VERIFICAR BIN: ' + value + '</font><br>');
                $('#resultado').scrollTop($('#resultado')[0].scrollHeight);
              }
            });
          },
          1000 * index
        );
      });
    });

    $('.btn-stop').click(function() {
      Swal.fire({
        title: 'Teste Interrompido!', icon: 'info', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
      });

      $('.btn-play').attr('disabled', false);
      $('.btn-stop').attr('disabled', true);

      // This doesn't actually stop AJAX requests in progress, but prevents new ones
      // For a real implementation, you'd need to track and abort the requests
    });
  });
</script>
` %>
