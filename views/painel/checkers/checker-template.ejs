<div class="col-md-11 mt-4" style="margin: auto;">
  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title text-center"><i class="<%= checker.icon %> text-primary"></i> <b><%= checkerTitle %></b></h4>
          <% if (checker.description) { %>
            <p class="text-center"><%= checker.description %></p>
          <% } %>
          <div class="alert alert-info text-center">
            <p><b>Preço:</b> <%= price %> crédito(s) por <%= checker.charge_type === 'per_test' ? 'teste' : 'aprovação' %></p>
            <p><b>Saldo atual:</b> <%= user.saldo %> crédito(s)</p>
          </div>
        </div>
        <div class="card-body text-center">
          <textarea style="height: 8.06rem;" class="form-control text-center form-checker mb-2" placeholder="Insira sua Lista (1200)"></textarea>
          <button class="btn btn-success btn-play text-white" style="width: 49%; float: left;"><i class="fa fa-play"></i> INICIAR</button>
          <button class="btn btn-danger btn-stop text-white" style="width: 49%; float: right;" disabled><i class="fa fa-stop"></i> PARAR</button>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title text-center"><i class="fa fa-chart-bar text-primary"></i> <b>ESTATÍSTICAS</b></h4>
        </div>
        <div class="card-body">
          <h5 class="title"><i class="fa fa-check"></i> Aprovadas:<span class="badge badge-success float-right aprovadas">0</span></h5><hr>
          <h5 class="title"><i class="fa fa-times"></i> Reprovadas:<span class="badge badge-danger float-right reprovadas">0</span></h5><hr>
          <h5 class="title"><i class="fa fa-sync-alt"></i> Testadas:<span class="badge badge-info float-right testadas">0</span></h5><hr>
          <h5 class="title mb-0"><i class="fa fa-share-square"></i> Carregadas:<span class="badge badge-primary float-right carregadas">0</span></h5><hr>
          <h5 class="title mb-0"><i class="fa fa-credit-card"></i> Créditos Gastos:<span class="badge badge-warning float-right creditos">0</span></h5>
        </div>
      </div>
    </div>
    <div class="col-xl-12">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title"><i class="fa fa-check text-success"></i> APROVADAS</h4>
          <div class="float-right">
            <button type="show" class="btn btn-primary btn-sm show-lives"><i class="fa fa-eye-slash"></i></button>
            <button class="btn btn-success btn-sm btn-copy"><i class="fa fa-copy"></i></button>
          </div>
        </div>
        <div class="card-body">
          <div id='lista_aprovadas'></div>
        </div>
      </div>
    </div>
    <div class="col-xl-12">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title"><i class="fa fa-times text-danger"></i> REPROVADAS</h4>
          <div class="float-right">
            <button type='hidden' class="btn btn-primary btn-sm show-dies"><i class="fa fa-eye-slash"></i></button>
            <button class="btn btn-danger btn-sm btn-trash"><i class="fa fa-trash"></i></button>
          </div>
        </div>
        <div class="card-body">
          <div id='lista_reprovadas'></div>
        </div>
      </div>
    </div>
  </div>
</div>

<% extraScripts = `
<script src="/js/vendor/sweetalert2.min.js"></script>
<script>
  var audio = new Audio('/audio/notification.mp3');

  $(document).ready(function() {
    $('.show-lives').click(function() {
      var type = $('.show-lives').attr('type');
      $('#lista_aprovadas').slideToggle();
      if (type == 'show') {
        $('.show-lives').html('<i class="fa fa-eye"></i>');
        $('.show-lives').attr('type', 'hidden');
      } else {
        $('.show-lives').html('<i class="fa fa-eye-slash"></i>');
        $('.show-lives').attr('type', 'show');
      }
    });

    $('.show-dies').click(function() {
      var type = $('.show-dies').attr('type');
      $('#lista_reprovadas').slideToggle();
      if (type == 'show') {
        $('.show-dies').html('<i class="fa fa-eye"></i>');
        $('.show-dies').attr('type', 'hidden');
      } else {
        $('.show-dies').html('<i class="fa fa-eye-slash"></i>');
        $('.show-dies').attr('type', 'show');
      }
    });

    $('.btn-trash').click(function() {
      Swal.fire({
        title: 'Lista de Reprovadas Limpa!', icon: 'success', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
      });
      $('#lista_reprovadas').text('');
    });

    $('.btn-copy').click(function() {
      Swal.fire({
        title: 'Lista de Aprovadas Copiada!', icon: 'success', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
      });
      var lista_lives = document.getElementById('lista_aprovadas').innerText;
      var textarea = document.createElement("textarea");
      textarea.value = lista_lives;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy'); document.body.removeChild(textarea);
    });

    $('.btn-play').click(function() {
      var lista = $('.form-checker').val().trim();
      var array = lista.split('\\n');
      var lives = 0,
      dies = 0,
      testadas = 0,
      txt = '';

      if (!lista) {
        Swal.fire({
          title: 'Erro: Lista Vazia!', icon: 'error', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
        });
        return false;
      }

      Swal.fire({
        title: 'Teste Iniciado!', icon: 'success', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
      });

      var line = array.filter(function(value) {
        if (value.trim() !== "") {
          txt += value.trim() + '\\n';
          return value.trim();
        }
      });

      var total = line.length;

      $('.form-checker').val(txt.trim());

      if (total > 1200) {
        Swal.fire({
          title: 'Limite de Linhas Excedido!', icon: 'warning', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
        });
        return false;
      }

      $('.carregadas').text(total);
      $('.btn-play').attr('disabled', true);
      $('.btn-stop').attr('disabled', false);

      line.forEach(function(data) {
        var callBack = $.ajax({
          url: '/api/dynamic/${checker.endpoint}?lista=' + data,
          success: function(retorno) {
            if (retorno.indexOf("[LIVE]") >= 0) {
              audio.play();
              Swal.fire({
                title: '+1 Aprovada!', icon: 'success', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
              });
              $('#lista_aprovadas').append(retorno + '<br>');
              removelinha();
              lives = lives + 1;

              // Atualizar créditos gastos (apenas se cobrar por aprovação)
              if ('${checker.charge_type}' === 'per_live') {
                var creditos = parseInt($('.creditos').text()) + ${price};
                $('.creditos').text(creditos);
              }
            } else if (retorno.indexOf("[DIE]") >= 0) {
              $('#lista_reprovadas').append(retorno + '<br>');
              removelinha();
              dies = dies + 1;
            } else {
              // Fallback para respostas que não seguem o padrão
              $('#lista_reprovadas').append("Resposta inválida: " + retorno + '<br>');
              removelinha();
              dies = dies + 1;
            }

            testadas = lives + dies;
            $('.aprovadas').text(lives);
            $('.reprovadas').text(dies);
            $('.testadas').text(testadas);

            // Atualizar créditos gastos (se cobrar por teste)
            if ('${checker.charge_type}' === 'per_test') {
              var creditos = parseInt($('.creditos').text()) + ${price};
              $('.creditos').text(creditos);
            }

            if (testadas == total) {
              Swal.fire({
                title: 'Teste Finalizado!', icon: 'success', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
              });
              $('.btn-play').attr('disabled', false);
              $('.btn-stop').attr('disabled', true);
            }
          }
        });

        $('.btn-stop').click(function() {
          Swal.fire({
            title: 'Teste Parado!', icon: 'warning', showConfirmButton: false, toast: true, position: 'top-end', timer: 3000
          });
          $('.btn-play').attr('disabled', false);
          $('.btn-stop').attr('disabled', true);
          callBack.abort();
          return false;
        });
      });
    });
  });

  function removelinha() {
    var lines = $('.form-checker').val().split('\\n');
    lines.splice(0, 1);
    $('.form-checker').val(lines.join("\\n"));
  }

  // Aviso inicial - apenas quando o usuário clica no botão de iniciar
  $('.btn-play').on('click', function() {
    // Verificar se há texto na textarea antes de mostrar o aviso
    if ($('.form-checker').val().trim() !== '') {
      Swal.fire({
        title: 'Aviso!',
        html: 'Cada ${checker.charge_type === "per_test" ? "teste" : "aprovação"} custará ${price} crédito(s)',
        timer: 2000,
        timerProgressBar: true,
        didOpen: () => {
          Swal.showLoading()
        }
      });
    }
  });
</script>
` %>
