<!-- Modern Profile Page -->
<div class="profile-page">
  <div class="container-fluid px-4">
    <!-- Flash Messages -->
    <% if (typeof success_msg !== 'undefined' && success_msg) { %>
      <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <%= success_msg %>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <% } %>

    <% if (typeof error_msg !== 'undefined' && error_msg) { %>
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <%= error_msg %>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <% } %>

    <!-- Page Header -->
    <div class="page-header mb-4">
      <div class="row align-items-center">
        <div class="col">
          <h1 class="page-title">
            <i class="fas fa-user-circle text-primary me-3"></i>
            Meu Perfil
          </h1>
          <p class="page-subtitle text-muted">Gerencie suas informações pessoais e configurações</p>
        </div>
      </div>
    </div>

    <div class="row g-4">
      <!-- Profile Information Card -->
      <div class="col-lg-4">
        <div class="card modern-card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fas fa-id-card me-2"></i>
              Informações do Perfil
            </h6>
          </div>
          <div class="card-body text-center">
            <div class="profile-avatar mb-3">
              <img src="https://www.gravatar.com/avatar/<%= user.email ? require('crypto').createHash('md5').update(user.email.toLowerCase()).digest('hex') : '' %>?s=120&d=mp"
                   alt="<%= user.usuario %>" class="avatar-img">
              <div class="avatar-status online"></div>
            </div>

            <h5 class="profile-name"><%= user.usuario %></h5>

            <div class="profile-role mb-3">
              <% if (user.rank === 1 || user.role === 'admin') { %>
                <span class="badge badge-admin">
                  <i class="fas fa-crown me-1"></i>
                  Administrador
                </span>
              <% } else if (user.role === 'programmer') { %>
                <span class="badge badge-programmer">
                  <i class="fas fa-code me-1"></i>
                  Programador
                </span>
              <% } else if (user.role === 'affiliate') { %>
                <span class="badge badge-affiliate">
                  <i class="fas fa-users me-1"></i>
                  Afiliado
                </span>
              <% } else if (user.role === 'vip') { %>
                <span class="badge badge-vip">
                  <i class="fas fa-star me-1"></i>
                  VIP
                </span>
              <% } else if (user.role === 'premium') { %>
                <span class="badge badge-premium">
                  <i class="fas fa-gem me-1"></i>
                  Premium
                </span>
              <% } else { %>
                <span class="badge badge-user">
                  <i class="fas fa-user me-1"></i>
                  Usuário
                </span>
              <% } %>
            </div>

            <div class="profile-stats">
              <div class="stat-item">
                <div class="stat-value">R$ <%= user.saldo.toFixed(2) %></div>
                <div class="stat-label">Saldo Atual</div>
              </div>
              <div class="stat-divider"></div>
              <div class="stat-item">
                <div class="stat-value"><%= transactionSummary.total_transactions || 0 %></div>
                <div class="stat-label">Transações</div>
              </div>
              <% if (earnings) { %>
                <div class="stat-divider"></div>
                <div class="stat-item">
                  <div class="stat-value">R$ <%= (earnings.total_amount || 0).toFixed(2) %></div>
                  <div class="stat-label">Ganhos</div>
                </div>
              <% } %>
            </div>
          </div>
        </div>
      </div>

      <!-- Edit Profile Form -->
      <div class="col-lg-8">
        <div class="card modern-card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fas fa-edit me-2"></i>
              Editar Informações
            </h6>
          </div>
          <div class="card-body">
            <form method="POST" action="/painel/profile/update">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group mb-3">
                    <label for="usuario" class="form-label">
                      <i class="fas fa-user me-2"></i>
                      Nome de Usuário
                    </label>
                    <input type="text" class="form-control" id="usuario" value="<%= user.usuario %>" readonly>
                    <small class="form-text text-muted">O nome de usuário não pode ser alterado</small>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-group mb-3">
                    <label for="email" class="form-label">
                      <i class="fas fa-envelope me-2"></i>
                      Email
                    </label>
                    <input type="email" class="form-control" id="email" name="email"
                           value="<%= user.email || '' %>" placeholder="<EMAIL>">
                  </div>
                </div>
              </div>

              <hr class="my-4">

              <h6 class="mb-3">
                <i class="fas fa-lock me-2"></i>
                Alterar Senha
              </h6>

              <div class="row">
                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label for="current_password" class="form-label">Senha Atual</label>
                    <input type="password" class="form-control" id="current_password" name="current_password">
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label for="new_password" class="form-label">Nova Senha</label>
                    <input type="password" class="form-control" id="new_password" name="new_password">
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label for="confirm_password" class="form-label">Confirmar Nova Senha</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                  </div>
                </div>
              </div>

              <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save me-2"></i>
                  Salvar Alterações
                </button>
                <a href="/painel" class="btn btn-secondary ms-2">
                  <i class="fas fa-arrow-left me-2"></i>
                  Voltar ao Dashboard
                </a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card modern-card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fas fa-history me-2"></i>
              Atividade Recente
            </h6>
          </div>
          <div class="card-body">
            <% if (recentActivity && recentActivity.length > 0) { %>
              <div class="activity-list">
                <% recentActivity.forEach(activity => { %>
                  <div class="activity-item">
                    <div class="activity-icon">
                      <i class="fas fa-circle"></i>
                    </div>
                    <div class="activity-content">
                      <div class="activity-description"><%= activity.description %></div>
                      <div class="activity-time">
                        <%= new Date(activity.created_at).toLocaleString('pt-BR') %>
                      </div>
                    </div>
                  </div>
                <% }); %>
              </div>
            <% } else { %>
              <div class="empty-state">
                <div class="empty-icon">
                  <i class="fas fa-history"></i>
                </div>
                <h6 class="empty-title">Nenhuma atividade recente</h6>
                <p class="empty-description">Suas atividades aparecerão aqui conforme você usar o sistema.</p>
              </div>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Custom Styles for Profile Page -->
<style>
/* =========================================================
 * Modern Profile Page Styles
 ========================================================= */
.profile-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 1.6rem 0;
}

.page-header {
  margin-bottom: 1.6rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.4rem;
}

.page-subtitle {
  font-size: 0.9rem;
  margin-bottom: 0;
}

/* =========================================================
 * Modern Card Styles
 ========================================================= */
.modern-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modern-card .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 1.2rem 1.6rem;
  color: white;
}

.modern-card .card-body {
  padding: 1.6rem;
}

/* =========================================================
 * Profile Avatar & Info
 ========================================================= */
.profile-avatar {
  position: relative;
  display: inline-block;
}

.avatar-img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid #667eea;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.avatar-status {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 3px solid white;
}

.avatar-status.online {
  background: #28a745;
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

/* =========================================================
 * Badges
 ========================================================= */
.badge {
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.badge-admin {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.badge-programmer {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.badge-affiliate {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.badge-vip {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
  color: white;
}

.badge-premium {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
}

.badge-user {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
}

/* =========================================================
 * Profile Stats
 ========================================================= */
.profile-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-label {
  font-size: 0.75rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: #dee2e6;
}

/* =========================================================
 * Form Styles
 ========================================================= */
.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-actions {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

/* =========================================================
 * Activity List
 ========================================================= */
.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.activity-description {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.75rem;
  color: #6c757d;
}

/* =========================================================
 * Empty State
 ========================================================= */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.empty-title {
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.empty-description {
  font-size: 0.875rem;
}

/* =========================================================
 * Responsive Design
 ========================================================= */
@media (max-width: 768px) {
  .page-title {
    font-size: 1.6rem;
  }

  .profile-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .stat-divider {
    width: 40px;
    height: 1px;
  }

  .modern-card .card-body {
    padding: 1.2rem;
  }
}

@media (max-width: 576px) {
  .profile-page {
    padding: 0.8rem 0;
  }

  .container-fluid {
    padding-left: 0.8rem;
    padding-right: 0.8rem;
  }

  .avatar-img {
    width: 100px;
    height: 100px;
  }

  .profile-name {
    font-size: 1.25rem;
  }
}
</style>
