<!-- Modern Earnings Page -->
<div class="earnings-page">
  <div class="container-fluid px-4">
    <!-- Page Header -->
    <div class="page-header mb-4">
      <div class="row align-items-center">
        <div class="col">
          <h1 class="page-title">
            <i class="fas fa-chart-line text-primary me-3"></i>
            Meus Ganhos
          </h1>
          <p class="page-subtitle text-muted">Acompanhe seus rendimentos e comissões</p>
        </div>
        <div class="col-auto">
          <div class="page-actions">
            <button class="btn btn-outline-primary btn-sm" onclick="window.print()">
              <i class="fas fa-print me-2"></i>Imprimir Relatório
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Earnings Summary Cards -->
    <div class="earnings-summary mb-5">
      <div class="row g-4">
        <!-- Total Earnings Card -->
        <div class="col-lg-4 col-md-6">
          <div class="earnings-card total-earnings">
            <div class="card-gradient"></div>
            <div class="card-content">
              <div class="card-icon">
                <i class="fas fa-coins"></i>
              </div>
              <div class="card-info">
                <h3 class="card-title">Total de Ganhos</h3>
                <div class="card-value">R$ <%= totalEarnings.toFixed(2) %></div>
                <div class="card-subtitle">Receita total acumulada</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Programmer Earnings Card -->
        <div class="col-lg-4 col-md-6">
          <div class="earnings-card programmer-earnings">
            <div class="card-gradient"></div>
            <div class="card-content">
              <div class="card-icon">
                <i class="fas fa-code"></i>
              </div>
              <div class="card-info">
                <h3 class="card-title">Desenvolvimento</h3>
                <div class="card-value">R$ <%= totalProgrammerEarnings.toFixed(2) %></div>
                <div class="card-subtitle">Ganhos como programador</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Affiliate Earnings Card -->
        <div class="col-lg-4 col-md-6">
          <div class="earnings-card affiliate-earnings">
            <div class="card-gradient"></div>
            <div class="card-content">
              <div class="card-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="card-info">
                <h3 class="card-title">Afiliação</h3>
                <div class="card-value">R$ <%= totalAffiliateEarnings.toFixed(2) %></div>
                <div class="card-subtitle">Comissões de referência</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Earnings Details Section -->
    <div class="earnings-details">
      <!-- Modern Tabs -->
      <div class="modern-tabs mb-3">
        <ul class="nav nav-pills nav-fill" id="earningsTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
              <i class="fas fa-chart-bar me-2"></i>
              Todos os Ganhos
              <span class="badge bg-light text-dark ms-2"><%= earnings.length %></span>
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="programmer-tab" data-bs-toggle="pill" data-bs-target="#programmer" type="button" role="tab" aria-controls="programmer" aria-selected="false">
              <i class="fas fa-code me-2"></i>
              Desenvolvimento
              <span class="badge bg-light text-dark ms-2"><%= programmerEarnings.length %></span>
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="affiliate-tab" data-bs-toggle="pill" data-bs-target="#affiliate" type="button" role="tab" aria-controls="affiliate" aria-selected="false">
              <i class="fas fa-users me-2"></i>
              Afiliação
              <span class="badge bg-light text-dark ms-2"><%= affiliateEarnings.length %></span>
            </button>
          </li>
        </ul>
      </div>
      <!-- Tab Content -->
      <div class="tab-content" id="earningsTabContent">
        <!-- All Earnings Tab -->
        <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
          <div class="card modern-card">
            <div class="card-header">
              <h6 class="card-title mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                Todos os Ganhos
              </h6>
            </div>
            <div class="card-body">
              <% if (earnings.length === 0) { %>
                <div class="empty-state">
                  <div class="empty-icon">
                    <i class="fas fa-chart-line"></i>
                  </div>
                  <h5>Nenhum ganho registrado</h5>
                  <p class="text-muted">Seus ganhos aparecerão aqui quando você começar a receber comissões.</p>
                </div>
              <% } else { %>
                  <div class="table-responsive">
                    <table class="table modern-table">
                      <thead>
                        <tr>
                          <th>
                            <i class="fas fa-calendar-alt me-2"></i>
                            Data & Hora
                          </th>
                          <th>
                            <i class="fas fa-tag me-2"></i>
                            Tipo de Ganho
                          </th>
                          <th>
                            <i class="fas fa-tools me-2"></i>
                            Ferramenta
                          </th>
                          <th class="text-end">
                            <i class="fas fa-dollar-sign me-2"></i>
                            Valor
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <% earnings.forEach(earning => { %>
                          <tr class="table-row">
                            <td>
                              <div class="date-info">
                                <div class="date-primary">
                                  <%= new Date(earning.created_at).toLocaleDateString('pt-BR') %>
                                </div>
                                <div class="date-secondary">
                                  <%= new Date(earning.created_at).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }) %>
                                </div>
                              </div>
                            </td>
                            <td>
                              <% if (earning.source_type === 'programmer') { %>
                                <span class="badge-modern badge-programmer">
                                  <i class="fas fa-code me-1"></i>
                                  Desenvolvimento
                                </span>
                              <% } else if (earning.source_type === 'affiliate') { %>
                                <span class="badge-modern badge-affiliate">
                                  <i class="fas fa-users me-1"></i>
                                  Afiliação
                                </span>
                              <% } else { %>
                                <span class="badge-modern badge-site">
                                  <i class="fas fa-globe me-1"></i>
                                  Site
                                </span>
                              <% } %>
                            </td>
                            <td>
                              <div class="checker-info">
                                <span class="checker-name">
                                  <%= earning.checker ? earning.checker.title : 'Sistema' %>
                                </span>
                              </div>
                            </td>
                            <td class="text-end">
                              <div class="amount-info">
                                <span class="amount-value">R$ <%= earning.amount.toFixed(2) %></span>
                              </div>
                            </td>
                          </tr>
                        <% }); %>
                      </tbody>
                    </table>
                </div>
              <% } %>
            </div>
          </div>
        </div>

        <!-- Programmer Earnings Tab -->
        <div class="tab-pane fade" id="programmer" role="tabpanel" aria-labelledby="programmer-tab">
          <div class="card modern-card">
            <div class="card-header">
              <h6 class="card-title mb-0">
                <i class="fas fa-code me-2"></i>
                Ganhos de Desenvolvimento
              </h6>
            </div>
            <div class="card-body">
              <% if (programmerEarnings.length === 0) { %>
                <div class="empty-state">
                  <div class="empty-icon">
                    <i class="fas fa-code"></i>
                  </div>
                  <h5>Nenhum ganho como desenvolvedor</h5>
                  <p class="text-muted">Seus ganhos de desenvolvimento aparecerão aqui quando suas ferramentas forem utilizadas.</p>
                </div>
              <% } else { %>
                  <div class="table-responsive">
                    <table class="table modern-table">
                      <thead>
                        <tr>
                          <th>
                            <i class="fas fa-calendar-alt me-2"></i>
                            Data & Hora
                          </th>
                          <th>
                            <i class="fas fa-tools me-2"></i>
                            Ferramenta Desenvolvida
                          </th>
                          <th class="text-end">
                            <i class="fas fa-dollar-sign me-2"></i>
                            Comissão
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <% programmerEarnings.forEach(earning => { %>
                          <tr class="table-row">
                            <td>
                              <div class="date-info">
                                <div class="date-primary">
                                  <%= new Date(earning.created_at).toLocaleDateString('pt-BR') %>
                                </div>
                                <div class="date-secondary">
                                  <%= new Date(earning.created_at).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }) %>
                                </div>
                              </div>
                            </td>
                            <td>
                              <div class="checker-info">
                                <span class="checker-name">
                                  <i class="fas fa-cog me-2 text-primary"></i>
                                  <%= earning.checker ? earning.checker.title : 'Sistema' %>
                                </span>
                              </div>
                            </td>
                            <td class="text-end">
                              <div class="amount-info">
                                <span class="amount-value text-success">R$ <%= earning.amount.toFixed(2) %></span>
                              </div>
                            </td>
                          </tr>
                        <% }); %>
                      </tbody>
                    </table>
                </div>
              <% } %>
            </div>
          </div>
        </div>

        <!-- Affiliate Earnings Tab -->
        <div class="tab-pane fade" id="affiliate" role="tabpanel" aria-labelledby="affiliate-tab">
          <div class="card modern-card">
            <div class="card-header">
              <h6 class="card-title mb-0">
                <i class="fas fa-users me-2"></i>
                Comissões de Afiliação
              </h6>
            </div>
            <div class="card-body">
              <% if (affiliateEarnings.length === 0) { %>
                <div class="empty-state">
                  <div class="empty-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <h5>Nenhuma comissão de afiliação</h5>
                  <p class="text-muted">Suas comissões de referência aparecerão aqui quando seus indicados utilizarem a plataforma.</p>
                  <a href="/earnings/referrals" class="btn btn-primary btn-sm mt-3">
                    <i class="fas fa-link me-2"></i>
                    Ver Programa de Afiliados
                  </a>
                </div>
              <% } else { %>
                  <div class="table-responsive">
                    <table class="table modern-table">
                      <thead>
                        <tr>
                          <th>
                            <i class="fas fa-calendar-alt me-2"></i>
                            Data & Hora
                          </th>
                          <th>
                            <i class="fas fa-tools me-2"></i>
                            Ferramenta Utilizada
                          </th>
                          <th class="text-end">
                            <i class="fas fa-percentage me-2"></i>
                            Comissão
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <% affiliateEarnings.forEach(earning => { %>
                          <tr class="table-row">
                            <td>
                              <div class="date-info">
                                <div class="date-primary">
                                  <%= new Date(earning.created_at).toLocaleDateString('pt-BR') %>
                                </div>
                                <div class="date-secondary">
                                  <%= new Date(earning.created_at).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }) %>
                                </div>
                              </div>
                            </td>
                            <td>
                              <div class="checker-info">
                                <span class="checker-name">
                                  <i class="fas fa-user-friends me-2 text-warning"></i>
                                  <%= earning.checker ? earning.checker.title : 'Sistema' %>
                                </span>
                              </div>
                            </td>
                            <td class="text-end">
                              <div class="amount-info">
                                <span class="amount-value text-warning">R$ <%= earning.amount.toFixed(2) %></span>
                              </div>
                            </td>
                          </tr>
                        <% }); %>
                      </tbody>
                    </table>
                </div>
              <% } %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Custom Styles for Modern Earnings Page -->
<style>
/* =========================================================
 * Modern Earnings Page Styles
 ========================================================= */
.earnings-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 1.6rem 0;
}

.page-header {
  margin-bottom: 1.6rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.4rem;
}

.page-subtitle {
  font-size: 0.9rem;
  margin-bottom: 0;
}

.page-actions .btn {
  border-radius: 20px;
  padding: 0.4rem 1.2rem;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.page-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* =========================================================
 * Earnings Summary Cards
 ========================================================= */
.earnings-card {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 1.6rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
}

.earnings-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15);
}

.card-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  border-radius: 16px 16px 0 0;
}

.total-earnings .card-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.programmer-earnings .card-gradient {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.affiliate-earnings .card-gradient {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  flex-shrink: 0;
}

.total-earnings .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.programmer-earnings .card-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.affiliate-earnings .card-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6c757d;
  margin-bottom: 0.4rem;
}

.card-value {
  font-size: 1.6rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.card-subtitle {
  font-size: 0.7rem;
  color: #6c757d;
}

/* =========================================================
 * Modern Card & Tabs
 ========================================================= */
.modern-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modern-card .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 1.2rem 1.6rem;
  color: white;
}

.modern-card .card-body {
  padding: 1.6rem;
}

.modern-tabs .nav-pills .nav-link {
  border-radius: 12px;
  padding: 0.8rem 1.2rem;
  font-weight: 500;
  color: #6c757d;
  background: #f8f9fa;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  margin: 0 0.2rem;
  font-size: 0.8rem;
}

.modern-tabs .nav-pills .nav-link:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.modern-tabs .nav-pills .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.modern-tabs .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* =========================================================
 * Modern Table Styles
 ========================================================= */
.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.modern-table {
  margin-bottom: 0;
}

.modern-table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  padding: 1rem 0.8rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.4px;
}

.modern-table tbody .table-row {
  border: none;
  transition: all 0.3s ease;
}

.modern-table tbody .table-row:hover {
  background: #f8f9fa;
  transform: scale(1.005);
}

.modern-table tbody td {
  padding: 1rem 0.8rem;
  border: none;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
}

.date-info .date-primary {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.7rem;
}

.date-info .date-secondary {
  font-size: 0.6rem;
  color: #6c757d;
  margin-top: 0.1rem;
}

.badge-modern {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.6rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
}

.badge-programmer {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.badge-affiliate {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.badge-site {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #495057;
}

.checker-name {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.8rem;
}

.amount-value {
  font-size: 0.9rem;
  font-weight: 700;
  color: #28a745;
}

/* =========================================================
 * Empty State
 ========================================================= */
.empty-state {
  text-align: center;
  padding: 3.2rem 1.6rem;
  color: #6c757d;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;
  color: white;
}

.empty-state h5 {
  color: #495057;
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

/* =========================================================
 * Responsive Design
 ========================================================= */
@media (max-width: 768px) {
  .page-title {
    font-size: 1.6rem;
  }

  .earnings-card {
    padding: 1.2rem;
  }

  .card-content {
    flex-direction: column;
    text-align: center;
    gap: 0.8rem;
  }

  .card-value {
    font-size: 1.4rem;
  }

  .modern-tabs .nav-pills .nav-link {
    padding: 0.6rem 0.8rem;
    margin: 0.1rem;
    font-size: 0.7rem;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.8rem 0.6rem;
  }

  .date-info .date-primary,
  .date-info .date-secondary {
    font-size: 0.65rem;
  }
}

@media (max-width: 576px) {
  .earnings-page {
    padding: 0.8rem 0;
  }

  .container-fluid {
    padding-left: 0.8rem;
    padding-right: 0.8rem;
  }

  .page-actions {
    margin-top: 0.8rem;
  }

  .modern-card .card-body {
    padding: 1.2rem;
  }

  .empty-state {
    padding: 2.4rem 0.8rem;
  }
}
</style>


