<div class="container mt-4">
  <div class="row">
    <div class="col-md-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h4 class="mb-0"><i class="fa fa-users"></i> Meus Referidos</h4>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <h5 class="card-title">Total de Ganhos com Referidos</h5>
                  <h2 class="card-text">R$ <%= totalReferralEarnings.toFixed(2) %></h2>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <h5 class="card-title">Total de Referidos</h5>
                  <h2 class="card-text"><%= referrals.length %></h2>
                </div>
              </div>
            </div>
          </div>

          <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
              <h5 class="mb-0">Seu Link de Referência</h5>
            </div>
            <div class="card-body">
              <div class="input-group">
                <input type="text" class="form-control" id="referralLink" value="<%= referralLink %>" readonly>
                <div class="input-group-append">
                  <button class="btn btn-outline-secondary" type="button" onclick="copyReferralLink()">
                    <i class="fas fa-copy"></i> Copiar
                  </button>
                </div>
              </div>
              <small class="text-muted mt-2 d-block">
                Compartilhe este link com seus amigos. Você ganhará 20% de todos os créditos que eles gastarem!
              </small>
            </div>
          </div>

          <ul class="nav nav-tabs" id="referralsTab" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="users-tab" data-toggle="tab" href="#users" role="tab" aria-controls="users" aria-selected="true">Usuários Referidos</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="earnings-tab" data-toggle="tab" href="#earnings" role="tab" aria-controls="earnings" aria-selected="false">Ganhos com Referidos</a>
            </li>
          </ul>
          <div class="tab-content" id="referralsTabContent">
            <div class="tab-pane fade show active" id="users" role="tabpanel" aria-labelledby="users-tab">
              <div class="table-responsive mt-3">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Usuário</th>
                      <th>Data de Registro</th>
                      <th>Saldo</th>
                      <th>Lives</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% if (referrals.length === 0) { %>
                      <tr>
                        <td colspan="4" class="text-center">Nenhum usuário referido</td>
                      </tr>
                    <% } else { %>
                      <% referrals.forEach(referral => { %>
                        <tr>
                          <td><%= referral.usuario %></td>
                          <td><%= new Date(referral.createdAt).toLocaleDateString() %></td>
                          <td>R$ <%= referral.saldo.toFixed(2) %></td>
                          <td><%= referral.lives %></td>
                        </tr>
                      <% }); %>
                    <% } %>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="tab-pane fade" id="earnings" role="tabpanel" aria-labelledby="earnings-tab">
              <div class="table-responsive mt-3">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Data</th>
                      <th>Usuário</th>
                      <th>Checker</th>
                      <th>Valor</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% if (referralEarnings.length === 0) { %>
                      <tr>
                        <td colspan="4" class="text-center">Nenhum ganho com referidos registrado</td>
                      </tr>
                    <% } else { %>
                      <% referralEarnings.forEach(earning => { %>
                        <tr>
                          <td><%= new Date(earning.created_at).toLocaleString() %></td>
                          <td><%= earning.transaction.user ? earning.transaction.user.usuario : 'N/A' %></td>
                          <td><%= earning.checker ? earning.checker.title : 'N/A' %></td>
                          <td>R$ <%= earning.amount.toFixed(2) %></td>
                        </tr>
                      <% }); %>
                    <% } %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function copyReferralLink() {
    const referralLinkInput = document.getElementById('referralLink');
    referralLinkInput.select();
    document.execCommand('copy');

    // Show alert
    alert('Link de referência copiado para a área de transferência!');
  }
</script>


