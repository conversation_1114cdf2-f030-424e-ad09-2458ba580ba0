<div class="sidebar" id="sidebar">
  <div class="sidebar-wrapper">
    <!-- Logo Section -->
    <div class="logo">
      <a href="/painel">
        <img src="/img/raio.svg" alt="CS" width="32">
        <span class="logo-text">PrivXploit</span>
      </a>
    </div>

    <!-- User Info Section -->
    <div class="user-info">
      <div class="user-avatar">
        <img src="https://www.gravatar.com/avatar/<%= user.email ? require('crypto').createHash('md5').update(user.email.toLowerCase()).digest('hex') : '' %>?s=80&d=mp" alt="<%= user.usuario %>">
        <div class="status-indicator online"></div>
      </div>
      <h6><%= user.usuario %></h6>
      <div class="user-badges">
        <% if (user.rank === 1 || user.role === 'admin') { %>
          <span class="badge badge-primary">
            <i class="fas fa-crown"></i> Administrador
          </span>
        <% } else if (user.role === 'vip') { %>
          <span class="badge badge-warning">
            <i class="fas fa-star"></i> VIP
          </span>
        <% } else if (user.role === 'premium') { %>
          <span class="badge badge-info">
            <i class="fas fa-gem"></i> Premium
          </span>
        <% } else { %>
          <span class="badge badge-secondary">
            <i class="fas fa-user"></i> Usuário
          </span>
        <% } %>
      </div>
      <div class="user-credits">
        <i class="fas fa-coins"></i>
        <span><%= user.saldo.toFixed(2) %> créditos</span>
      </div>
    </div>

    <ul class="nav">
      <li>
        <a href="/painel">
          <i class="fa fa-home"></i>
          <p>
            Dashboard
          </p>
        </a>
      </li>

      <% if (user.role !== 'affiliate') { %>
      <li class="<%= typeof path !== 'undefined' && path.includes('/earnings') ? 'active' : '' %>">
        <a data-toggle="collapse" href="#earnings" aria-expanded="<%= typeof path !== 'undefined' && path.includes('/earnings') ? 'true' : 'false' %>">
          <i class="fa fa-money-bill-wave"></i>
          <p class="title">
            Ganhos <b class="caret"></b>
          </p>
        </a>
        <div class="collapse <%= typeof path !== 'undefined' && path.includes('/earnings') ? 'show' : '' %>" id="earnings">
          <ul class="nav">
            <li class="<%= typeof path !== 'undefined' && path === '/earnings' ? 'active' : '' %>">
              <a href="/earnings">
                <span class="sidebar-mini-icon"><i class="fa fa-chart-line"></i></span>
                <span class="sidebar-normal title"> Meus Ganhos </span>
              </a>
            </li>
            <li class="<%= typeof path !== 'undefined' && path === '/earnings/referrals' ? 'active' : '' %>">
              <a href="/earnings/referrals">
                <span class="sidebar-mini-icon"><i class="fa fa-users"></i></span>
                <span class="sidebar-normal title"> Meus Referidos </span>
              </a>
            </li>
            <% if (user.rank === 1 || user.role === 'admin') { %>
            <li class="<%= typeof path !== 'undefined' && path === '/earnings/admin' ? 'active' : '' %>">
              <a href="/earnings/admin">
                <span class="sidebar-mini-icon"><i class="fa fa-chart-bar"></i></span>
                <span class="sidebar-normal title"> Todos os Ganhos </span>
              </a>
            </li>
            <% } %>
          </ul>
        </div>
      </li>
      <% } %>

      <% if (user.rank === 1 || user.role === 'admin' || user.role === 'programmer') { %>
        <li class="<%= typeof path !== 'undefined' && path === '/admin' ? 'active' : '' %>">
          <a href="/admin">
            <i class="fa fa-tachometer-alt"></i>
            <p class="title">
              Admin Dashboard
            </p>
          </a>
        </li>
        <li class="<%= typeof path !== 'undefined' && path.includes('/admin/') && !path.includes('/admin/dashboard') ? 'active' : '' %>">
          <a data-toggle="collapse" href="#admin" aria-expanded="<%= typeof path !== 'undefined' && path.includes('/admin/') && !path.includes('/admin/dashboard') ? 'true' : 'false' %>">
            <i class="fa fa-cogs"></i>
            <p class="title">
              Administração <b class="caret"></b>
            </p>
          </a>
          <div class="collapse <%= typeof path !== 'undefined' && path.includes('/admin/') && !path.includes('/admin/dashboard') ? 'show' : '' %>" id="admin">
            <ul class="nav">
              <% if (user.role === 'programmer') { %>
              <li class="<%= typeof path !== 'undefined' && path === '/admin/users/view' ? 'active' : '' %>">
                <a href="/admin/users/view">
                  <span class="sidebar-mini-icon"><i class="fa fa-users"></i></span>
                  <span class="sidebar-normal title"> Visualizar Usuários </span>
                </a>
              </li>
              <% } else { %>
              <li class="<%= typeof path !== 'undefined' && path === '/admin/users' ? 'active' : '' %>">
                <a href="/admin/users">
                  <span class="sidebar-mini-icon"><i class="fa fa-users"></i></span>
                  <span class="sidebar-normal title"> Usuários </span>
                </a>
              </li>
              <% } %>
              <li class="<%= typeof path !== 'undefined' && path === '/admin/categories' ? 'active' : '' %>">
                <a href="/admin/categories">
                  <span class="sidebar-mini-icon"><i class="fa fa-folder"></i></span>
                  <span class="sidebar-normal title"> Categorias </span>
                </a>
              </li>
              <li class="<%= typeof path !== 'undefined' && path === '/admin/checkers' ? 'active' : '' %>">
                <a href="/admin/checkers">
                  <span class="sidebar-mini-icon"><i class="fa fa-check-circle"></i></span>
                  <span class="sidebar-normal title"> Checkers </span>
                </a>
              </li>
              <% if (user.rank === 1 || user.role === 'admin') { %>
              <li class="<%= typeof path !== 'undefined' && path === '/admin/plans' ? 'active' : '' %>">
                <a href="/admin/plans">
                  <span class="sidebar-mini-icon"><i class="fa fa-tags"></i></span>
                  <span class="sidebar-normal title"> Planos </span>
                </a>
              </li>
              <li class="<%= typeof path !== 'undefined' && path === '/admin/transactions' ? 'active' : '' %>">
                <a href="/admin/transactions">
                  <span class="sidebar-mini-icon"><i class="fa fa-exchange-alt"></i></span>
                  <span class="sidebar-normal title"> Transações </span>
                </a>
              </li>
              <li class="<%= typeof path !== 'undefined' && path === '/admin/logs' ? 'active' : '' %>">
                <a href="/admin/logs">
                  <span class="sidebar-mini-icon"><i class="fa fa-history"></i></span>
                  <span class="sidebar-normal title"> Logs </span>
                </a>
              </li>
              <li class="<%= typeof path !== 'undefined' && path === '/earnings/admin' ? 'active' : '' %>">
                <a href="/earnings/admin">
                  <span class="sidebar-mini-icon"><i class="fa fa-money-bill-wave"></i></span>
                  <span class="sidebar-normal title"> Ganhos </span>
                </a>
              </li>
              <li class="<%= typeof path !== 'undefined' && path.includes('/admin/version-control') ? 'active' : '' %>">
                <a href="/admin/version-control">
                  <span class="sidebar-mini-icon"><i class="fab fa-github"></i></span>
                  <span class="sidebar-normal title"> Controle de Versão </span>
                </a>
              </li>
              <% } %>
            </ul>
          </div>
        </li>
      <% } %>

      <% if (user.role === 'affiliate') { %>
        <li class="<%= typeof path !== 'undefined' && path.includes('/earnings') ? 'active' : '' %>">
          <a data-toggle="collapse" href="#affiliate_earnings" aria-expanded="<%= typeof path !== 'undefined' && path.includes('/earnings') ? 'true' : 'false' %>">
            <i class="fa fa-money-bill-wave"></i>
            <p class="title">
              Ganhos <b class="caret"></b>
            </p>
          </a>
          <div class="collapse <%= typeof path !== 'undefined' && path.includes('/earnings') ? 'show' : '' %>" id="affiliate_earnings">
            <ul class="nav">
              <li class="<%= typeof path !== 'undefined' && path === '/earnings' ? 'active' : '' %>">
                <a href="/earnings">
                  <span class="sidebar-mini-icon"><i class="fa fa-chart-line"></i></span>
                  <span class="sidebar-normal title"> Meus Ganhos </span>
                </a>
              </li>
              <li class="<%= typeof path !== 'undefined' && path === '/earnings/referrals' ? 'active' : '' %>">
                <a href="/earnings/referrals">
                  <span class="sidebar-mini-icon"><i class="fa fa-users"></i></span>
                  <span class="sidebar-normal title"> Meus Referidos </span>
                </a>
              </li>
            </ul>
          </div>
        </li>
      <% } %>

      <% if (typeof categories !== 'undefined' && categories.length > 0) { %>
        <% categories.forEach(category => { %>
          <li class="<%= typeof path !== 'undefined' && path.includes(`/painel/categories/${category.id}`) ? 'active' : '' %>">
            <a data-toggle="collapse" href="#category<%= category.id %>">
              <i class="<%= category.icon %>"></i>
              <p class="title">
                <%= category.name %> <b class="caret"></b>
              </p>
            </a>
            <div class="collapse <%= typeof path !== 'undefined' && path.includes(`/painel/categories/${category.id}`) ? 'show' : '' %>" id="category<%= category.id %>">
              <ul class="nav">
                <% if (category.checkers && category.checkers.length > 0) { %>
                  <% category.checkers.forEach(checker => { %>
                    <li class="<%= typeof path !== 'undefined' && path === `/painel/checkers/${checker.name}` ? 'active' : '' %>">
                      <a href="/painel/checkers/<%= checker.name %>" <%= checker.name === 'cep' || checker.name === 'ip' || checker.name === 'bin' || checker.name === 'cpf' || checker.name === 'cnpj' ? 'target="_blank"' : '' %>>
                        <span class="sidebar-mini-icon">CHK</span>
                        <span class="sidebar-normal title">
                          <% if (checker.status === 'active') { %>
                            <% if (checker.isCodeApproved) { %>
                              <span class="badge badge-success float-right">ON</span>
                            <% } else { %>
                              <span class="badge badge-warning float-right">Em Breve</span>
                            <% } %>
                          <% } else if (checker.status === 'maintenance') { %>
                            <span class="badge badge-warning float-right">MANUTENÇÃO</span>
                          <% } else { %>
                            <span class="badge badge-danger float-right">OFF</span>
                          <% } %>
                          <%= checker.title %>
                        </span>
                      </a>
                    </li>
                  <% }); %>
                <% } else { %>
                  <li>
                    <a href="#">
                      <span class="sidebar-mini-icon">N/A</span>
                      <span class="sidebar-normal title">Nenhum checker disponível</span>
                    </a>
                  </li>
                <% } %>
              </ul>
            </div>
          </li>
        <% }); %>
      <% } else { %>
        <li>
          <a href="#">
            <i class="fa fa-exclamation-triangle"></i>
            <p>
              Nenhuma categoria disponível
            </p>
          </a>
        </li>
      <% } %>

      <li>
        <a href="/logout">
          <i class="fas fa-sign-out-alt"></i>
          <p>
            Sair
          </p>
        </a>
      </li>
    </ul>

    <div class="sidebar-footer mt-3 text-center">
      <p class="text-muted small mb-0">© <%= new Date().getFullYear() %> PrivXploit</p>
      <p class="text-muted small">Versão 2.0</p>
    </div>
  </div>
</div>

<style>
  .sidebar .user-info {
    padding: 0 15px;
  }
  .sidebar .user-avatar img {
    border: 2px solid rgba(255, 255, 255, 0.2);
  }
  .sidebar .sidebar-wrapper .nav .nav-item.active a:not([data-toggle="collapse"]) {
    background: linear-gradient(to right, #e14eca, #ba54f5);
  }
  .sidebar-mini-icon {
    width: 34px;
    display: inline-block;
    text-align: center;
  }
  .sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 15px;
  }
  .sidebar-wrapper {
    position: relative;
    min-height: 100%;
  }
  .sidebar .nav li.active > a {
    background: rgba(225, 78, 202, 0.1);
    color: #e14eca;
  }
  .sidebar .nav li.active > a i,
  .sidebar .nav li.active > a .sidebar-mini-icon i {
    color: #e14eca;
  }
  .sidebar .nav li.active > a .sidebar-normal {
    color: #e14eca;
    font-weight: 600;
  }
</style>
