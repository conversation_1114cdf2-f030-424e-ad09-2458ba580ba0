<div class="sidebar" id="sidebar">
  <div class="sidebar-wrapper">
    <!-- Logo Section -->
    <div class="logo">
      <a href="/painel">
        <img src="/img/raio.svg" alt="CS" width="32">
        <span class="logo-text">PrivXploit</span>
      </a>
    </div>

    <!-- Main Navigation Area -->
    <div class="sidebar-main-content">
      <!-- Navigation Menu -->
      <ul class="nav">
      <!-- Dashboard -->
      <li class="<%= typeof path !== 'undefined' && path === '/painel' ? 'active' : '' %>">
        <a href="/painel">
          <i class="fas fa-tachometer-alt"></i>
          <p>Dashboard</p>
        </a>
      </li>

      <!-- Profile & Account Section - Available to all users -->
      <li class="nav-section-header">
        <span>Minha Conta</span>
      </li>

      <li class="<%= typeof path !== 'undefined' && path === '/painel/profile' ? 'active' : '' %>">
        <a href="/painel/profile">
          <i class="fas fa-user-circle"></i>
          <p>Meu Perfil</p>
        </a>
      </li>

      <!-- Credits and History - Available to all users -->
      <li class="<%= typeof path !== 'undefined' && path === '/painel/credits' ? 'active' : '' %>">
        <a href="/painel/credits">
          <i class="fas fa-credit-card"></i>
          <p>Comprar Créditos</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path === '/painel/transactions' ? 'active' : '' %>">
        <a href="/painel/transactions">
          <i class="fas fa-history"></i>
          <p>Histórico</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path === '/painel/payment-history' ? 'active' : '' %>">
        <a href="/painel/payment-history">
          <i class="fas fa-receipt"></i>
          <p>Pagamentos PIX/Crypto</p>
        </a>
      </li>

      <!-- Earnings Section - For Programmers and Affiliates -->
      <% if (user.role === 'programmer' || user.role === 'affiliate' || user.rank === 1 || user.role === 'admin') { %>
      <li class="nav-section-header">
        <span>Ganhos</span>
      </li>

      <li class="<%= typeof path !== 'undefined' && path.includes('/earnings') ? 'active' : '' %>">
        <a data-toggle="collapse" href="#earnings" aria-expanded="false">
          <i class="fas fa-chart-line"></i>
          <p>Meus Ganhos <b class="caret fas fa-chevron-down"></b></p>
        </a>
        <div class="collapse" id="earnings">
          <ul class="nav">
            <li class="<%= typeof path !== 'undefined' && path === '/earnings' ? 'active' : '' %>">
              <a href="/earnings">
                <span class="sidebar-mini-icon"><i class="fas fa-chart-line"></i></span>
                <span class="sidebar-normal">Resumo</span>
              </a>
            </li>
            <% if (user.role === 'affiliate' || user.rank === 1 || user.role === 'admin') { %>
            <li class="<%= typeof path !== 'undefined' && path === '/earnings/referrals' ? 'active' : '' %>">
              <a href="/earnings/referrals">
                <span class="sidebar-mini-icon"><i class="fas fa-users"></i></span>
                <span class="sidebar-normal">Referidos</span>
              </a>
            </li>
            <% } %>
            <% if (user.rank === 1 || user.role === 'admin') { %>
            <li class="<%= typeof path !== 'undefined' && path === '/earnings/admin' ? 'active' : '' %>">
              <a href="/earnings/admin">
                <span class="sidebar-mini-icon"><i class="fas fa-chart-bar"></i></span>
                <span class="sidebar-normal">Todos os Ganhos</span>
              </a>
            </li>
            <% } %>
          </ul>
        </div>
      </li>
      <% } %>

      <!-- Admin Section - For Admins and Programmers -->
      <% if (user.rank === 1 || user.role === 'admin' || user.role === 'programmer') { %>
      <li class="nav-section-header">
        <span><%= user.role === 'programmer' ? 'Ferramentas' : 'Administração' %></span>
      </li>

      <% if (user.rank === 1 || user.role === 'admin') { %>
      <li class="<%= typeof path !== 'undefined' && path === '/admin' ? 'active' : '' %>">
        <a href="/admin">
          <i class="fas fa-shield-alt"></i>
          <p>Admin Dashboard</p>
        </a>
      </li>
      <% } %>

      <!-- User Management -->
      <% if (user.role === 'programmer') { %>
      <li class="<%= typeof path !== 'undefined' && path === '/admin/users/view' ? 'active' : '' %>">
        <a href="/admin/users/view">
          <i class="fas fa-users"></i>
          <p>Visualizar Usuários</p>
        </a>
      </li>
      <% } else if (user.rank === 1 || user.role === 'admin') { %>
      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/users') ? 'active' : '' %>">
        <a href="/admin/users">
          <i class="fas fa-users"></i>
          <p>Gerenciar Usuários</p>
        </a>
      </li>
      <% } %>

      <!-- Content Management -->
      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/categories') ? 'active' : '' %>">
        <a href="/admin/categories">
          <i class="fas fa-folder"></i>
          <p>Categorias</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/checkers') ? 'active' : '' %>">
        <a href="/admin/checkers">
          <i class="fas fa-check-circle"></i>
          <p>Checkers</p>
        </a>
      </li>

      <!-- Financial Management - Admin Only -->
      <% if (user.rank === 1 || user.role === 'admin') { %>
      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/plans') ? 'active' : '' %>">
        <a href="/admin/plans">
          <i class="fas fa-tags"></i>
          <p>Planos</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/transactions') ? 'active' : '' %>">
        <a href="/admin/transactions">
          <i class="fas fa-exchange-alt"></i>
          <p>Transações Admin</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/payment-transactions') ? 'active' : '' %>">
        <a href="/admin/payment-transactions">
          <i class="fas fa-credit-card"></i>
          <p>Pagamentos PIX/Crypto</p>
        </a>
      </li>

      <!-- System Management - Admin Only -->
      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/logs') ? 'active' : '' %>">
        <a href="/admin/logs">
          <i class="fas fa-file-alt"></i>
          <p>Logs do Sistema</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/monitoring') ? 'active' : '' %>">
        <a href="/admin/monitoring">
          <i class="fas fa-chart-line"></i>
          <p>Monitoramento</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/code-reviews') ? 'active' : '' %>">
        <a href="/admin/code-reviews">
          <i class="fas fa-code"></i>
          <p>Revisão de Código</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path.includes('/admin/version-control') ? 'active' : '' %>">
        <a href="/admin/version-control">
          <i class="fab fa-github"></i>
          <p>Controle de Versão</p>
        </a>
      </li>

      <li class="<%= typeof path !== 'undefined' && path.includes('/health') ? 'active' : '' %>">
        <a href="/health/detailed" target="_blank">
          <i class="fas fa-heartbeat"></i>
          <p>Health Check</p>
        </a>
      </li>
      <% } %>
      <% } %>



      <!-- Checkers Section - Available to all users -->
      <% if (typeof categories !== 'undefined' && categories.length > 0) { %>
      <li class="nav-section-header">
        <span>Ferramentas</span>
      </li>

      <% categories.forEach(category => { %>
        <li class="category-item <%= typeof path !== 'undefined' && path.includes(`/painel/categories/${category.id}`) ? 'active' : '' %>">
          <a data-toggle="collapse" href="#category<%= category.id %>" aria-expanded="false" class="category-link">
            <i class="<%= category.icon %>"></i>
            <p><%= category.name %>
              <span class="category-count">(<%= category.checkers ? category.checkers.length : 0 %>)</span>
              <b class="caret fas fa-chevron-down"></b>
            </p>
          </a>
          <div class="collapse" id="category<%= category.id %>">
            <ul class="nav checker-list">
              <% if (category.checkers && category.checkers.length > 0) { %>
                <% category.checkers.forEach(checker => { %>
                  <li class="checker-item <%= typeof path !== 'undefined' && path === `/painel/checkers/${checker.name}` ? 'active' : '' %>">
                    <a href="/painel/checkers/<%= checker.name %>" <%= checker.name === 'cep' || checker.name === 'ip' || checker.name === 'bin' || checker.name === 'cpf' || checker.name === 'cnpj' ? 'target="_blank"' : '' %> class="checker-link">
                      <span class="sidebar-mini-icon">
                        <i class="fas fa-<%= checker.status === 'active' ? 'check-circle' : (checker.status === 'maintenance' ? 'wrench' : 'times-circle') %>"></i>
                      </span>
                      <span class="sidebar-normal">
                        <%= checker.title %>
                        <% if (checker.status === 'active') { %>
                          <% if (checker.isCodeApproved) { %>
                            <span class="checker-status status-active">ON</span>
                          <% } else { %>
                            <span class="checker-status status-pending">Em Breve</span>
                          <% } %>
                        <% } else if (checker.status === 'maintenance') { %>
                          <span class="checker-status status-maintenance">MANUTENÇÃO</span>
                        <% } else { %>
                          <span class="checker-status status-disabled">OFF</span>
                        <% } %>
                      </span>
                    </a>
                  </li>
                <% }); %>
              <% } else { %>
                <li class="empty-category">
                  <a href="#" class="disabled">
                    <span class="sidebar-mini-icon"><i class="fas fa-exclamation-triangle"></i></span>
                    <span class="sidebar-normal">Nenhum checker disponível</span>
                  </a>
                </li>
              <% } %>
            </ul>
          </div>
        </li>
      <% }); %>
      <% } else { %>
        <li class="nav-section-header">
          <span>Ferramentas</span>
        </li>
        <li class="empty-tools">
          <a href="#" class="disabled">
            <i class="fas fa-exclamation-triangle"></i>
            <p>Nenhuma ferramenta disponível</p>
          </a>
        </li>
      <% } %>

      <!-- Logout -->
      <li class="nav-section-divider"></li>
      <li>
        <a href="/logout" class="logout-link">
          <i class="fas fa-sign-out-alt"></i>
          <p>Sair</p>
        </a>
      </li>
    </ul>
    </div>

    <!-- User Info Section - Compact Bottom Area -->
    <div class="user-info-compact">
      <div class="user-summary">
        <div class="user-avatar-small">
          <img src="https://www.gravatar.com/avatar/<%= user.email ? require('crypto').createHash('md5').update(user.email.toLowerCase()).digest('hex') : '' %>?s=40&d=mp" alt="<%= user.usuario %>">
          <div class="status-indicator online"></div>
        </div>
        <div class="user-details">
          <div class="user-name"><%= user.usuario %></div>
          <div class="user-role">
            <% if (user.rank === 1 || user.role === 'admin') { %>
              <span class="role-badge admin"><i class="fas fa-crown"></i> Admin</span>
            <% } else if (user.role === 'vip') { %>
              <span class="role-badge vip"><i class="fas fa-star"></i> VIP</span>
            <% } else if (user.role === 'premium') { %>
              <span class="role-badge premium"><i class="fas fa-gem"></i> Premium</span>
            <% } else { %>
              <span class="role-badge user"><i class="fas fa-user"></i> Usuário</span>
            <% } %>
          </div>
          <div class="user-credits-compact">
            <i class="fas fa-coins"></i>
            <span><%= user.saldo.toFixed(2) %></span>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
      <p>© <%= new Date().getFullYear() %> PrivXploit</p>
      <p>Versão 2.0</p>
    </div>
  </div>
</div>

<!-- Mobile Overlay -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<style>
/* Status específicos para checkers */
.checker-status.status-pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}

.checker-status.status-active {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}

.checker-status.status-maintenance {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}

.checker-status.status-disabled {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}
</style>
