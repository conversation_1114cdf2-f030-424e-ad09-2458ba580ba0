<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= typeof title !== 'undefined' ? title : 'Login - PrivXploit' %></title>
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
  <link rel="stylesheet" href="/css/style.css">
  <style>
    body {
      background: linear-gradient(to right, #1e1e2f, #1e1e24);
      color: #fff;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .card {
      background-color: rgba(30, 30, 47, 0.9);
      border: none;
      border-radius: 10px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    }
    .card-header {
      background: linear-gradient(to right, #e14eca, #ba54f5);
      color: white;
      border-radius: 10px 10px 0 0 !important;
      padding: 15px;
    }
    .form-control {
      background-color: rgba(30, 30, 47, 0.5);
      border: 1px solid #2b3553;
      color: white;
      border-radius: 5px;
    }
    .form-control:focus {
      background-color: rgba(30, 30, 47, 0.7);
      border-color: #e14eca;
      color: white;
      box-shadow: none;
    }
    .form-control::placeholder {
      color: #aaa;
    }
    .input-group-text {
      background-color: rgba(30, 30, 47, 0.5);
      border: 1px solid #2b3553;
      color: white;
    }
    .btn-primary {
      background: linear-gradient(to right, #e14eca, #ba54f5);
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      font-weight: bold;
    }
    .btn-primary:hover {
      background: linear-gradient(to right, #ba54f5, #e14eca);
    }
    .alert {
      border-radius: 5px;
    }
    .logo {
      max-width: 150px;
      margin-bottom: 20px;
    }
    .text-primary {
      color: #e14eca !important;
    }
    a {
      color: #e14eca;
    }
    a:hover {
      color: #ba54f5;
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="row justify-content-center align-items-center" style="min-height: 100vh;">
      <div class="col-md-6">
        <div class="text-center mb-4">
          <img src="/img/raio.svg" alt="PrivXploit" class="logo">
          <h2 class="text-white">PrivXploit</h2>
        </div>

        <div class="card">
          <div class="card-header text-center">
            <h4 class="mb-0"><i class="fas fa-sign-in-alt"></i> Login</h4>
          </div>
          <div class="card-body">
            <% if (typeof error !== 'undefined' && error.length > 0) { %>
              <div class="alert alert-danger" role="alert">
                <%= error %>
              </div>
            <% } %>

            <% if (typeof success !== 'undefined' && success.length > 0) { %>
              <div class="alert alert-success" role="alert">
                Sucesso! Redirecionando...
              </div>
              <script>
                setTimeout(function() {
                  window.location.href = '/painel';
                }, 3000);
              </script>
            <% } %>

            <form method="POST" action="/login">
              <div class="form-group">
                <label for="usuario">Usuário</label>
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text bg-transparent text-white">
                      <i class="fas fa-user"></i>
                    </span>
                  </div>
                  <input type="text" class="form-control" id="usuario" name="usuario" placeholder="Digite seu usuário" required>
                </div>
              </div>

              <div class="form-group">
                <label for="senha">Senha</label>
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text bg-transparent text-white">
                      <i class="fas fa-lock"></i>
                    </span>
                  </div>
                  <input type="password" class="form-control" id="senha" name="senha" placeholder="Digite sua senha" required>
                </div>
              </div>

              <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fas fa-sign-in-alt"></i> Entrar
                </button>
              </div>
            </form>

            <div class="text-center mt-3">
              <p><a href="/forgot-password">Esqueceu sua senha?</a></p>
              <p>Não tem uma conta? <a href="/register" class="text-primary">Registre-se</a></p>
            </div>
          </div>
        </div>

        <div class="text-center mt-3 text-muted">
          <small>&copy; <%= new Date().getFullYear() %> PrivXploit. Todos os direitos reservados.</small>
        </div>
      </div>
    </div>
  </div>

  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
