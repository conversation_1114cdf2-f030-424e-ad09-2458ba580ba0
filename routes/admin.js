const express = require('express');
const router = express.Router();
const { ensureAuthenticated, ensureAdmin, ensureProgrammer, ensureFullAdmin, logActivity } = require('../middleware/auth');
const { User, Checker, Category, Transaction, Plan, Subscription, ActivityLog } = require('../models');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const upload = require('../middleware/upload');

// Admin dashboard - accessible by admins and programmers
router.get('/', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    // Get counts for dashboard
    const userCount = await User.count();
    const activeUserCount = await User.count({ where: { status: 'active' } });
    const checkerCount = await Checker.count();
    const activeCheckerCount = await Checker.count({ where: { status: 'active' } });
    const categoryCount = await Category.count();
    const transactionCount = await Transaction.count();

    // Get recent transactions
    const recentTransactions = await Transaction.findAll({
      include: [
        { model: User, as: 'user', attributes: ['id', 'usuario'] }
      ],
      order: [['id', 'DESC']],
      limit: 10
    });

    // Get recent activity logs
    const recentActivity = await ActivityLog.findAll({
      include: [
        { model: User, as: 'user', attributes: ['id', 'usuario'] }
      ],
      order: [['id', 'DESC']],
      limit: 10
    });

    res.render('admin/dashboard', {
      title: 'Painel Administrativo - CancroSoft TM',
      user: req.session.user,
      userCount,
      activeUserCount,
      checkerCount,
      activeCheckerCount,
      categoryCount,
      transactionCount,
      recentTransactions,
      recentActivity
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar o painel administrativo');
    res.redirect('/painel');
  }
});

// User management
router.get('/users', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const users = await User.findAll({
      order: [['id', 'DESC']]
    });

    // Generate Gravatar hashes for each user
    const usersWithGravatar = users.map(user => {
      const userData = user.toJSON();
      // Generate MD5 hash for Gravatar
      userData.gravatarHash = user.email ?
        crypto.createHash('md5').update(user.email.toLowerCase()).digest('hex') :
        '';
      return userData;
    });

    res.render('admin/users/index', {
      title: 'Gerenciar Usuários - CancroSoft TM',
      user: req.session.user,
      users: usersWithGravatar
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar usuários');
    res.redirect('/admin');
  }
});

// View-only user list for programmers
router.get('/users/view', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const users = await User.findAll({
      order: [['id', 'DESC']]
    });

    // Generate Gravatar hashes for each user
    const usersWithGravatar = users.map(user => {
      const userData = user.toJSON();
      // Generate MD5 hash for Gravatar
      userData.gravatarHash = user.email ?
        crypto.createHash('md5').update(user.email.toLowerCase()).digest('hex') :
        '';
      return userData;
    });

    res.render('admin/users/view', {
      title: 'Visualizar Usuários - CancroSoft TM',
      user: req.session.user,
      users: usersWithGravatar
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar usuários');
    res.redirect('/admin');
  }
});

// Add user form
router.get('/users/add', ensureAuthenticated, ensureAdmin, (req, res) => {
  res.render('admin/users/add', {
    title: 'Adicionar Usuário - CancroSoft TM',
    user: req.session.user
  });
});

// Add user process
router.post('/users/add', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const { usuario, senha, email, role, saldo } = req.body;

    // Validate input
    if (!usuario || !senha) {
      req.flash('error_msg', 'Preencha todos os campos obrigatórios');
      return res.redirect('/admin/users/add');
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { usuario },
          { email: email || null }
        ]
      }
    });

    if (existingUser) {
      req.flash('error_msg', 'Usuário ou email já existe');
      return res.redirect('/admin/users/add');
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(senha, salt);

    // Generate unique referral code
    const uniqueReferralCode = crypto.randomBytes(4).toString('hex');

    // Create user
    const newUser = await User.create({
      usuario,
      senha: hashedPassword,
      email: email || null,
      role: role || 'user',
      rank: role === 'admin' ? 1 : 0,
      saldo: saldo || 0,
      criador: req.session.user.usuario,
      referral_code: uniqueReferralCode,
      referred_by: null, // Admin-created users don't have referrers
      status: 'active'
    });

    // Log the role assignment for special roles
    if (role === 'programmer') {
      await logActivity(
        req,
        'role_assigned',
        `Usuário ${usuario} designado como programador`,
        'user',
        newUser.id
      );
    } else if (role === 'affiliate') {
      await logActivity(
        req,
        'role_assigned',
        `Usuário ${usuario} designado como afiliado`,
        'user',
        newUser.id
      );
    }

    // Log activity
    await logActivity(
      req,
      'user_created',
      `Usuário ${usuario} criado`,
      'user',
      newUser.id
    );

    req.flash('success_msg', 'Usuário adicionado com sucesso');
    res.redirect('/admin/users');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao adicionar usuário');
    res.redirect('/admin/users/add');
  }
});

// Edit user form
router.get('/users/edit/:id', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);

    if (!user) {
      req.flash('error_msg', 'Usuário não encontrado');
      return res.redirect('/admin/users');
    }

    res.render('admin/users/edit', {
      title: 'Editar Usuário - CancroSoft TM',
      user: req.session.user,
      editUser: user
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar usuário');
    res.redirect('/admin/users');
  }
});

// Update user
router.post('/users/edit/:id', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const { usuario, email, role, saldo, status } = req.body;
    const userId = req.params.id;

    const user = await User.findByPk(userId);

    if (!user) {
      req.flash('error_msg', 'Usuário não encontrado');
      return res.redirect('/admin/users');
    }

    // Check if username or email is already taken by another user
    if (usuario !== user.usuario || (email && email !== user.email)) {
      const existingUser = await User.findOne({
        where: {
          [Op.and]: [
            { id: { [Op.ne]: userId } },
            {
              [Op.or]: [
                { usuario },
                { email: email || null }
              ]
            }
          ]
        }
      });

      if (existingUser) {
        req.flash('error_msg', 'Usuário ou email já existe');
        return res.redirect(`/admin/users/edit/${userId}`);
      }
    }

    // Update user
    user.usuario = usuario;
    if (email) user.email = email;

    // Update password if provided
    if (req.body.senha) {
      const salt = await bcrypt.genSalt(10);
      user.senha = await bcrypt.hash(req.body.senha, salt);
    }

    // Update role and rank
    if (role) {
      // Check if role has changed
      const oldRole = user.role;
      user.role = role;
      user.rank = role === 'admin' ? 1 : 0;

      // Log role changes for special roles
      if (oldRole !== role) {
        if (role === 'programmer') {
          await logActivity(
            req,
            'role_changed',
            `Usuário ${usuario} designado como programador`,
            'user',
            user.id
          );
        } else if (role === 'affiliate') {
          await logActivity(
            req,
            'role_changed',
            `Usuário ${usuario} designado como afiliado`,
            'user',
            user.id
          );
        }
      }
    }

    // Update balance if provided
    if (saldo !== undefined) {
      const oldBalance = user.saldo;
      user.saldo = parseFloat(saldo);

      // Create transaction record for balance change
      if (oldBalance !== user.saldo) {
        await Transaction.create({
          user_id: user.id,
          amount: user.saldo - oldBalance,
          type: user.saldo > oldBalance ? 'credit' : 'debit',
          description: 'Ajuste de saldo pelo administrador',
          balance_before: oldBalance,
          balance_after: user.saldo,
          admin_id: req.session.user.id
        });
      }
    }

    // Update status
    if (status) {
      user.status = status;
    }

    await user.save();

    // Log activity
    await logActivity(
      req,
      'user_updated',
      `Usuário ${usuario} atualizado`,
      'user',
      user.id
    );

    req.flash('success_msg', 'Usuário atualizado com sucesso');
    res.redirect('/admin/users');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao atualizar usuário');
    res.redirect('/admin/users');
  }
});

// Delete user
router.delete('/users/delete/:id', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const userId = req.params.id;

    // Don't allow deleting yourself
    if (userId == req.session.user.id) {
      req.flash('error_msg', 'Você não pode excluir seu próprio usuário');
      return res.redirect('/admin/users');
    }

    const user = await User.findByPk(userId);

    if (!user) {
      req.flash('error_msg', 'Usuário não encontrado');
      return res.redirect('/admin/users');
    }

    // Log activity before deleting
    await logActivity(
      req,
      'user_deleted',
      `Usuário ${user.usuario} excluído`,
      'user',
      user.id
    );

    await user.destroy();

    req.flash('success_msg', 'Usuário removido com sucesso');
    res.redirect('/admin/users');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao remover usuário');
    res.redirect('/admin/users');
  }
});

// Category management
router.get('/categories', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const categories = await Category.findAll({
      order: [['display_order', 'ASC']]
    });

    res.render('admin/categories/index', {
      title: 'Gerenciar Categorias - CancroSoft TM',
      user: req.session.user,
      categories
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar categorias');
    res.redirect('/admin');
  }
});

// Add category form
router.get('/categories/add', ensureAuthenticated, ensureProgrammer, (req, res) => {
  res.render('admin/categories/add', {
    title: 'Adicionar Categoria - CancroSoft TM',
    user: req.session.user
  });
});

// Add category process
router.post('/categories/add', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const { name, description, icon, required_role, display_order, status } = req.body;

    // Validate input
    if (!name) {
      req.flash('error_msg', 'Nome da categoria é obrigatório');
      return res.redirect('/admin/categories/add');
    }

    // Check if category already exists
    const existingCategory = await Category.findOne({ where: { name } });

    if (existingCategory) {
      req.flash('error_msg', 'Categoria já existe');
      return res.redirect('/admin/categories/add');
    }

    // Create category
    const newCategory = await Category.create({
      name,
      description: description || null,
      icon: icon || 'fa fa-folder',
      required_role: required_role || 'user',
      display_order: display_order || 0,
      status: status || 'active'
    });

    // Log activity
    await logActivity(
      req,
      'category_created',
      `Categoria ${name} criada`,
      'category',
      newCategory.id
    );

    req.flash('success_msg', 'Categoria adicionada com sucesso');
    res.redirect('/admin/categories');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao adicionar categoria');
    res.redirect('/admin/categories/add');
  }
});

// Edit category form
router.get('/categories/edit/:id', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const category = await Category.findByPk(req.params.id);

    if (!category) {
      req.flash('error_msg', 'Categoria não encontrada');
      return res.redirect('/admin/categories');
    }

    res.render('admin/categories/edit', {
      title: 'Editar Categoria - CancroSoft TM',
      user: req.session.user,
      category
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar categoria');
    res.redirect('/admin/categories');
  }
});

// Update category
router.post('/categories/edit/:id', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const { name, description, icon, required_role, display_order, status } = req.body;
    const categoryId = req.params.id;

    const category = await Category.findByPk(categoryId);

    if (!category) {
      req.flash('error_msg', 'Categoria não encontrada');
      return res.redirect('/admin/categories');
    }

    // Check if name is already taken by another category
    if (name !== category.name) {
      const existingCategory = await Category.findOne({
        where: {
          name,
          id: { [Op.ne]: categoryId }
        }
      });

      if (existingCategory) {
        req.flash('error_msg', 'Nome de categoria já existe');
        return res.redirect(`/admin/categories/edit/${categoryId}`);
      }
    }

    // Update category
    category.name = name;
    category.description = description || null;
    category.icon = icon || 'fa fa-folder';
    category.required_role = required_role || 'user';
    category.display_order = display_order || 0;
    category.status = status || 'active';

    await category.save();

    // Log activity
    await logActivity(
      req,
      'category_updated',
      `Categoria ${name} atualizada`,
      'category',
      category.id
    );

    req.flash('success_msg', 'Categoria atualizada com sucesso');
    res.redirect('/admin/categories');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao atualizar categoria');
    res.redirect('/admin/categories');
  }
});

// Delete category
router.delete('/categories/delete/:id', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const categoryId = req.params.id;

    const category = await Category.findByPk(categoryId);

    if (!category) {
      req.flash('error_msg', 'Categoria não encontrada');
      return res.redirect('/admin/categories');
    }

    // Check if category has checkers
    const checkerCount = await Checker.count({ where: { category_id: categoryId } });

    if (checkerCount > 0) {
      req.flash('error_msg', 'Não é possível excluir uma categoria que possui checkers');
      return res.redirect('/admin/categories');
    }

    // Log activity before deleting
    await logActivity(
      req,
      'category_deleted',
      `Categoria ${category.name} excluída`,
      'category',
      category.id
    );

    await category.destroy();

    req.flash('success_msg', 'Categoria removida com sucesso');
    res.redirect('/admin/categories');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao remover categoria');
    res.redirect('/admin/categories');
  }
});

// Checker management
router.get('/checkers', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const checkers = await Checker.findAll({
      include: [{ model: Category, as: 'category' }],
      order: [
        [{ model: Category, as: 'category' }, 'display_order', 'ASC'],
        ['display_order', 'ASC']
      ]
    });

    // Verificar status de aprovação de cada checker
    const { CodeReview } = require('../models');
    for (const checker of checkers) {
      let isApproved = false;

      if (checker.approved_code_hash) {
        const codeReview = await CodeReview.findOne({
          where: {
            approved_code_hash: checker.approved_code_hash,
            status: 'approved'
          }
        });
        isApproved = !!codeReview;
      }

      checker.dataValues.isCodeApproved = isApproved;
    }

    res.render('admin/checkers/index', {
      title: 'Gerenciar Checkers - CancroSoft TM',
      user: req.session.user,
      checkers
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar checkers');
    res.redirect('/admin');
  }
});

// Add checker form
router.get('/checkers/add', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const categories = await Category.findAll({
      where: { status: 'active' },
      order: [['display_order', 'ASC']]
    });

    // Get programmers (users with role 'programmer' or 'admin')
    const programmers = await User.findAll({
      where: {
        [Op.or]: [
          { role: 'programmer' },
          { role: 'admin' }
        ]
      },
      order: [['usuario', 'ASC']]
    });

    res.render('admin/checkers/add', {
      title: 'Adicionar Checker - CancroSoft TM',
      user: req.session.user,
      categories,
      programmers
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar categorias');
    res.redirect('/admin/checkers');
  }
});

// Add checker process
router.post('/checkers/add', ensureAuthenticated, ensureProgrammer, upload.single('module_file'), async (req, res) => {
  try {
    const {
      name, endpoint, title, description, category_id,
      price, status, icon, background_image,
      charge_type, required_role, display_order,
      custom_code, programmer_id
    } = req.body;

    // Validate input
    if (!name || !endpoint || !title || !category_id) {
      req.flash('error_msg', 'Preencha todos os campos obrigatórios');
      return res.redirect('/admin/checkers/add');
    }

    // Check if checker already exists
    const existingChecker = await Checker.findOne({
      where: {
        [Op.or]: [
          { name },
          { endpoint }
        ]
      }
    });

    if (existingChecker) {
      req.flash('error_msg', 'Checker com este nome ou endpoint já existe');
      return res.redirect('/admin/checkers/add');
    }

    // Determine module path if file was uploaded
    let module_path = null;
    if (req.file) {
      module_path = `modules/checkers/${req.file.filename}`;
    }

    // Create checker
    const newChecker = await Checker.create({
      name,
      endpoint,
      title,
      description: description || null,
      category_id,
      price: price || 1.0,
      status: status || 'active',
      icon: icon || 'fa fa-check',
      background_image: background_image || null,
      charge_type: charge_type || 'per_test',
      required_role: required_role || 'user',
      display_order: display_order || 0,
      module_path,
      custom_code: custom_code || null,
      programmer_id: programmer_id || null
    });

    // Criar code review automaticamente se há código para revisar
    if (custom_code || module_path) {
      const { CodeReview } = require('../models');
      const codeReviewService = require('../services/CodeReviewService');

      let codeContent = '';
      let codeType = 'custom_code';
      let originalFilename = null;

      if (module_path && req.file) {
        // Ler código do arquivo
        const fs = require('fs');
        const path = require('path');
        codeContent = fs.readFileSync(path.join(process.cwd(), module_path), 'utf8');
        codeType = 'module';
        originalFilename = req.file.originalname;
      } else if (custom_code) {
        codeContent = custom_code;
      }

      if (codeContent) {
        try {
          await codeReviewService.submitCodeForReview({
            checkerId: newChecker.id,
            submittedBy: req.session.user.id,
            code: codeContent,
            codeType: codeType,
            originalFilename: originalFilename
          });
        } catch (reviewError) {
          console.error('Erro ao criar code review:', reviewError);
          // Não falhar a criação do checker por causa do code review
        }
      }
    }

    // Log activity
    await logActivity(
      req,
      'checker_created',
      `Checker ${name} criado`,
      'checker',
      newChecker.id
    );

    req.flash('success_msg', 'Checker adicionado com sucesso. Code review criado automaticamente.');
    res.redirect('/admin/checkers');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao adicionar checker: ' + err.message);
    res.redirect('/admin/checkers/add');
  }
});

// Edit checker form
router.get('/checkers/edit/:id', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const checker = await Checker.findByPk(req.params.id, {
      include: [{ model: User, as: 'programmer' }]
    });

    if (!checker) {
      req.flash('error_msg', 'Checker não encontrado');
      return res.redirect('/admin/checkers');
    }

    const categories = await Category.findAll({
      order: [['display_order', 'ASC']]
    });

    // Get programmers (users with role 'programmer' or 'admin')
    const programmers = await User.findAll({
      where: {
        [Op.or]: [
          { role: 'programmer' },
          { role: 'admin' }
        ]
      },
      order: [['usuario', 'ASC']]
    });

    res.render('admin/checkers/edit', {
      title: 'Editar Checker - CancroSoft TM',
      user: req.session.user,
      checker,
      categories,
      programmers
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar checker');
    res.redirect('/admin/checkers');
  }
});

// Update checker
router.post('/checkers/edit/:id', ensureAuthenticated, ensureProgrammer, upload.single('module_file'), async (req, res) => {
  try {
    const {
      name, endpoint, title, description, category_id,
      price, status, icon, background_image,
      charge_type, required_role, display_order,
      custom_code, programmer_id
    } = req.body;
    const checkerId = req.params.id;

    const checker = await Checker.findByPk(checkerId);

    if (!checker) {
      req.flash('error_msg', 'Checker não encontrado');
      return res.redirect('/admin/checkers');
    }

    // Check if name or endpoint is already taken by another checker
    if (name !== checker.name || endpoint !== checker.endpoint) {
      const existingChecker = await Checker.findOne({
        where: {
          [Op.and]: [
            { id: { [Op.ne]: checkerId } },
            {
              [Op.or]: [
                { name },
                { endpoint }
              ]
            }
          ]
        }
      });

      if (existingChecker) {
        req.flash('error_msg', 'Checker com este nome ou endpoint já existe');
        return res.redirect(`/admin/checkers/edit/${checkerId}`);
      }
    }

    // Determine module path if file was uploaded
    let module_path = checker.module_path;
    if (req.file) {
      module_path = `modules/checkers/${req.file.filename}`;
    }

    // Verificar se o código mudou para criar novo code review
    const codeChanged = (
      (custom_code && custom_code !== checker.custom_code) ||
      (req.file && module_path !== checker.module_path)
    );

    // Update checker
    checker.name = name;
    checker.endpoint = endpoint;
    checker.title = title;
    checker.description = description || null;
    checker.category_id = category_id;
    checker.price = price || 1.0;
    checker.status = status || 'active';
    checker.icon = icon || 'fa fa-check';
    checker.background_image = background_image || null;
    checker.charge_type = charge_type || 'per_test';
    checker.required_role = required_role || 'user';
    checker.display_order = display_order || 0;
    checker.module_path = module_path;
    checker.custom_code = custom_code || null;
    checker.programmer_id = programmer_id || null;

    // Limpar hash aprovado se o código mudou
    if (codeChanged) {
      checker.approved_code_hash = null;
    }

    await checker.save();

    // Criar novo code review se o código mudou
    if (codeChanged && (custom_code || module_path)) {
      const { CodeReview } = require('../models');
      const codeReviewService = require('../services/CodeReviewService');

      let codeContent = '';
      let codeType = 'custom_code';
      let originalFilename = null;

      if (module_path && req.file) {
        // Ler código do arquivo
        const fs = require('fs');
        const path = require('path');
        codeContent = fs.readFileSync(path.join(process.cwd(), module_path), 'utf8');
        codeType = 'module';
        originalFilename = req.file.originalname;
      } else if (custom_code) {
        codeContent = custom_code;
      }

      if (codeContent) {
        try {
          await codeReviewService.submitCodeForReview({
            checkerId: checker.id,
            submittedBy: req.session.user.id,
            code: codeContent,
            codeType: codeType,
            originalFilename: originalFilename
          });
        } catch (reviewError) {
          console.error('Erro ao criar code review:', reviewError);
          // Não falhar a atualização do checker por causa do code review
        }
      }
    }

    // Log activity
    await logActivity(
      req,
      'checker_updated',
      `Checker ${name} atualizado`,
      'checker',
      checker.id
    );

    const successMessage = codeChanged
      ? 'Checker atualizado com sucesso. Novo code review criado automaticamente.'
      : 'Checker atualizado com sucesso';

    req.flash('success_msg', successMessage);
    res.redirect('/admin/checkers');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao atualizar checker: ' + err.message);
    res.redirect('/admin/checkers');
  }
});

// Delete checker
router.delete('/checkers/delete/:id', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const checkerId = req.params.id;

    const checker = await Checker.findByPk(checkerId);

    if (!checker) {
      req.flash('error_msg', 'Checker não encontrado');
      return res.redirect('/admin/checkers');
    }

    // Log activity before deleting
    await logActivity(
      req,
      'checker_deleted',
      `Checker ${checker.name} excluído`,
      'checker',
      checker.id
    );

    await checker.destroy();

    req.flash('success_msg', 'Checker removido com sucesso');
    res.redirect('/admin/checkers');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao remover checker');
    res.redirect('/admin/checkers');
  }
});

// Plan management
router.get('/plans', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const plans = await Plan.findAll({
      order: [['price', 'ASC']]
    });

    res.render('admin/plans/index', {
      title: 'Gerenciar Planos - CancroSoft TM',
      user: req.session.user,
      plans
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar planos');
    res.redirect('/admin');
  }
});

// Add plan form
router.get('/plans/add', ensureAuthenticated, ensureAdmin, (req, res) => {
  res.render('admin/plans/add', {
    title: 'Adicionar Plano - CancroSoft TM',
    user: req.session.user
  });
});

// Add plan process
router.post('/plans/add', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const {
      name, description, price, credits,
      duration_days, status, features, role_granted
    } = req.body;

    // Validate input
    if (!name || !price || !credits) {
      req.flash('error_msg', 'Preencha todos os campos obrigatórios');
      return res.redirect('/admin/plans/add');
    }

    // Check if plan already exists
    const existingPlan = await Plan.findOne({ where: { name } });

    if (existingPlan) {
      req.flash('error_msg', 'Plano com este nome já existe');
      return res.redirect('/admin/plans/add');
    }

    // Create plan
    const newPlan = await Plan.create({
      name,
      description: description || null,
      price: parseFloat(price),
      credits: parseInt(credits),
      duration_days: parseInt(duration_days) || 30,
      status: status || 'active',
      features: features || null,
      role_granted: role_granted || 'user'
    });

    // Log activity
    await logActivity(
      req,
      'plan_created',
      `Plano ${name} criado`,
      'plan',
      newPlan.id
    );

    req.flash('success_msg', 'Plano adicionado com sucesso');
    res.redirect('/admin/plans');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao adicionar plano');
    res.redirect('/admin/plans/add');
  }
});

// Monitoring dashboard - restricted to full admins only
router.get('/monitoring', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    res.render('admin/monitoring', {
      title: 'Monitoramento do Sistema - CancroSoft TM',
      user: req.session.user
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar monitoramento');
    res.redirect('/admin');
  }
});

// Code review routes
router.use('/code-reviews', require('./admin/codeReview'));

// Version control routes - restricted to full admins only
router.use('/version-control', require('./admin/versionControl'));

// Activity logs - restricted to full admins only
router.get('/logs', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 50;
    const offset = (page - 1) * limit;

    const { count, rows: logs } = await ActivityLog.findAndCountAll({
      include: [
        { model: User, as: 'user', attributes: ['id', 'usuario'] }
      ],
      order: [['id', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/logs', {
      title: 'Logs de Atividade - CancroSoft TM',
      user: req.session.user,
      logs,
      pagination: {
        page,
        totalPages,
        count
      }
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar logs');
    res.redirect('/admin');
  }
});

// Transactions - restricted to full admins only
router.get('/transactions', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 50;
    const offset = (page - 1) * limit;

    const { count, rows: transactions } = await Transaction.findAndCountAll({
      include: [
        { model: User, as: 'user', attributes: ['id', 'usuario'] },
        { model: User, as: 'admin', attributes: ['id', 'usuario'] },
        { model: Checker, as: 'checker', attributes: ['id', 'name'] }
      ],
      order: [['id', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/transactions', {
      title: 'Transações - CancroSoft TM',
      user: req.session.user,
      transactions,
      pagination: {
        page,
        totalPages,
        count
      }
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar transações');
    res.redirect('/admin');
  }
});

// Payment Transactions - Admin only
router.get('/payment-transactions', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 50;
    const offset = (page - 1) * limit;

    const { PaymentTransaction } = require('../models');
    const { count, rows: paymentTransactions } = await PaymentTransaction.findAndCountAll({
      include: [
        { model: User, as: 'user', attributes: ['id', 'usuario', 'email'] },
        { model: Plan, as: 'plan', attributes: ['id', 'name', 'price'] }
      ],
      order: [['id', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    // Buscar transações da API PagFly
    const paymentService = require('../services/paymentService');
    const apiTransactions = await paymentService.listTransactions({
      page: 1,
      pageSize: 20
    });

    res.render('admin/payment-transactions', {
      title: 'Transações de Pagamento - CancroSoft TM',
      user: req.session.user,
      paymentTransactions,
      apiTransactions: apiTransactions.success ? apiTransactions.data : null,
      pagination: {
        page,
        totalPages,
        count
      }
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar transações de pagamento');
    res.redirect('/admin');
  }
});

module.exports = router;
