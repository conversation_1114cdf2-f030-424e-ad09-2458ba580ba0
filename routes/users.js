const express = require('express');
const router = express.Router();
const User = require('../models/User');
const crypto = require('crypto');
const { ensureAuthenticated, ensureAdmin } = require('../middleware/auth');

// Admin: Add new user
router.post('/add', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const { usuario, senha, rank } = req.body;

    // Validate input
    if (!usuario || !senha) {
      req.flash('error_msg', 'Preencha todos os campos');
      return res.redirect('/painel/adicionar');
    }

    // Check if user already exists
    const existingUser = await User.findOne({ where: { usuario } });

    if (existingUser) {
      req.flash('error_msg', 'Usuário já existe');
      return res.redirect('/painel/adicionar');
    }

    // Create new user with bcrypt hash for better security
    const salt = await require('bcryptjs').genSalt(10);
    const hashedPassword = await require('bcryptjs').hash(senha, salt);

    // Generate unique referral code
    const uniqueReferralCode = crypto.randomBytes(4).toString('hex');

    await User.create({
      usuario,
      senha: hashedPassword,
      rank: rank || 0,
      saldo: 0,
      lives: 0,
      criador: req.session.user.usuario,
      referral_code: uniqueReferralCode,
      referred_by: null, // Admin-created users don't have referrers
      role: rank === 1 ? 'admin' : 'user',
      status: 'active'
    });

    req.flash('success_msg', 'Usuário adicionado com sucesso');
    res.redirect('/painel/usuarios');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao adicionar usuário');
    res.redirect('/painel/adicionar');
  }
});

// Admin: Update user
router.post('/update/:id', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const { usuario, senha, rank, saldo } = req.body;
    const userId = req.params.id;

    const user = await User.findByPk(userId);

    if (!user) {
      req.flash('error_msg', 'Usuário não encontrado');
      return res.redirect('/painel/usuarios');
    }

    // Update user
    user.usuario = usuario || user.usuario;

    if (senha) {
      const salt = await require('bcryptjs').genSalt(10);
      user.senha = await require('bcryptjs').hash(senha, salt);
    }

    if (rank !== undefined) {
      user.rank = rank;
    }

    if (saldo !== undefined) {
      user.saldo = saldo;
    }

    await user.save();

    req.flash('success_msg', 'Usuário atualizado com sucesso');
    res.redirect('/painel/usuarios');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao atualizar usuário');
    res.redirect('/painel/usuarios');
  }
});

// Admin: Delete user
router.delete('/delete/:id', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const userId = req.params.id;

    const user = await User.findByPk(userId);

    if (!user) {
      req.flash('error_msg', 'Usuário não encontrado');
      return res.redirect('/painel/usuarios');
    }

    await user.destroy();

    req.flash('success_msg', 'Usuário removido com sucesso');
    res.redirect('/painel/usuarios');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao remover usuário');
    res.redirect('/painel/usuarios');
  }
});

module.exports = router;
