const express = require('express');
const router = express.Router();
const { ensureAuthenticated, ensureAdmin, logActivity } = require('../middleware/auth');
const { User, Plan, Transaction, PaymentTransaction } = require('../models');
const paymentService = require('../services/paymentService');

// Cache para evitar múltiplos pagamentos do mesmo usuário
const paymentCache = new Map();

/**
 * Limpa cache de pagamento expirado
 */
function cleanExpiredCache() {
  const now = Date.now();
  for (const [key, value] of paymentCache.entries()) {
    if (now > value.expires) {
      paymentCache.delete(key);
    }
  }
}

/**
 * Verifica se usuário tem pagamento pendente
 */
function hasActivePendingPayment(userId) {
  cleanExpiredCache();
  const cacheKey = `user_${userId}`;
  return paymentCache.has(cacheKey);
}

/**
 * Adiciona pagamento ao cache
 */
function addToPaymentCache(userId, transactionId) {
  const cacheKey = `user_${userId}`;
  paymentCache.set(cacheKey, {
    transactionId,
    expires: Date.now() + (30 * 60 * 1000) // 30 minutos
  });
}

/**
 * Remove pagamento do cache
 */
function removeFromPaymentCache(userId) {
  const cacheKey = `user_${userId}`;
  paymentCache.delete(cacheKey);
}

// Criar pagamento PIX
router.post('/create-pix', ensureAuthenticated, async (req, res) => {
  try {
    console.log('Dados recebidos para PIX:', req.body);
    const { customAmount, customerName, customerDocument } = req.body;
    const userId = req.session.user.id;

    // Verificar se usuário já tem pagamento pendente
    if (hasActivePendingPayment(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Você já possui um pagamento pendente. Aguarde a conclusão ou expiração.'
      });
    }

    // Validar valor customizado
    if (!customAmount) {
      return res.status(400).json({
        success: false,
        message: 'Valor é obrigatório'
      });
    }

    const amount = parseFloat(customAmount);
    if (amount < 5 || amount > 1000) {
      return res.status(400).json({
        success: false,
        message: 'Valor deve estar entre R$ 5,00 e R$ 1.000,00'
      });
    }

    const credits = Math.floor(amount * 10); // 1 real = 10 créditos

    // Gerar ID único para a transação
    const externalId = paymentService.generateExternalId(userId);

    // Criar transação na API PagFly
    const paymentData = {
      amount: amount,
      externalId: externalId,
      customerName: customerName || req.session.user.usuario,
      customerEmail: req.session.user.email || `${req.session.user.usuario}@privxploit.com`,
      customerDocument: customerDocument,
      description: `Compra de ${credits} créditos - PrivXploit`
    };

    console.log('Dados para PagFly:', paymentData);
    const apiResponse = await paymentService.createPixTransaction(paymentData);
    console.log('Resposta da PagFly:', apiResponse);

    if (!apiResponse.success) {
      return res.status(500).json({
        success: false,
        message: apiResponse.error
      });
    }

    // Salvar transação no banco
    const paymentTransaction = await PaymentTransaction.create({
      user_id: userId,
      plan_id: null, // Não usamos mais planos
      external_id: externalId,
      amount: amount,
      credits: credits,
      payment_method: 'pix',
      status: 'pending',
      payment_url: apiResponse.data.qrCodeText || apiResponse.data.paymentUrl,
      qr_code: apiResponse.data.qrCode,
      provider_id: apiResponse.data.id,
      expires_at: new Date(apiResponse.data.expiresAt || Date.now() + (60 * 60 * 1000))
    });

    // Adicionar ao cache
    addToPaymentCache(userId, paymentTransaction.id);

    // Log da atividade
    await logActivity(
      req,
      'payment_created',
      `Pagamento PIX criado - R$ ${amount.toFixed(2)}`
    );

    res.json({
      success: true,
      data: {
        transactionId: paymentTransaction.id,
        externalId: externalId,
        amount: amount,
        credits: credits,
        paymentUrl: apiResponse.data.paymentUrl,
        qrCode: apiResponse.data.qrCode,
        qrCodeText: apiResponse.data.qrCodeText || apiResponse.data.qrCode || apiResponse.data.paymentUrl,
        expiresAt: paymentTransaction.expires_at
      }
    });

  } catch (error) {
    console.error('Erro ao criar pagamento PIX:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Criar pagamento Crypto
router.post('/create-crypto', ensureAuthenticated, async (req, res) => {
  try {
    const { customAmount, cryptoCurrency, customerName, customerDocument } = req.body;
    const userId = req.session.user.id;

    // Verificar se usuário já tem pagamento pendente
    if (hasActivePendingPayment(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Você já possui um pagamento pendente. Aguarde a conclusão ou expiração.'
      });
    }

    // Validar criptomoeda
    const supportedCryptos = ['BTC', 'ETH', 'USDT', 'LTC', 'BCH'];
    if (!supportedCryptos.includes(cryptoCurrency.toUpperCase())) {
      return res.status(400).json({
        success: false,
        message: 'Criptomoeda não suportada'
      });
    }

    // Validar valor customizado
    if (!customAmount) {
      return res.status(400).json({
        success: false,
        message: 'Valor é obrigatório'
      });
    }

    const amount = parseFloat(customAmount);
    if (amount < 5 || amount > 1000) {
      return res.status(400).json({
        success: false,
        message: 'Valor deve estar entre R$ 5,00 e R$ 1.000,00'
      });
    }

    const credits = Math.floor(amount * 10); // 1 real = 10 créditos

    // Gerar ID único para a transação
    const externalId = paymentService.generateExternalId(userId);

    // Criar transação na API NowPayments
    const paymentData = {
      amount: amount,
      externalId: externalId,
      customerName: customerName || req.session.user.usuario,
      customerEmail: req.session.user.email || `${req.session.user.usuario}@privxploit.com`,
      customerDocument: customerDocument,
      cryptoCurrency: cryptoCurrency.toUpperCase(),
      description: `Compra de ${credits} créditos - PrivXploit`
    };

    const apiResponse = await paymentService.createCryptoTransaction(paymentData);

    if (!apiResponse.success) {
      return res.status(500).json({
        success: false,
        message: apiResponse.error
      });
    }

    // Salvar transação no banco
    const paymentTransaction = await PaymentTransaction.create({
      user_id: userId,
      plan_id: null, // Não usamos mais planos
      external_id: externalId,
      amount: amount,
      credits: credits,
      payment_method: 'crypto',
      crypto_currency: cryptoCurrency.toUpperCase(),
      status: 'pending',
      payment_url: apiResponse.data.paymentUrl,
      provider_id: apiResponse.data.id,
      crypto_amount: apiResponse.data.cryptoAmount,
      usd_amount: apiResponse.data.usdAmount,
      expires_at: new Date(apiResponse.data.expiresAt || Date.now() + (2 * 60 * 60 * 1000))
    });

    // Adicionar ao cache
    addToPaymentCache(userId, paymentTransaction.id);

    // Log da atividade
    await logActivity(
      req,
      'payment_created',
      `Pagamento ${cryptoCurrency} criado - R$ ${amount.toFixed(2)}`
    );

    res.json({
      success: true,
      data: {
        transactionId: paymentTransaction.id,
        externalId: externalId,
        amount: amount,
        credits: credits,
        cryptoCurrency: cryptoCurrency.toUpperCase(),
        paymentUrl: apiResponse.data.paymentUrl,
        cryptoAmount: apiResponse.data.cryptoAmount,
        expiresAt: paymentTransaction.expires_at
      }
    });

  } catch (error) {
    console.error('Erro ao criar pagamento crypto:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Verificar status do pagamento
router.get('/status/:transactionId', ensureAuthenticated, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const userId = req.session.user.id;

    // Buscar transação no banco
    const paymentTransaction = await PaymentTransaction.findOne({
      where: {
        id: transactionId,
        user_id: userId
      }
    });

    if (!paymentTransaction) {
      return res.status(404).json({
        success: false,
        message: 'Transação não encontrada'
      });
    }

    // Se já foi paga, retornar status
    if (paymentTransaction.status === 'paid') {
      return res.json({
        success: true,
        status: 'paid',
        paidAt: paymentTransaction.paid_at
      });
    }

    // Consultar status na API
    const apiResponse = await paymentService.getTransactionStatus(paymentTransaction.external_id);

    if (apiResponse.success) {
      const apiStatus = apiResponse.data.status;

      // Mapear status da API para status do banco
      let mappedStatus = 'pending';
      if (apiStatus === 'paid' || apiStatus === 'approved' || apiStatus === 'completed') {
        mappedStatus = 'paid';
      } else if (apiStatus === 'cancelled' || apiStatus === 'rejected' || apiStatus === 'failed') {
        mappedStatus = 'failed';
      } else if (apiStatus === 'expired') {
        mappedStatus = 'expired';
      } else if (apiStatus === 'waiting_payment' || apiStatus === 'pending') {
        mappedStatus = 'pending';
      }

      // Atualizar status no banco se necessário
      if (mappedStatus !== paymentTransaction.status) {
        paymentTransaction.status = mappedStatus;

        if (mappedStatus === 'paid') {
          paymentTransaction.paid_at = new Date();

          // Processar pagamento aprovado
          await processApprovedPayment(paymentTransaction);

          // Remover do cache
          removeFromPaymentCache(userId);
        }

        await paymentTransaction.save();
      }

      res.json({
        success: true,
        status: mappedStatus,
        paidAt: paymentTransaction.paid_at
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Erro ao consultar status do pagamento'
      });
    }

  } catch (error) {
    console.error('Erro ao verificar status:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Obter moedas disponíveis para crypto
router.get('/crypto-currencies', ensureAuthenticated, async (req, res) => {
  try {
    const result = await paymentService.getAvailableCurrencies();

    if (result.success) {
      // Filtrar apenas as moedas mais populares
      const popularCurrencies = ['btc', 'eth', 'usdt', 'ltc', 'bch', 'ada', 'dot', 'bnb'];
      const availableCurrencies = result.data.filter(currency =>
        popularCurrencies.includes(currency.toLowerCase())
      );

      res.json({
        success: true,
        data: availableCurrencies.map(currency => ({
          code: currency.toUpperCase(),
          name: getCurrencyName(currency.toUpperCase()),
          icon: `fab fa-${currency.toLowerCase()}`
        }))
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Erro ao obter moedas disponíveis'
      });
    }
  } catch (error) {
    console.error('Erro ao obter moedas crypto:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Função auxiliar para obter nome da moeda
function getCurrencyName(code) {
  const names = {
    'BTC': 'Bitcoin',
    'ETH': 'Ethereum',
    'USDT': 'Tether',
    'LTC': 'Litecoin',
    'BCH': 'Bitcoin Cash',
    'ADA': 'Cardano',
    'DOT': 'Polkadot',
    'BNB': 'Binance Coin'
  };
  return names[code] || code;
}

// Webhook para PagFly (PIX)
router.post('/webhook/pagfly', async (req, res) => {
  try {
    const webhookData = req.body;
    console.log('Webhook PagFly recebido:', webhookData);

    // Validar webhook
    const signature = req.headers['x-signature'];
    if (!paymentService.validatePagFlyWebhook(webhookData, signature)) {
      return res.status(401).send('Unauthorized');
    }

    const externalId = webhookData.externalRef;
    const status = webhookData.status;

    // Buscar transação no banco
    const paymentTransaction = await PaymentTransaction.findOne({
      where: { external_id: externalId }
    });

    if (!paymentTransaction) {
      console.error('Transação não encontrada para webhook PagFly:', externalId);
      return res.status(404).send('Transaction not found');
    }

    // Atualizar dados do webhook
    paymentTransaction.webhook_data = webhookData;
    paymentTransaction.provider_id = webhookData.id;

    // Mapear status da PagFly
    let mappedStatus = 'pending';
    if (status === 'paid' || status === 'approved' || status === 'completed') {
      mappedStatus = 'paid';
    } else if (status === 'cancelled' || status === 'rejected' || status === 'failed') {
      mappedStatus = 'failed';
    } else if (status === 'expired') {
      mappedStatus = 'expired';
    } else if (status === 'waiting_payment') {
      mappedStatus = 'pending';
    }

    paymentTransaction.status = mappedStatus;

    if (mappedStatus === 'paid') {
      paymentTransaction.paid_at = new Date(webhookData.paidAt || new Date());
      await processApprovedPayment(paymentTransaction);
      removeFromPaymentCache(paymentTransaction.user_id);
    }

    await paymentTransaction.save();

    res.status(200).send('OK');

  } catch (error) {
    console.error('Erro no webhook PagFly:', error);
    res.status(500).send('Internal Server Error');
  }
});

// Webhook para NowPayments (Crypto)
router.post('/webhook/nowpayments', async (req, res) => {
  try {
    const webhookData = req.body;
    console.log('Webhook NowPayments recebido:', webhookData);

    // Validar webhook
    const signature = req.headers['x-nowpayments-sig'];
    const payload = JSON.stringify(webhookData);
    if (!paymentService.validateNowPaymentsWebhook(payload, signature)) {
      return res.status(401).send('Unauthorized');
    }

    const externalId = webhookData.order_id;
    const paymentStatus = webhookData.payment_status;

    // Buscar transação no banco
    const paymentTransaction = await PaymentTransaction.findOne({
      where: { external_id: externalId }
    });

    if (!paymentTransaction) {
      console.error('Transação não encontrada para webhook NowPayments:', externalId);
      return res.status(404).send('Transaction not found');
    }

    // Atualizar dados do webhook
    paymentTransaction.webhook_data = webhookData;

    // Mapear status do NowPayments
    let status = 'pending';
    if (paymentStatus === 'finished' || paymentStatus === 'confirmed') {
      status = 'paid';
    } else if (paymentStatus === 'failed' || paymentStatus === 'expired') {
      status = 'failed';
    }

    paymentTransaction.status = status;

    if (status === 'paid') {
      paymentTransaction.paid_at = new Date();
      await processApprovedPayment(paymentTransaction);
      removeFromPaymentCache(paymentTransaction.user_id);
    }

    await paymentTransaction.save();

    res.status(200).send('OK');

  } catch (error) {
    console.error('Erro no webhook NowPayments:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * Processa pagamento aprovado
 */
async function processApprovedPayment(paymentTransaction) {
  try {
    // Buscar usuário
    const user = await User.findByPk(paymentTransaction.user_id);
    if (!user) {
      throw new Error('Usuário não encontrado');
    }

    // Adicionar créditos
    const oldBalance = user.saldo;
    user.saldo += paymentTransaction.credits;
    await user.save();

    // Criar registro de transação
    await Transaction.create({
      user_id: paymentTransaction.user_id,
      amount: paymentTransaction.credits,
      type: 'credit',
      description: `Compra de créditos via ${paymentTransaction.payment_method.toUpperCase()} - R$ ${paymentTransaction.amount}`,
      balance_before: oldBalance,
      balance_after: user.saldo,
      reference_id: paymentTransaction.external_id
    });

    console.log(`Pagamento processado: ${paymentTransaction.credits} créditos adicionados ao usuário ${user.usuario}`);

  } catch (error) {
    console.error('Erro ao processar pagamento aprovado:', error);
    throw error;
  }
}

// Admin routes for payment management
router.get('/admin-status/:externalId', async (req, res) => {
  try {
    const { externalId } = req.params;

    // Buscar transação no banco
    const paymentTransaction = await PaymentTransaction.findOne({
      where: { external_id: externalId }
    });

    if (!paymentTransaction) {
      return res.status(404).json({
        success: false,
        message: 'Transação não encontrada'
      });
    }

    // Consultar status na API
    const apiResponse = await paymentService.getTransactionStatus(externalId);

    if (apiResponse.success) {
      const apiStatus = apiResponse.data.status;

      // Mapear status da API para status do banco
      let mappedStatus = 'pending';
      if (apiStatus === 'paid' || apiStatus === 'approved' || apiStatus === 'completed') {
        mappedStatus = 'paid';
      } else if (apiStatus === 'cancelled' || apiStatus === 'rejected' || apiStatus === 'failed') {
        mappedStatus = 'failed';
      } else if (apiStatus === 'expired') {
        mappedStatus = 'expired';
      } else if (apiStatus === 'waiting_payment' || apiStatus === 'pending') {
        mappedStatus = 'pending';
      }

      // Atualizar status no banco se necessário
      if (mappedStatus !== paymentTransaction.status) {
        paymentTransaction.status = mappedStatus;

        if (mappedStatus === 'paid') {
          paymentTransaction.paid_at = new Date();

          // Processar pagamento aprovado
          await processApprovedPayment(paymentTransaction);

          // Remover do cache
          removeFromPaymentCache(paymentTransaction.user_id);
        }

        await paymentTransaction.save();
      }

      res.json({
        success: true,
        status: mappedStatus,
        localStatus: paymentTransaction.status,
        paidAt: paymentTransaction.paid_at
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Erro ao consultar status na API'
      });
    }

  } catch (error) {
    console.error('Erro ao verificar status admin:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Get payment details for admin
router.get('/details/:externalId', async (req, res) => {
  try {
    const { externalId } = req.params;

    // Buscar transação no banco
    const paymentTransaction = await PaymentTransaction.findOne({
      where: { external_id: externalId },
      include: [
        { model: User, as: 'user', attributes: ['id', 'usuario', 'email'] },
        { model: Plan, as: 'plan', attributes: ['id', 'name', 'price'] }
      ]
    });

    if (!paymentTransaction) {
      return res.status(404).json({
        success: false,
        message: 'Transação não encontrada'
      });
    }

    // Consultar detalhes na API
    const apiResponse = await paymentService.getTransactionStatus(externalId);

    res.json({
      success: true,
      data: {
        local: paymentTransaction,
        api: apiResponse.success ? apiResponse.data : null
      }
    });

  } catch (error) {
    console.error('Erro ao buscar detalhes:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Test API connection
router.get('/test-api', async (req, res) => {
  try {
    // Testar listagem de transações
    const apiResponse = await paymentService.listTransactions({
      page: 1,
      pageSize: 5
    });

    res.json({
      success: true,
      message: 'Conexão com API PagFly testada com sucesso',
      data: apiResponse
    });

  } catch (error) {
    console.error('Erro ao testar API:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao conectar com API PagFly',
      error: error.message
    });
  }
});

module.exports = router;
