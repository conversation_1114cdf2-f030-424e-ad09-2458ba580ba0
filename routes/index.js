const express = require('express');
const router = express.Router();
const { User } = require('../models');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const { Op } = require('sequelize');
const { logActivity } = require('../middleware/auth');

// Home page - Login form
router.get('/', (req, res) => {
  // If user is already logged in, redirect to dashboard
  if (req.session.user) {
    return res.redirect('/painel');
  }

  res.render('index', {
    title: 'PRIVXPLOIT',
    error: req.flash('error'),
    success: req.flash('success')
  });
});

// Login process
router.post('/login', async (req, res) => {
  try {
    const { usuario, senha } = req.body;

    // Validate input
    if (!usuario || !senha) {
      req.flash('error', 'Por favor, preencha todos os campos');
      return res.redirect('/');
    }

    // Find user
    const user = await User.findOne({ where: { usuario } });

    if (!user) {
      req.flash('error', 'Usuário ou Senha Incorretos!');
      return res.redirect('/');
    }

    // Check if account is active
    if (user.status !== 'active') {
      req.flash('error', 'Sua conta foi suspensa ou desativada. Entre em contato com o suporte.');
      return res.redirect('/');
    }

    // Check password (MD5 for compatibility with old system)
    const md5Hash = crypto.createHash('md5').update(senha).digest('hex');

    let isValidPassword = false;

    // First try direct MD5 comparison (for backward compatibility)
    if (user.senha === md5Hash) {
      isValidPassword = true;
    } else {
      // Then try bcrypt comparison (for newer accounts)
      try {
        isValidPassword = await user.validPassword(senha);
      } catch (error) {
        console.error('Password validation error:', error);
      }
    }

    if (isValidPassword) {
      // Update last login time
      user.lastLogin = new Date();
      await user.save();

      // Set session
      req.session.user = {
        id: user.id,
        rank: user.rank,
        role: user.role,
        saldo: user.saldo,
        lives: user.lives,
        usuario: user.usuario,
        status: user.status
      };

      // Log login activity
      await logActivity(req, 'login', 'Login realizado com sucesso');

      req.flash('success', 'Login realizado com sucesso!');
      return res.redirect('/painel');
    } else {
      // Log failed login attempt
      await logActivity(
        { ip: req.ip, headers: req.headers },
        'login_failed',
        `Tentativa de login falha para o usuário: ${usuario}`,
        'user',
        user.id
      );

      req.flash('error', 'Usuário ou Senha Incorretos!');
      return res.redirect('/');
    }
  } catch (err) {
    console.error(err);
    req.flash('error', 'Erro no servidor');
    return res.redirect('/');
  }
});

// Logout
router.get('/logout', async (req, res) => {
  if (req.session.user) {
    // Log logout activity
    await logActivity(req, 'logout', 'Logout realizado');
  }

  req.session.destroy();
  res.redirect('/');
});

// Registration page
router.get('/register', (req, res) => {
  // If user is already logged in, redirect to dashboard
  if (req.session.user) {
    return res.redirect('/painel');
  }

  // Get referral code from query parameter
  const referralCode = req.query.ref || '';

  res.render('register', {
    title: 'Registrar - CancroSoft TM',
    error: req.flash('error'),
    success: req.flash('success'),
    referralCode
  });
});

// Registration process
router.post('/register', async (req, res) => {
  try {
    const { usuario, senha, email, referralCode } = req.body;

    // Validate input
    if (!usuario || !senha) {
      req.flash('error', 'Por favor, preencha todos os campos obrigatórios');
      return res.redirect('/register');
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { usuario },
          { email: email || null }
        ]
      }
    });

    if (existingUser) {
      req.flash('error', 'Usuário ou email já existe');
      return res.redirect('/register');
    }

    // Generate unique referral code
    const uniqueReferralCode = crypto.randomBytes(4).toString('hex');

    // Find referrer if referral code was provided
    let referrerId = null;
    if (referralCode) {
      const referrer = await User.findOne({
        where: {
          [Op.or]: [
            { referral_code: referralCode },
            { id: isNaN(referralCode) ? 0 : parseInt(referralCode) }
          ]
        }
      });

      if (referrer) {
        referrerId = referrer.id;
      }
    }

    // Create new user with bcrypt hash
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(senha, salt);

    const newUser = await User.create({
      usuario,
      senha: hashedPassword,
      email: email || null,
      rank: 0,
      role: 'user',
      saldo: 0,
      lives: 0,
      criador: referrerId ? 'referral' : 'self-register',
      referral_code: uniqueReferralCode,
      referred_by: referrerId,
      status: 'active'
    });

    // Log activity
    await logActivity(
      req,
      'user_registered',
      `Usuário ${usuario} registrado${referrerId ? ' via referral' : ''}`,
      'user',
      newUser.id
    );

    req.flash('success', 'Registro realizado com sucesso! Faça login com suas credenciais.');
    res.redirect('/');
  } catch (err) {
    console.error(err);
    req.flash('error', 'Erro ao registrar usuário');
    res.redirect('/register');
  }
});

// Forgot password
router.get('/forgot-password', (req, res) => {
  res.render('forgot-password', {
    title: 'Recuperar Senha - CancroSoft TM',
    error: req.flash('error'),
    success: req.flash('success')
  });
});

// Process forgot password
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      req.flash('error', 'Por favor, informe seu email');
      return res.redirect('/forgot-password');
    }

    // Find user by email
    const user = await User.findOne({ where: { email } });

    if (!user) {
      // Don't reveal that the email doesn't exist
      req.flash('success', 'Se o email estiver cadastrado, você receberá instruções para redefinir sua senha');
      return res.redirect('/forgot-password');
    }

    // Generate reset token
    const token = crypto.randomBytes(20).toString('hex');

    // Set token and expiration
    user.resetPasswordToken = token;
    user.resetPasswordExpires = Date.now() + 3600000; // 1 hour
    await user.save();

    // In a real application, send an email with the reset link
    // For now, just show a success message with the token
    console.log('Reset token:', token);

    req.flash('success', 'Instruções para redefinir sua senha foram enviadas para seu email');
    res.redirect('/forgot-password');
  } catch (err) {
    console.error(err);
    req.flash('error', 'Erro ao processar solicitação');
    res.redirect('/forgot-password');
  }
});

// Reset password form
router.get('/reset-password/:token', async (req, res) => {
  try {
    const { token } = req.params;

    // Find user with valid token
    const user = await User.findOne({
      where: {
        resetPasswordToken: token,
        resetPasswordExpires: { [require('sequelize').Op.gt]: Date.now() }
      }
    });

    if (!user) {
      req.flash('error', 'Token de redefinição de senha inválido ou expirado');
      return res.redirect('/forgot-password');
    }

    res.render('reset-password', {
      title: 'Redefinir Senha - CancroSoft TM',
      token,
      error: req.flash('error'),
      success: req.flash('success')
    });
  } catch (err) {
    console.error(err);
    req.flash('error', 'Erro ao processar solicitação');
    res.redirect('/forgot-password');
  }
});

// Process reset password
router.post('/reset-password/:token', async (req, res) => {
  try {
    const { token } = req.params;
    const { password, confirmPassword } = req.body;

    if (password !== confirmPassword) {
      req.flash('error', 'As senhas não coincidem');
      return res.redirect(`/reset-password/${token}`);
    }

    // Find user with valid token
    const user = await User.findOne({
      where: {
        resetPasswordToken: token,
        resetPasswordExpires: { [require('sequelize').Op.gt]: Date.now() }
      }
    });

    if (!user) {
      req.flash('error', 'Token de redefinição de senha inválido ou expirado');
      return res.redirect('/forgot-password');
    }

    // Hash password
    const salt = await require('bcryptjs').genSalt(10);
    user.senha = await require('bcryptjs').hash(password, salt);
    user.resetPasswordToken = null;
    user.resetPasswordExpires = null;
    await user.save();

    // Log activity
    await logActivity(
      { ip: req.ip, headers: req.headers },
      'password_reset',
      'Senha redefinida com sucesso',
      'user',
      user.id
    );

    req.flash('success', 'Senha redefinida com sucesso. Faça login com sua nova senha.');
    res.redirect('/');
  } catch (err) {
    console.error(err);
    req.flash('error', 'Erro ao processar solicitação');
    res.redirect('/forgot-password');
  }
});

module.exports = router;
