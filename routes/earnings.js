const express = require('express');
const router = express.Router();
const { ensureAuthenticated, ensureAffiliate, ensureProgrammer, ensureAdmin } = require('../middleware/auth');
const { User, Checker, Transaction, Earning } = require('../models');
const { Op } = require('sequelize');

// View earnings
router.get('/', ensureAuthenticated, async (req, res) => {
  try {
    // Get user's earnings
    const earnings = await Earning.findAll({
      where: { user_id: req.session.user.id },
      include: [
        { model: Checker, as: 'checker', attributes: ['id', 'name', 'title'] },
        { model: Transaction, as: 'transaction' }
      ],
      order: [['created_at', 'DESC']]
    });

    // Calculate total earnings
    const totalEarnings = earnings.reduce((total, earning) => total + earning.amount, 0);

    // Group earnings by source type
    const programmerEarnings = earnings.filter(e => e.source_type === 'programmer');
    const affiliateEarnings = earnings.filter(e => e.source_type === 'affiliate');
    const siteEarnings = earnings.filter(e => e.source_type === 'site');

    // Calculate totals by source type
    const totalProgrammerEarnings = programmerEarnings.reduce((total, earning) => total + earning.amount, 0);
    const totalAffiliateEarnings = affiliateEarnings.reduce((total, earning) => total + earning.amount, 0);
    const totalSiteEarnings = siteEarnings.reduce((total, earning) => total + earning.amount, 0);

    res.render('earnings/index', {
      title: 'Meus Ganhos - CancroSoft TM',
      user: req.session.user,
      earnings,
      totalEarnings,
      programmerEarnings,
      affiliateEarnings,
      siteEarnings,
      totalProgrammerEarnings,
      totalAffiliateEarnings,
      totalSiteEarnings
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar ganhos');
    res.redirect('/painel');
  }
});

// View referrals
router.get('/referrals', ensureAuthenticated, async (req, res) => {
  try {
    // Get user's referrals
    const referrals = await User.findAll({
      where: { referred_by: req.session.user.id },
      attributes: ['id', 'usuario', 'createdAt', 'saldo', 'lives']
    });

    // Get earnings from referrals
    const referralEarnings = await Earning.findAll({
      where: {
        user_id: req.session.user.id,
        source_type: 'affiliate'
      },
      include: [
        { model: Transaction, as: 'transaction', include: [{ model: User, as: 'user' }] }
      ],
      order: [['created_at', 'DESC']]
    });

    // Calculate total earnings from referrals
    const totalReferralEarnings = referralEarnings.reduce((total, earning) => total + earning.amount, 0);

    // Generate referral link
    const referralCode = req.session.user.referral_code || req.session.user.id;
    const referralLink = `${req.protocol}://${req.get('host')}/register?ref=${referralCode}`;

    res.render('earnings/referrals', {
      title: 'Meus Referidos - CancroSoft TM',
      user: req.session.user,
      referrals,
      referralEarnings,
      totalReferralEarnings,
      referralLink
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar referidos');
    res.redirect('/painel');
  }
});

// Admin: View all earnings - accessible by admins and programmers
router.get('/admin', ensureAuthenticated, ensureProgrammer, async (req, res) => {

  try {
    // Get all earnings
    const earnings = await Earning.findAll({
      include: [
        { model: User, as: 'user', attributes: ['id', 'usuario'] },
        { model: Checker, as: 'checker', attributes: ['id', 'name', 'title'] },
        { model: Transaction, as: 'transaction' }
      ],
      order: [['created_at', 'DESC']]
    });

    // Calculate total earnings
    const totalEarnings = earnings.reduce((total, earning) => total + earning.amount, 0);

    // Group earnings by source type
    const programmerEarnings = earnings.filter(e => e.source_type === 'programmer');
    const affiliateEarnings = earnings.filter(e => e.source_type === 'affiliate');
    const siteEarnings = earnings.filter(e => e.source_type === 'site');

    // Calculate totals by source type
    const totalProgrammerEarnings = programmerEarnings.reduce((total, earning) => total + earning.amount, 0);
    const totalAffiliateEarnings = affiliateEarnings.reduce((total, earning) => total + earning.amount, 0);
    const totalSiteEarnings = siteEarnings.reduce((total, earning) => total + earning.amount, 0);

    res.render('admin/earnings', {
      title: 'Todos os Ganhos - CancroSoft TM',
      user: req.session.user,
      earnings,
      totalEarnings,
      programmerEarnings,
      affiliateEarnings,
      siteEarnings,
      totalProgrammerEarnings,
      totalAffiliateEarnings,
      totalSiteEarnings
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar ganhos');
    res.redirect('/admin');
  }
});

module.exports = router;
