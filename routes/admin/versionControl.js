const express = require('express');
const router = express.Router();
const { exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const { ensureAuthenticated, ensureFullAdmin } = require('../../middleware/auth');

// GitHub repository configuration
const GITHUB_REPO = 'MatxCoder/privxploit';
const GITHUB_URL = `https://github.com/${GITHUB_REPO}`;
const PROJECT_ROOT = process.cwd();

console.log('[VERSION CONTROL] Configuração inicial:');
console.log('  - GITHUB_REPO:', GITHUB_REPO);
console.log('  - PROJECT_ROOT:', PROJECT_ROOT);
console.log('  - __dirname:', __dirname);

// GitHub API configuration
const GITHUB_API_URL = `https://api.github.com/repos/${GITHUB_REPO}`;

// Get GitHub token from database or environment
const getGitHubToken = async () => {
  try {
    const { Setting } = require('../../models');
    const tokenSetting = await Setting.findOne({ where: { key: 'github_token' } });
    return tokenSetting ? tokenSetting.value : (process.env.GITHUB_TOKEN || null);
  } catch (error) {
    return process.env.GITHUB_TOKEN || null;
  }
};

// Validate GitHub configuration
const validateGitHubConfig = async () => {
  try {
    const token = await getGitHubToken();

    if (!token || token === 'your_github_personal_access_token_here') {
      console.log('⚠️  GitHub Token não configurado para controle de versão.');
      console.log('   Configure um token em: /admin/version-control');
      return false;
    }

    console.log('✅ GitHub Token configurado para controle de versão.');
    return true;
  } catch (error) {
    console.log('❌ Erro ao validar configuração GitHub:', error.message);
    return false;
  }
};

// Helper function to execute shell commands
const execCommand = (command, options = {}) => {
  return new Promise((resolve, reject) => {
    const execOptions = {
      cwd: PROJECT_ROOT,
      timeout: 30000, // 30 second timeout
      maxBuffer: 1024 * 1024, // 1MB buffer
      ...options
    };

    console.log(`[VERSION CONTROL] Executando comando: ${command} em ${execOptions.cwd}`);

    exec(command, execOptions, (error, stdout, stderr) => {
      if (error) {
        console.error(`[VERSION CONTROL] Erro no comando "${command}":`, {
          code: error.code,
          signal: error.signal,
          stdout: stdout?.trim(),
          stderr: stderr?.trim()
        });
        reject({ error, stdout, stderr });
      } else {
        console.log(`[VERSION CONTROL] Comando "${command}" executado com sucesso`);
        resolve({ stdout, stderr });
      }
    });
  });
};

// Get current version info
const getCurrentVersion = async () => {
  try {
    // Check if we're in a git repository first
    try {
      await execCommand('git rev-parse --git-dir');
    } catch (gitError) {
      throw new Error('Não é um repositório Git válido. Execute "git init" para inicializar.');
    }

    const { stdout: currentBranch } = await execCommand('git branch --show-current').catch(() => ({ stdout: 'main' }));
    const { stdout: currentCommit } = await execCommand('git rev-parse HEAD');
    const { stdout: lastCommitMsg } = await execCommand('git log -1 --pretty=%B');
    const { stdout: lastCommitDate } = await execCommand('git log -1 --pretty=%cd --date=iso');
    const { stdout: status } = await execCommand('git status --porcelain');

    return {
      branch: currentBranch.trim() || 'main',
      commit: currentCommit.trim().substring(0, 7),
      fullCommit: currentCommit.trim(),
      lastMessage: lastCommitMsg.trim(),
      lastDate: new Date(lastCommitDate.trim()),
      hasChanges: status.trim().length > 0,
      status: status.trim()
    };
  } catch (error) {
    console.error('Erro detalhado em getCurrentVersion:', error);
    throw new Error(`Erro ao obter informações da versão atual: ${error.message}`);
  }
};

// Get remote version info using GitHub API
const getRemoteVersionAPI = async () => {
  try {
    const https = require('https');
    const url = `${GITHUB_API_URL}/commits/main`;

    const options = {
      headers: {
        'User-Agent': 'PrivXploit-Version-Control',
        'Accept': 'application/vnd.github.v3+json'
      }
    };

    // Add authorization if token is available
    const GITHUB_TOKEN = await getGitHubToken();
    if (GITHUB_TOKEN) {
      options.headers['Authorization'] = `token ${GITHUB_TOKEN}`;
    } else {
      console.log('⚠️  GitHub Token não configurado. Configure GITHUB_TOKEN no .env ou no painel admin.');
    }

    return new Promise((resolve, reject) => {
      const req = https.get(url, options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            if (res.statusCode === 200) {
              const commit = JSON.parse(data);
              resolve({
                commit: commit.sha.substring(0, 7),
                fullCommit: commit.sha,
                lastMessage: commit.commit.message,
                lastDate: new Date(commit.commit.committer.date),
                behindCount: 0 // Will be calculated later
              });
            } else if (res.statusCode === 401) {
              reject(new Error('🔐 Acesso negado ao GitHub. Configure um GitHub Personal Access Token válido no painel admin ou no arquivo .env.'));
            } else if (res.statusCode === 404) {
              reject(new Error('📂 Repositório não encontrado ou sem acesso. Verifique se o token tem permissões para o repositório MatxCoder/privxploit.'));
            } else {
              reject(new Error(`🌐 GitHub API retornou status ${res.statusCode}. Verifique sua conexão e configurações.`));
            }
          } catch (parseError) {
            reject(new Error('Erro ao processar resposta da GitHub API'));
          }
        });
      });

      req.on('error', (error) => {
        reject(new Error(`Erro de conexão com GitHub API: ${error.message}`));
      });

      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('Timeout na conexão com GitHub API'));
      });
    });
  } catch (error) {
    throw new Error(`Erro na GitHub API: ${error.message}`);
  }
};

// Configure Git remote URL with token
const configureGitAuth = async () => {
  try {
    const token = await getGitHubToken();
    if (token) {
      // Configure remote URL with token
      const authUrl = `https://MatxCoder:${token}@github.com/MatxCoder/privxploit.git`;
      await execCommand(`git remote set-url origin "${authUrl}"`);

      // Configure Git to not prompt for credentials
      await execCommand('git config credential.helper store');
      await execCommand('git config core.askpass ""');

      console.log('[VERSION CONTROL] Git configurado com token de autenticação');
      return true;
    } else {
      console.log('[VERSION CONTROL] Token GitHub não encontrado. Configure no painel admin.');
      return false;
    }
  } catch (error) {
    console.error('[VERSION CONTROL] Erro ao configurar autenticação Git:', error.message);
    return false;
  }
};

// Initialize Git configuration on module load
(async () => {
  try {
    await configureGitAuth();
  } catch (error) {
    console.error('[VERSION CONTROL] Erro na inicialização:', error.message);
  }
})();

// Get remote version info using Git (fallback)
const getRemoteVersionGit = async () => {
  try {
    // Check if we have a remote configured
    const { stdout: remoteUrl } = await execCommand('git remote get-url origin').catch(() => ({ stdout: '' }));
    if (!remoteUrl.trim()) {
      throw new Error('Repositório remoto não configurado');
    }

    // Configure authentication if token is available
    await configureGitAuth();

    // Try to fetch
    try {
      await execCommand('git fetch origin');
    } catch (fetchError) {
      // If authentication fails, provide helpful message
      if (fetchError.stderr && fetchError.stderr.includes('Authentication failed')) {
        throw new Error('Falha na autenticação. Configure credenciais Git ou GITHUB_TOKEN no .env');
      }
      throw new Error(`Erro ao fazer fetch: ${fetchError.stderr || fetchError.message}`);
    }

    // Check if origin/main exists
    const { stdout: remoteBranches } = await execCommand('git branch -r');
    if (!remoteBranches.includes('origin/main')) {
      throw new Error('Branch origin/main não encontrada');
    }

    const { stdout: remoteCommit } = await execCommand('git rev-parse origin/main');
    const { stdout: remoteMsg } = await execCommand('git log origin/main -1 --pretty=%B');
    const { stdout: remoteDate } = await execCommand('git log origin/main -1 --pretty=%cd --date=iso');
    const { stdout: behindCount } = await execCommand('git rev-list --count HEAD..origin/main');

    return {
      commit: remoteCommit.trim().substring(0, 7),
      fullCommit: remoteCommit.trim(),
      lastMessage: remoteMsg.trim(),
      lastDate: new Date(remoteDate.trim()),
      behindCount: parseInt(behindCount.trim()) || 0
    };
  } catch (error) {
    throw new Error(`Git: ${error.message}`);
  }
};

// Get remote version info (tries API first, then Git)
const getRemoteVersion = async () => {
  try {
    // Try GitHub API first
    const remoteInfo = await getRemoteVersionAPI();

    // Calculate behind count using Git if possible
    try {
      const currentVersion = await getCurrentVersion();
      if (currentVersion.fullCommit !== remoteInfo.fullCommit) {
        // Configure authentication and try to get accurate count
        await configureGitAuth();
        await execCommand('git fetch origin').catch(() => {});
        const { stdout: behindCount } = await execCommand('git rev-list --count HEAD..origin/main').catch(() => ({ stdout: '1' }));
        remoteInfo.behindCount = parseInt(behindCount.trim()) || 1;
      }
    } catch (e) {
      // If can't calculate, assume there's an update
      remoteInfo.behindCount = 1;
    }

    return remoteInfo;
  } catch (apiError) {
    console.log('GitHub API failed, trying Git method:', apiError.message);

    // Fallback to Git method
    try {
      return await getRemoteVersionGit();
    } catch (gitError) {
      throw new Error(`${apiError.message}`);
    }
  }
};

// Get commit history
const getCommitHistory = async (limit = 10) => {
  try {
    const { stdout } = await execCommand(`git log --oneline -${limit} --pretty=format:"%h|%s|%an|%cd" --date=iso`);
    return stdout.trim().split('\n').map(line => {
      const [hash, message, author, date] = line.split('|');
      return {
        hash,
        message,
        author,
        date: new Date(date)
      };
    });
  } catch (error) {
    return [];
  }
};

// Version Control Dashboard
router.get('/', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    console.log('[VERSION CONTROL] Iniciando carregamento do dashboard...');

    // Try to get current version first
    let currentVersion = null;
    try {
      currentVersion = await getCurrentVersion();
      console.log('[VERSION CONTROL] Versão atual obtida com sucesso');
    } catch (currentError) {
      console.error('[VERSION CONTROL] Erro ao obter versão atual:', currentError.message);
      // Continue with null currentVersion
    }

    // Try to get remote version
    let remoteVersion = null;
    try {
      remoteVersion = await getRemoteVersion();
      console.log('[VERSION CONTROL] Versão remota obtida com sucesso');
    } catch (remoteError) {
      console.error('[VERSION CONTROL] Erro ao obter versão remota:', remoteError.message);
      // Continue with null remoteVersion
    }

    // Try to get commit history
    let commitHistory = [];
    try {
      commitHistory = await getCommitHistory(15);
      console.log('[VERSION CONTROL] Histórico de commits obtido com sucesso');
    } catch (historyError) {
      console.error('[VERSION CONTROL] Erro ao obter histórico:', historyError.message);
      // Continue with empty array
    }

    const updateAvailable = currentVersion && remoteVersion ?
      currentVersion.fullCommit !== remoteVersion.fullCommit : false;

    res.render('admin/version-control/index', {
      title: 'Controle de Versão - PrivXploit',
      user: req.session.user,
      currentVersion: currentVersion || {
        branch: 'unknown',
        commit: 'unknown',
        fullCommit: 'unknown',
        lastMessage: 'Erro ao obter informações',
        lastDate: new Date(),
        hasChanges: false,
        status: ''
      },
      remoteVersion: remoteVersion || {
        commit: 'unknown',
        fullCommit: 'unknown',
        lastMessage: 'Erro ao obter informações',
        lastDate: new Date(),
        behindCount: 0
      },
      commitHistory,
      updateAvailable,
      githubRepo: GITHUB_REPO,
      githubUrl: GITHUB_URL,
      hasErrors: !currentVersion || !remoteVersion
    });
  } catch (error) {
    console.error('Erro no controle de versão:', error);
    req.flash('error_msg', 'Erro ao carregar informações de versão: ' + error.message);
    res.redirect('/admin');
  }
});

// Check for updates
router.post('/check-updates', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const [currentVersion, remoteVersion] = await Promise.all([
      getCurrentVersion(),
      getRemoteVersion()
    ]);

    const updateAvailable = currentVersion.fullCommit !== remoteVersion.fullCommit;

    res.json({
      success: true,
      updateAvailable,
      current: currentVersion,
      remote: remoteVersion,
      behindCount: remoteVersion.behindCount
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Pull updates from GitHub
router.post('/pull-updates', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const currentVersion = await getCurrentVersion();

    if (currentVersion.hasChanges) {
      return res.json({
        success: false,
        error: 'Existem alterações locais não commitadas. Faça backup ou commit antes de atualizar.'
      });
    }

    // Configure authentication before pulling
    await configureGitAuth();

    // Pull updates
    const { stdout } = await execCommand('git pull origin main');

    // Get new version info
    const newVersion = await getCurrentVersion();

    res.json({
      success: true,
      message: 'Projeto atualizado com sucesso!',
      output: stdout,
      oldCommit: currentVersion.commit,
      newCommit: newVersion.commit
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.error ? error.error.message : error.message,
      output: error.stderr || error.stdout
    });
  }
});

// Restart application
router.post('/restart-app', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    // Try PM2 restart first
    try {
      await execCommand('pm2 restart all');
      res.json({
        success: true,
        message: 'Aplicação reiniciada com PM2'
      });
    } catch (pm2Error) {
      // If PM2 fails, try to restart the process
      res.json({
        success: true,
        message: 'Reinicialização solicitada. A aplicação será reiniciada em alguns segundos.',
        warning: 'PM2 não disponível, usando reinicialização manual'
      });

      // Restart after sending response
      setTimeout(() => {
        process.exit(0);
      }, 2000);
    }
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Backup current version
router.post('/backup', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `backup-${timestamp}`;

    // Create backup using git
    await execCommand(`git tag ${backupName}`);

    res.json({
      success: true,
      message: `Backup criado: ${backupName}`,
      backupName
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Get system status
router.get('/status', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const [currentVersion, remoteVersion] = await Promise.all([
      getCurrentVersion(),
      getRemoteVersion()
    ]);

    // Check if PM2 is running
    let pm2Status = 'Não disponível';
    try {
      const { stdout } = await execCommand('pm2 status');
      pm2Status = stdout.includes('online') ? 'Online' : 'Offline';
    } catch (e) {
      pm2Status = 'Não instalado';
    }

    // Check Node.js version
    const { stdout: nodeVersion } = await execCommand('node --version');

    res.json({
      success: true,
      status: {
        git: {
          current: currentVersion,
          remote: remoteVersion,
          updateAvailable: currentVersion.fullCommit !== remoteVersion.fullCommit
        },
        system: {
          nodeVersion: nodeVersion.trim(),
          pm2Status,
          uptime: process.uptime()
        }
      }
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Test GitHub API Key
router.post('/test-github-token', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.json({
        success: false,
        error: 'Token é obrigatório'
      });
    }

    // Test token by making API call
    const https = require('https');
    const testUrl = `${GITHUB_API_URL}`;

    const options = {
      headers: {
        'User-Agent': 'PrivXploit-Version-Control',
        'Accept': 'application/vnd.github.v3+json',
        'Authorization': `token ${token}`
      }
    };

    const testResult = await new Promise((resolve, reject) => {
      const req = https.get(testUrl, options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            if (res.statusCode === 200) {
              const repoData = JSON.parse(data);
              resolve({
                success: true,
                repository: {
                  name: repoData.full_name,
                  private: repoData.private,
                  permissions: repoData.permissions || {},
                  owner: repoData.owner.login
                }
              });
            } else if (res.statusCode === 401) {
              resolve({
                success: false,
                error: 'Token inválido ou sem permissões'
              });
            } else if (res.statusCode === 404) {
              resolve({
                success: false,
                error: 'Repositório não encontrado ou sem acesso'
              });
            } else {
              resolve({
                success: false,
                error: `Erro HTTP ${res.statusCode}`
              });
            }
          } catch (parseError) {
            resolve({
              success: false,
              error: 'Erro ao processar resposta da API'
            });
          }
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: `Erro de conexão: ${error.message}`
        });
      });

      req.setTimeout(10000, () => {
        req.destroy();
        resolve({
          success: false,
          error: 'Timeout na conexão'
        });
      });
    });

    res.json(testResult);
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Save GitHub API Key
router.post('/save-github-token', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.json({
        success: false,
        error: 'Token é obrigatório'
      });
    }

    // First test the token
    const testResponse = await new Promise((resolve) => {
      const https = require('https');
      const testUrl = `${GITHUB_API_URL}`;

      const options = {
        headers: {
          'User-Agent': 'PrivXploit-Version-Control',
          'Accept': 'application/vnd.github.v3+json',
          'Authorization': `token ${token}`
        }
      };

      const req = https.get(testUrl, options, (res) => {
        res.on('data', () => {});
        res.on('end', () => {
          resolve({ success: res.statusCode === 200 });
        });
      });

      req.on('error', () => {
        resolve({ success: false });
      });

      req.setTimeout(10000, () => {
        req.destroy();
        resolve({ success: false });
      });
    });

    if (!testResponse.success) {
      return res.json({
        success: false,
        error: 'Token inválido ou sem acesso ao repositório'
      });
    }

    // Save token to database
    const { Setting } = require('../../models');
    await Setting.upsert({
      key: 'github_token',
      value: token,
      description: 'GitHub Personal Access Token para controle de versão'
    });

    res.json({
      success: true,
      message: 'Token GitHub salvo com sucesso!'
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Get GitHub token status
router.get('/github-token-status', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const token = await getGitHubToken();

    res.json({
      success: true,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 8)}...` : null
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Remove GitHub token
router.post('/remove-github-token', ensureAuthenticated, ensureFullAdmin, async (req, res) => {
  try {
    const { Setting } = require('../../models');
    await Setting.destroy({ where: { key: 'github_token' } });

    res.json({
      success: true,
      message: 'Token GitHub removido com sucesso!'
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Export validation function for use in other modules
router.validateGitHubConfig = validateGitHubConfig;

module.exports = router;
