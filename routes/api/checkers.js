const express = require('express');
const router = express.Router();
const axios = require('axios');
const { User, Checker, Transaction, Earning } = require('../../models');
const { ensureAuthenticated, logActivity } = require('../../middleware/auth');
const { checkerMiddleware, simpleCheckerMiddleware } = require('../../middleware/checkerMiddleware');

// As funções distributeEarnings e debitBalance agora estão no middleware/checkerMiddleware.js

// BIN Checker
router.get('/bin', checkerMiddleware('bin'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Make request to BIN API
    const response = await axios.get(`https://bin-checker.net/api/${lista}`);
    const data = response.data;

    const result = {
      bandeira: data.scheme || 'NÃO LOCALIZADO',
      tipo: data.type || 'NÃO LOCALIZADO',
      nivel: data.level || 'NÃO LOCALIZADO',
      pais: data.country?.code || 'NÃO LOCALIZADO',
      site: data.bank?.website || 'NÃO LOCALIZADO',
      numero: data.bank?.phone || 'NÃO LOCALIZADO'
    };

    // Determine if this is a "live" (successful result)
    const isLive = data.scheme && data.scheme !== 'NÃO LOCALIZADO';

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isLive);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    }

    // Log activity
    await logActivity(
      req,
      'checker_used',
      `Checker BIN utilizado: ${lista}`,
      'checker',
      req.checker.id
    );

    let output = `
      <font color='#00FFFF'>BIN LOCALIZADA!</font> <br>
      <font color='yellow'> BANDEIRA:</font> </font> <font color='red'>${result.bandeira}</font> <br>
      <font color='yellow'> TIPO:</font> </font> <font color='red'>${result.tipo}</font> <br>
      <font color='yellow'> NÍVEL:</font> </font> <font color='red'>${result.nivel}</font> <br>
      <font color='yellow'> PAÍS:</font> </font> <font color='red'>${result.pais}</font> <br>
      <font color='yellow'> SITE DO BANCO:</font> </font> <font color='red'>${result.site}</font> <br>
      <font color='yellow'> NÚMERO DO BANCO:</font> </font> <font color='red'>${result.numero}</font> <br>
      <font color='#00FFFF'>SPLIT CHKS</font> <br>
    `;

    res.send(output);
  } catch (err) {
    console.error(err);
    res.status(500).send('BIN INVÁLIDA');
  }
});

// CPF Checker
router.get('/cpf', simpleCheckerMiddleware('cpf'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Extract CPF from lista
    const cpf = lista.split('|')[0];

    // Make request to CPF API (mocked for example)
    try {
      const response = await axios.get(`http://20.195.186.42/xyzcpf.php?lista=${cpf}`);
      const data = response.data;

      // Process response and format output similar to the original
      let output = `
        <font color='#00FFFF'>CPF LOCALIZADO!</font> <br>
        <font color='white'>• CPF:</font> <font color='yellow'>${data.cpf || cpf}</font> <br>
        <font color='white'>• NOME:</font> <font color='yellow'>${data.nome || 'NÃO LOCALIZADO'}</font> <br>
        <font color='white'>• NASCIMENTO:</font> <font color='yellow'>${data.dataNascimento || 'NÃO LOCALIZADO'}</font> <br>
        <font color='white'>• MÃE:</font> <font color='yellow'>${data.nomeMae || 'NÃO LOCALIZADO'}</font> <br>
        <font color='white'>• PAI:</font> <font color='yellow'>${data.nomePai || 'NÃO LOCALIZADO'}</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } catch (error) {
      // If API fails, return a generic response
      let output = `
        <font color='#00FFFF'>CPF LOCALIZADO!</font> <br>
        <font color='white'>• CPF:</font> <font color='yellow'>${cpf}</font> <br>
        <font color='white'>• NOME:</font> <font color='yellow'>NÃO LOCALIZADO</font> <br>
        <font color='white'>• NASCIMENTO:</font> <font color='yellow'>NÃO LOCALIZADO</font> <br>
        <font color='white'>• MÃE:</font> <font color='yellow'>NÃO LOCALIZADO</font> <br>
        <font color='white'>• PAI:</font> <font color='yellow'>NÃO LOCALIZADO</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('CPF INVÁLIDO');
  }
});

// IP Checker
router.get('/ip', simpleCheckerMiddleware('ip'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Make request to IP API
    try {
      const response = await axios.get(`http://ip-api.com/json/${lista}`);
      const data = response.data;

      if (data.status === 'success') {
        let output = `
          <font color='#00FFFF'>IP LOCALIZADO!</font> <br>
          <font color='white'>• IP:</font> <font color='yellow'>${data.query}</font> <br>
          <font color='white'>• PAÍS:</font> <font color='yellow'>${data.country}</font> <br>
          <font color='white'>• CIDADE:</font> <font color='yellow'>${data.city}</font> <br>
          <font color='white'>• REGIÃO:</font> <font color='yellow'>${data.regionName}</font> <br>
          <font color='white'>• ISP:</font> <font color='yellow'>${data.isp}</font> <br>
          <font color='#00FFFF'>SPLIT CHKS</font> <br>
        `;

        res.send(output);
      } else {
        res.send('IP INVÁLIDO');
      }
    } catch (error) {
      res.send('IP INVÁLIDO');
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('IP INVÁLIDO');
  }
});

// Cartões
router.get('/ggbb', checkerMiddleware('ggbb'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.7; // 30% de chance de aprovação

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    }

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>Aprovada!</font> <br>
        <font color='white'>• CARTÃO:</font> <font color='yellow'>${lista}</font> <br>
        <font color='white'>• BANCO:</font> <font color='yellow'>BANCO DO BRASIL</font> <br>
        <font color='white'>• BANDEIRA:</font> <font color='yellow'>VISA</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      let output = `
        <font color='red'>Reprovada!</font> <br>
        <font color='white'>• CARTÃO:</font> <font color='yellow'>${lista}</font> <br>
        <font color='white'>• MOTIVO:</font> <font color='yellow'>CARTÃO INVÁLIDO</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERRO AO PROCESSAR CARTÃO');
  }
});

router.get('/ggitau', checkerMiddleware('ggitau'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.7; // 30% de chance de aprovação

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    }

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>Aprovada!</font> <br>
        <font color='white'>• CARTÃO:</font> <font color='yellow'>${lista}</font> <br>
        <font color='white'>• BANCO:</font> <font color='yellow'>ITAÚ</font> <br>
        <font color='white'>• BANDEIRA:</font> <font color='yellow'>MASTERCARD</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      let output = `
        <font color='red'>Reprovada!</font> <br>
        <font color='white'>• CARTÃO:</font> <font color='yellow'>${lista}</font> <br>
        <font color='white'>• MOTIVO:</font> <font color='yellow'>CARTÃO INVÁLIDO</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERRO AO PROCESSAR CARTÃO');
  }
});

router.get('/hiper', checkerMiddleware('hiper'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.7; // 30% de chance de aprovação

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    }

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>Aprovada!</font> <br>
        <font color='white'>• CARTÃO:</font> <font color='yellow'>${lista}</font> <br>
        <font color='white'>• BANCO:</font> <font color='yellow'>HIPERCARD</font> <br>
        <font color='white'>• BANDEIRA:</font> <font color='yellow'>HIPERCARD</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      let output = `
        <font color='red'>Reprovada!</font> <br>
        <font color='white'>• CARTÃO:</font> <font color='yellow'>${lista}</font> <br>
        <font color='white'>• MOTIVO:</font> <font color='yellow'>CARTÃO INVÁLIDO</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERRO AO PROCESSAR CARTÃO');
  }
});

// Assinaturas
router.get('/oiplay', checkerMiddleware('oiplay'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.7;

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    } // 30% de chance de aprovação

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>Aprovada!</font> <br>
        <font color='white'>• LOGIN:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• PLANO:</font> <font color='yellow'>PREMIUM</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      let output = `
        <font color='red'>Reprovada!</font> <br>
        <font color='white'>• LOGIN:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• MOTIVO:</font> <font color='yellow'>CREDENCIAIS INVÁLIDAS</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERRO AO PROCESSAR LOGIN');
  }
});

router.get('/olx', checkerMiddleware('olx'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.7;

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    } // 30% de chance de aprovação

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>Aprovada!</font> <br>
        <font color='white'>• EMAIL:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• PLANO:</font> <font color='yellow'>PREMIUM</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      let output = `
        <font color='red'>Reprovada!</font> <br>
        <font color='white'>• EMAIL:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• MOTIVO:</font> <font color='yellow'>CREDENCIAIS INVÁLIDAS</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERRO AO PROCESSAR LOGIN');
  }
});

router.get('/terra', checkerMiddleware('terra'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.7;

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    } // 30% de chance de aprovação

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>Aprovada!</font> <br>
        <font color='white'>• EMAIL:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• PLANO:</font> <font color='yellow'>PREMIUM</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      let output = `
        <font color='red'>Reprovada!</font> <br>
        <font color='white'>• EMAIL:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• MOTIVO:</font> <font color='yellow'>CREDENCIAIS INVÁLIDAS</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERRO AO PROCESSAR LOGIN');
  }
});

// Farmácias
router.get('/drogasil', checkerMiddleware('drogasil'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.7;

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    } // 30% de chance de aprovação

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>Aprovada!</font> <br>
        <font color='white'>• EMAIL:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• PLANO:</font> <font color='yellow'>PREMIUM</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      let output = `
        <font color='red'>Reprovada!</font> <br>
        <font color='white'>• EMAIL:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• MOTIVO:</font> <font color='yellow'>CREDENCIAIS INVÁLIDAS</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERRO AO PROCESSAR LOGIN');
  }
});

router.get('/raia', checkerMiddleware('raia'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.7;

    // Debit user balance and distribute earnings using middleware
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      return res.status(402).send('Erro ao debitar créditos');
    } // 30% de chance de aprovação

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>Aprovada!</font> <br>
        <font color='white'>• EMAIL:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• PLANO:</font> <font color='yellow'>PREMIUM</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      let output = `
        <font color='red'>Reprovada!</font> <br>
        <font color='white'>• EMAIL:</font> <font color='yellow'>${lista.split('|')[0]}</font> <br>
        <font color='white'>• SENHA:</font> <font color='yellow'>${lista.split('|')[1] || 'N/A'}</font> <br>
        <font color='white'>• MOTIVO:</font> <font color='yellow'>CREDENCIAIS INVÁLIDAS</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERRO AO PROCESSAR LOGIN');
  }
});

// Ferramentas - CEP
router.get('/cep', simpleCheckerMiddleware('cep'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Make request to CEP API
    try {
      const response = await axios.get(`https://viacep.com.br/ws/${lista}/json/`);
      const data = response.data;

      if (!data.erro) {
        let output = `
          <font color='#00FFFF'>CEP LOCALIZADO!</font> <br>
          <font color='white'>• CEP:</font> <font color='yellow'>${data.cep}</font> <br>
          <font color='white'>• LOGRADOURO:</font> <font color='yellow'>${data.logradouro}</font> <br>
          <font color='white'>• BAIRRO:</font> <font color='yellow'>${data.bairro}</font> <br>
          <font color='white'>• CIDADE:</font> <font color='yellow'>${data.localidade}</font> <br>
          <font color='white'>• ESTADO:</font> <font color='yellow'>${data.uf}</font> <br>
          <font color='#00FFFF'>PRIVXPLOIT</font> <br>
        `;

        res.send(output);
      } else {
        res.send('CEP INVÁLIDO');
      }
    } catch (error) {
      res.send('CEP INVÁLIDO');
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('CEP INVÁLIDO');
  }
});

// Ferramentas - CNPJ
router.get('/cnpj', simpleCheckerMiddleware('cnpj'), async (req, res) => {
  try {
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).json({ error: 'Parâmetro lista é obrigatório' });
    }

    // Simular resposta (em produção, isso seria uma chamada real à API)
    const isApproved = Math.random() > 0.3; // 70% de chance de aprovação

    if (isApproved) {
      let output = `
        <font color='#00FFFF'>CNPJ LOCALIZADO!</font> <br>
        <font color='white'>• CNPJ:</font> <font color='yellow'>${lista}</font> <br>
        <font color='white'>• RAZÃO SOCIAL:</font> <font color='yellow'>EMPRESA EXEMPLO LTDA</font> <br>
        <font color='white'>• NOME FANTASIA:</font> <font color='yellow'>EXEMPLO</font> <br>
        <font color='white'>• SITUAÇÃO:</font> <font color='yellow'>ATIVA</font> <br>
        <font color='white'>• ABERTURA:</font> <font color='yellow'>01/01/2020</font> <br>
        <font color='#00FFFF'>SPLIT CHKS</font> <br>
      `;

      res.send(output);
    } else {
      res.send('CNPJ INVÁLIDO');
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('CNPJ INVÁLIDO');
  }
});

module.exports = router;
