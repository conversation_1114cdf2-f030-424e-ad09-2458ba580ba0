const express = require('express');
const router = express.Router();
const { ensureAuthenticated } = require('../../middleware/auth');
const { Checker, User, Transaction, Earning } = require('../../models');
const { get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, validateCheckerExecution } = require('../../utils/checkerLoader');
const { checkerApiLimiter } = require('../../middleware/rateLimiting');
const { cacheMiddleware } = require('../../middleware/cache');
const monitor = require('../../utils/monitoring');

// Cache para os handlers dos checkers
const checkerHandlers = new Map();

/**
 * Distribui os ganhos entre programador, afiliado e site
 * @param {Object} transaction - Transação registrada
 * @param {Object} checker - Checker utilizado
 * @param {number} amount - Valor da transação
 * @returns {boolean} - Se a distribuição foi bem-sucedida
 */
const distributeEarnings = async (transaction, checker, amount) => {
  try {
    // Calcular percentuais de distribuição
    // 20% para afiliado, 20% para o site, 60% para o programador
    const sitePercentage = 0.20;
    const affiliatePercentage = 0.20;
    const programmerPercentage = 0.60;

    // Obter o usuário que usou o checker
    const user = await User.findByPk(transaction.user_id);

    // Encontrar o programador
    if (checker.programmer_id) {
      // Adicionar ganhos para o programador (60%)
      await Earning.create({
        user_id: checker.programmer_id,
        amount: amount * programmerPercentage,
        source_type: 'programmer',
        transaction_id: transaction.id,
        checker_id: checker.id
      });
    }

    // Encontrar o afiliado (se existir)
    if (user.referred_by) {
      // Adicionar ganhos para o afiliado (20%)
      await Earning.create({
        user_id: user.referred_by,
        amount: amount * affiliatePercentage,
        source_type: 'affiliate',
        transaction_id: transaction.id,
        checker_id: checker.id
      });
    }

    // Adicionar ganhos para o site (20%)
    // Encontrar usuário admin (rank 1)
    const admin = await User.findOne({ where: { rank: 1 } });
    if (admin) {
      await Earning.create({
        user_id: admin.id,
        amount: amount * sitePercentage,
        source_type: 'site',
        transaction_id: transaction.id,
        checker_id: checker.id
      });
    }

    return true;
  } catch (err) {
    console.error('Erro ao distribuir ganhos:', err);
    return false;
  }
};

/**
 * Debita o saldo do usuário
 * @param {number} userId - ID do usuário
 * @param {number} amount - Quantidade a ser debitada
 * @returns {boolean} - Se o débito foi bem-sucedido
 */
const debitBalance = async (userId, amount = 1) => {
  try {
    const user = await User.findByPk(userId);

    if (!user || user.saldo < amount) {
      return false;
    }

    user.saldo -= amount;
    await user.save();

    return true;
  } catch (err) {
    console.error('Erro ao debitar saldo:', err);
    return false;
  }
};

/**
 * Registra uma transação
 * @param {Object} data - Dados da transação
 * @returns {Object} - Transação criada
 */
const registerTransaction = async (data) => {
  try {
    const transaction = await Transaction.create(data);
    return transaction;
  } catch (err) {
    console.error('Erro ao registrar transação:', err);
    return null;
  }
};

/**
 * Rota dinâmica para todos os checkers com segurança aprimorada
 */
router.get('/:endpoint',
  ensureAuthenticated,
  checkerApiLimiter,
  cacheMiddleware({
    duration: 30, // Cache por 30 segundos
    varyBy: ['url', 'user'],
    onlySuccessful: true
  }),
  async (req, res) => {
    const startTime = Date.now();
    let checkerExecutionSuccess = false;

    try {
      const { endpoint } = req.params;
      const { lista } = req.query;

      // Validações de segurança
      if (!lista || typeof lista !== 'string' || lista.length > 1000) {
        monitor.recordSecurityEvent('blocked_request', { reason: 'invalid_lista_param', endpoint });
        return res.status(400).send('[DIE] Parâmetro lista inválido');
      }

      // Verificar se o endpoint existe
      const checker = await Checker.findOne({
        where: {
          endpoint,
          status: 'active'
        }
      });

      if (!checker) {
        return res.status(404).send('[DIE] Checker não encontrado ou em manutenção');
      }

      // Verificar se o código do checker foi aprovado
      const isValidForExecution = await validateCheckerExecution(checker);
      if (!isValidForExecution) {
        monitor.recordSecurityEvent('blocked_request', {
          reason: 'unapproved_code',
          endpoint,
          checkerId: checker.id
        });
        return res.status(403).send('[DIE] Checker não aprovado para execução');
      }

      // Verificar permissões
      if (checker.required_role !== 'user' &&
          req.session.user.role !== checker.required_role &&
          req.session.user.role !== 'admin' &&
          req.session.user.rank !== 1) {
        monitor.recordSecurityEvent('blocked_request', { reason: 'insufficient_permissions', endpoint, userRole: req.session.user.role });
        return res.status(403).send('[DIE] Sem permissão para acessar este checker');
      }

    // Verificar saldo
    if (checker.charge_type === 'per_test') {
      const debited = await debitBalance(req.session.user.id, checker.price);

      if (!debited) {
        return res.status(400).send('Créditos insuficientes');
      }

      // Atualizar sessão
      req.session.user.saldo -= checker.price;

      // Registrar transação
      const transaction = await registerTransaction({
        user_id: req.session.user.id,
        type: 'debit',
        amount: checker.price,
        description: `Uso do checker ${checker.name}`,
        checker_id: checker.id,
        balance_before: req.session.user.saldo + checker.price,
        balance_after: req.session.user.saldo
      });

      // Distribuir ganhos
      if (transaction) {
        await distributeEarnings(transaction, checker, checker.price);
      }
    }

      // Obter o handler do checker de forma segura
      let handler;
      if (checkerHandlers.has(checker.id)) {
        handler = checkerHandlers.get(checker.id);
      } else {
        handler = await getCheckerHandler(checker);
        checkerHandlers.set(checker.id, handler);
      }

      // Executar o handler com monitoramento
      const executionStart = Date.now();
      await handler(req, res, checker);
      const executionTime = Date.now() - executionStart;

      checkerExecutionSuccess = res.statusCode >= 200 && res.statusCode < 400;

      // Registrar execução no monitoramento
      monitor.recordCheckerExecution(checker.id, checkerExecutionSuccess, executionTime);

    // Se o tipo de cobrança for por live, debitar apenas se a resposta contiver "[LIVE]"
    if (checker.charge_type === 'per_live' && res.statusCode === 200) {
      // Verificar se a resposta contém "[LIVE]"
      const responseBody = res.body;
      if (responseBody && responseBody.includes('[LIVE]')) {
        const debited = await debitBalance(req.session.user.id, checker.price);

        if (debited) {
          // Atualizar sessão
          req.session.user.saldo -= checker.price;
          req.session.user.lives += 1;

          // Registrar transação
          const transaction = await registerTransaction({
            user_id: req.session.user.id,
            type: 'debit',
            amount: checker.price,
            description: `Live no checker ${checker.name}`,
            checker_id: checker.id,
            balance_before: req.session.user.saldo + checker.price,
            balance_after: req.session.user.saldo
          });

          // Distribuir ganhos
          if (transaction) {
            await distributeEarnings(transaction, checker, checker.price);
          }
        }
      }
    }
    } catch (err) {
      console.error(`Erro no checker ${req.params.endpoint}:`, err);

      // Registrar erro no monitoramento
      monitor.recordCheckerExecution(req.params.endpoint, false, Date.now() - startTime);
      monitor.recordSecurityEvent('suspicious_activity', {
        reason: 'checker_execution_error',
        endpoint: req.params.endpoint,
        error: err.message
      });

      res.status(500).send('[DIE] Erro ao processar requisição');
    }
  });

module.exports = router;
