const express = require('express');
const router = express.Router();
const { ensureAuthenticated, ensureAdmin } = require('../../middleware/auth');
const monitor = require('../../utils/monitoring');
const { getRateLimitStats, clearUserRateLimit } = require('../../middleware/rateLimiting');
const { getCacheStats, invalidateCache } = require('../../middleware/cache');
const { getRedisStats, clearCachePattern } = require('../../config/redis');
const CodeReviewService = require('../../services/CodeReviewService');

// Middleware para garantir que apenas admins acessem
router.use(ensureAuthenticated);
router.use(ensureAdmin);

// Obter métricas atuais
router.get('/metrics', async (req, res) => {
  try {
    const metrics = monitor.getMetrics();
    
    // Adicionar métricas de cache e rate limiting
    const cacheStats = await getCacheStats();
    const rateLimitStats = await getRateLimitStats();
    
    const response = {
      ...metrics,
      cache: {
        ...metrics.cache,
        stats: cacheStats
      },
      rateLimiting: rateLimitStats,
      timestamp: new Date().toISOString()
    };

    res.json(response);
  } catch (err) {
    console.error('Erro ao obter métricas:', err);
    res.status(500).json({
      error: 'Erro ao obter métricas',
      message: err.message
    });
  }
});

// Obter histórico de métricas
router.get('/metrics/history', async (req, res) => {
  try {
    const hours = parseInt(req.query.hours) || 24;
    const history = await monitor.getMetricsHistory(hours);
    
    res.json({
      history,
      period: `${hours} hours`,
      count: history.length
    });
  } catch (err) {
    console.error('Erro ao obter histórico:', err);
    res.status(500).json({
      error: 'Erro ao obter histórico de métricas',
      message: err.message
    });
  }
});

// Obter alertas ativos
router.get('/alerts', (req, res) => {
  try {
    const metrics = monitor.getMetrics();
    const activeAlerts = metrics.alerts || [];
    
    res.json({
      alerts: activeAlerts,
      count: activeAlerts.length,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Erro ao obter alertas:', err);
    res.status(500).json({
      error: 'Erro ao obter alertas',
      message: err.message
    });
  }
});

// Reconhecer alerta
router.post('/alerts/:alertId/acknowledge', (req, res) => {
  try {
    const { alertId } = req.params;
    monitor.acknowledgeAlert(parseInt(alertId));
    
    res.json({
      success: true,
      message: 'Alerta reconhecido',
      alertId: alertId
    });
  } catch (err) {
    console.error('Erro ao reconhecer alerta:', err);
    res.status(500).json({
      error: 'Erro ao reconhecer alerta',
      message: err.message
    });
  }
});

// Estatísticas de rate limiting
router.get('/rate-limits', async (req, res) => {
  try {
    const stats = await getRateLimitStats();
    
    res.json({
      rateLimits: stats,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Erro ao obter estatísticas de rate limiting:', err);
    res.status(500).json({
      error: 'Erro ao obter estatísticas de rate limiting',
      message: err.message
    });
  }
});

// Limpar rate limit de usuário
router.delete('/rate-limits/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    await clearUserRateLimit(userId);
    
    res.json({
      success: true,
      message: `Rate limits limpos para usuário ${userId}`
    });
  } catch (err) {
    console.error('Erro ao limpar rate limits:', err);
    res.status(500).json({
      error: 'Erro ao limpar rate limits',
      message: err.message
    });
  }
});

// Estatísticas de cache
router.get('/cache', async (req, res) => {
  try {
    const cacheStats = await getCacheStats();
    const redisStats = await getRedisStats();
    
    res.json({
      cache: cacheStats,
      redis: redisStats,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Erro ao obter estatísticas de cache:', err);
    res.status(500).json({
      error: 'Erro ao obter estatísticas de cache',
      message: err.message
    });
  }
});

// Invalidar cache
router.delete('/cache', async (req, res) => {
  try {
    const { pattern } = req.query;
    
    if (!pattern) {
      return res.status(400).json({
        error: 'Parâmetro pattern é obrigatório'
      });
    }

    await invalidateCache(pattern);
    
    res.json({
      success: true,
      message: `Cache invalidado para padrão: ${pattern}`
    });
  } catch (err) {
    console.error('Erro ao invalidar cache:', err);
    res.status(500).json({
      error: 'Erro ao invalidar cache',
      message: err.message
    });
  }
});

// Limpar cache por padrão
router.delete('/cache/pattern/:pattern', async (req, res) => {
  try {
    const { pattern } = req.params;
    const count = await clearCachePattern(pattern);
    
    res.json({
      success: true,
      message: `${count} chaves removidas para padrão: ${pattern}`,
      count
    });
  } catch (err) {
    console.error('Erro ao limpar cache:', err);
    res.status(500).json({
      error: 'Erro ao limpar cache',
      message: err.message
    });
  }
});

// Estatísticas de revisão de código
router.get('/code-reviews', async (req, res) => {
  try {
    const codeReviewService = new CodeReviewService();
    const stats = await codeReviewService.getReviewStats();
    
    res.json({
      codeReviews: stats,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Erro ao obter estatísticas de revisão:', err);
    res.status(500).json({
      error: 'Erro ao obter estatísticas de revisão de código',
      message: err.message
    });
  }
});

// Reset de métricas (cuidado!)
router.post('/metrics/reset', (req, res) => {
  try {
    monitor.resetMetrics();
    
    res.json({
      success: true,
      message: 'Métricas resetadas',
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Erro ao resetar métricas:', err);
    res.status(500).json({
      error: 'Erro ao resetar métricas',
      message: err.message
    });
  }
});

// Obter métricas de sistema em tempo real
router.get('/system/realtime', (req, res) => {
  try {
    const os = require('os');
    
    const systemMetrics = {
      timestamp: new Date().toISOString(),
      cpu: {
        loadAverage: os.loadavg(),
        cores: os.cpus().length,
        usage: (os.loadavg()[0] / os.cpus().length) * 100
      },
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        percentage: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100
      },
      process: {
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        pid: process.pid,
        version: process.version
      },
      network: os.networkInterfaces()
    };

    res.json(systemMetrics);
  } catch (err) {
    console.error('Erro ao obter métricas de sistema:', err);
    res.status(500).json({
      error: 'Erro ao obter métricas de sistema',
      message: err.message
    });
  }
});

// Endpoint para dashboard em tempo real (Server-Sent Events)
router.get('/dashboard/stream', (req, res) => {
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });

  // Enviar dados iniciais
  const sendMetrics = () => {
    try {
      const metrics = monitor.getMetrics();
      const data = JSON.stringify({
        type: 'metrics',
        data: metrics,
        timestamp: new Date().toISOString()
      });
      
      res.write(`data: ${data}\n\n`);
    } catch (err) {
      console.error('Erro ao enviar métricas via SSE:', err);
    }
  };

  // Enviar métricas a cada 5 segundos
  const interval = setInterval(sendMetrics, 5000);
  
  // Enviar dados iniciais
  sendMetrics();

  // Limpar quando cliente desconectar
  req.on('close', () => {
    clearInterval(interval);
  });

  req.on('end', () => {
    clearInterval(interval);
  });
});

// Configurar alertas
router.post('/alerts/configure', (req, res) => {
  try {
    const { thresholds } = req.body;
    
    if (thresholds) {
      Object.assign(monitor.thresholds, thresholds);
    }
    
    res.json({
      success: true,
      message: 'Configuração de alertas atualizada',
      thresholds: monitor.thresholds
    });
  } catch (err) {
    console.error('Erro ao configurar alertas:', err);
    res.status(500).json({
      error: 'Erro ao configurar alertas',
      message: err.message
    });
  }
});

module.exports = router;
