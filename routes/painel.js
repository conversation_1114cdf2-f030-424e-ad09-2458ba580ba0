const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { User, Checker, Category, Transaction } = require('../models');
const { ensureAuthenticated, ensureAdmin, ensureCheckerAccess, logActivity } = require('../middleware/auth');

// Dashboard
router.get('/', ensureAuthenticated, async (req, res) => {
  try {
    // Count active users (with balance > 0)
    const activeUsers = await User.count({
      where: {
        saldo: {
          [require('sequelize').Op.gt]: 0
        },
        status: 'active'
      }
    });

    // Categories are now loaded globally in app.js middleware
    // No need to load them here anymore

    // Get recent transactions for this user
    const recentTransactions = await Transaction.findAll({
      where: { user_id: req.session.user.id },
      include: [
        { model: Checker, as: 'checker', attributes: ['id', 'name', 'title'] }
      ],
      order: [['createdAt', 'DESC']],
      limit: 5
    });

    // Log activity
    await logActivity(
      req,
      'dashboard_access',
      'Acesso ao painel principal'
    );

    res.render('painel/index', {
      title: 'Painel - CancroSoft TM',
      user: req.session.user,
      activeUsers,
      recentTransactions
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar o painel');
    res.redirect('/');
  }
});

// Admin: Add user page
router.get('/adicionar', ensureAuthenticated, ensureAdmin, (req, res) => {
  res.render('painel/adicionar', {
    title: 'Adicionar Usuário - CancroSoft TM',
    user: req.session.user
  });
});

// Admin: Renew access page
router.get('/renovar', ensureAuthenticated, ensureAdmin, (req, res) => {
  res.render('painel/renovar', {
    title: 'Renovar Acesso - CancroSoft TM',
    user: req.session.user
  });
});

// Admin: List users page
router.get('/usuarios', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const users = await User.findAll();

    res.render('painel/usuarios', {
      title: 'Usuários Cadastrados - PrivXploit',
      user: req.session.user,
      users
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar usuários');
    res.redirect('/painel');
  }
});

// Histórico de Pagamentos PIX
router.get('/payment-history', ensureAuthenticated, async (req, res) => {
  try {
    const { PaymentTransaction } = require('../models');
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    // Buscar transações do usuário
    const { count, rows: transactions } = await PaymentTransaction.findAndCountAll({
      where: { user_id: req.session.user.id },
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    // Calcular resumo de faturamento
    const summary = await PaymentTransaction.findOne({
      where: {
        user_id: req.session.user.id,
        status: 'paid'
      },
      attributes: [
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'total_transactions'],
        [require('sequelize').fn('SUM', require('sequelize').col('amount')), 'total_amount'],
        [require('sequelize').fn('SUM', require('sequelize').col('credits')), 'total_credits']
      ],
      raw: true
    });

    // Resumo por método de pagamento
    const paymentMethodSummary = await PaymentTransaction.findAll({
      where: {
        user_id: req.session.user.id,
        status: 'paid'
      },
      attributes: [
        'payment_method',
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
        [require('sequelize').fn('SUM', require('sequelize').col('amount')), 'total_amount']
      ],
      group: ['payment_method'],
      raw: true
    });

    const totalPages = Math.ceil(count / limit);

    // Log activity
    await logActivity(
      req,
      'payment_history_access',
      'Acesso ao histórico de pagamentos'
    );

    res.render('painel/payment-history', {
      title: 'Histórico de Pagamentos - CancroSoft TM',
      user: req.session.user,
      transactions,
      summary: summary || { total_transactions: 0, total_amount: 0, total_credits: 0 },
      paymentMethodSummary,
      pagination: {
        page,
        totalPages,
        count,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar histórico de pagamentos');
    res.redirect('/painel');
  }
});

// User Profile - Implementação removida (duplicada)

// Credits page
router.get('/credits', ensureAuthenticated, async (req, res) => {
  try {
    // Log activity
    await logActivity(
      req,
      'credits_access',
      'Acesso à página de créditos'
    );

    res.render('painel/credits', {
      title: 'Comprar Créditos - PrivXploit',
      user: req.session.user
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar página de créditos');
    res.redirect('/painel');
  }
});

// Transactions history
router.get('/transactions', ensureAuthenticated, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    // Get user's transactions with pagination
    const { count, rows: transactions } = await Transaction.findAndCountAll({
      where: { user_id: req.session.user.id },
      include: [
        { model: Checker, as: 'checker', attributes: ['id', 'name', 'title'] },
        { model: User, as: 'admin', attributes: ['id', 'usuario'] }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    // Log activity
    await logActivity(
      req,
      'transactions_access',
      'Acesso ao histórico de transações'
    );

    res.render('painel/transactions', {
      title: 'Histórico de Transações - CancroSoft TM',
      user: req.session.user,
      transactions,
      pagination: {
        page,
        totalPages,
        count
      }
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar histórico');
    res.redirect('/painel');
  }
});

// Checker routes
router.get('/checkers/:checker', ensureAuthenticated, async (req, res) => {
  try {
    const checkerName = req.params.checker;

    // Find checker in database
    const checker = await Checker.findOne({
      where: {
        name: checkerName,
        status: 'active'
      },
      include: [{ model: Category, as: 'category' }]
    });

    // Verificar se o checker existe e está ativo
    if (!checker) {
      return res.status(404).render('error', {
        title: '404 - Checker não encontrado',
        message: 'O checker solicitado não existe ou está em manutenção.'
      });
    }

    // Verificar se o checker foi aprovado no code review
    if (checker.approved_code_hash) {
      const { CodeReview } = require('../models');
      const codeReview = await CodeReview.findOne({
        where: {
          approved_code_hash: checker.approved_code_hash,
          status: 'approved'
        }
      });

      if (!codeReview) {
        return res.status(403).render('error', {
          title: '403 - Checker em Análise',
          message: 'Este checker está passando por análise de código e estará disponível em breve.'
        });
      }
    } else {
      // Se não tem hash aprovado, também não pode ser acessado
      return res.status(403).render('error', {
        title: '403 - Checker em Análise',
        message: 'Este checker está passando por análise de código e estará disponível em breve.'
      });
    }

    // Verificar se o usuário tem permissão para acessar este checker
    if (checker.required_role !== 'user' &&
        req.session.user.role !== checker.required_role &&
        req.session.user.role !== 'admin' &&
        req.session.user.rank !== 1) {
      req.flash('error_msg', 'Você não tem permissão para acessar este checker');
      return res.redirect('/painel');
    }

    // Verificar se o usuário tem saldo suficiente
    if (req.session.user.saldo < checker.price) {
      req.flash('error_msg', 'Saldo insuficiente para usar este checker');
      return res.redirect('/painel');
    }

    // Log activity
    await logActivity(
      req,
      'checker_access',
      `Acesso ao checker ${checker.name}`,
      'checker',
      checker.id
    );

    // Verificar se existe uma página específica para este checker
    const checkerViewPath = path.join(__dirname, '..', 'views', 'painel', 'checkers', `${checkerName}.ejs`);
    const hasSpecificView = fs.existsSync(checkerViewPath);

    if (hasSpecificView) {
      // Renderizar página específica do checker
      res.render(`painel/checkers/${checkerName}`, {
        title: `${checker.title} - PrivXploit`,
        user: req.session.user,
        checker: checker,
        price: checker.price
      });
    } else {
      // Renderizar o template genérico com as configurações do checker
      res.render('painel/checkers/checker-template', {
        title: `${checker.title} - PrivXploit`,
        user: req.session.user,
        checkerTitle: checker.title,
        checkerEndpoint: checker.endpoint || `/api/dynamic/${checker.name}`,
        checker: checker,
        price: checker.price
      });
    }
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar o checker');
    res.redirect('/painel');
  }
});

// Profile page
router.get('/profile', ensureAuthenticated, async (req, res) => {
  try {
    // Get user's recent activity
    const recentActivity = await ActivityLog.findAll({
      where: { user_id: req.session.user.id },
      order: [['created_at', 'DESC']],
      limit: 10
    });

    // Get user's transaction summary
    const transactionSummary = await Transaction.findAll({
      where: { user_id: req.session.user.id },
      attributes: [
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'total_transactions'],
        [require('sequelize').fn('SUM', require('sequelize').col('amount')), 'total_amount']
      ],
      raw: true
    });

    // Get user's earnings if applicable
    let earnings = null;
    if (req.session.user.role === 'programmer' || req.session.user.role === 'affiliate') {
      const { Earning } = require('../models');
      earnings = await Earning.findAll({
        where: { user_id: req.session.user.id },
        attributes: [
          [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'total_earnings'],
          [require('sequelize').fn('SUM', require('sequelize').col('amount')), 'total_amount']
        ],
        raw: true
      });
    }

    res.render('painel/profile', {
      title: 'Meu Perfil - CancroSoft TM',
      user: req.session.user,
      recentActivity,
      transactionSummary: transactionSummary[0] || { total_transactions: 0, total_amount: 0 },
      earnings: earnings ? earnings[0] : null,
      pageTitle: 'Meu Perfil'
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar perfil');
    res.redirect('/painel');
  }
});

// Update profile
router.post('/profile/update', ensureAuthenticated, async (req, res) => {
  try {
    const { email, current_password, new_password, confirm_password } = req.body;
    const userId = req.session.user.id;

    // Validate current password if trying to change password
    if (new_password) {
      if (!current_password) {
        req.flash('error_msg', 'Senha atual é obrigatória para alterar a senha');
        return res.redirect('/painel/profile');
      }

      const user = await User.findByPk(userId);
      const isValidPassword = await bcrypt.compare(current_password, user.senha);

      if (!isValidPassword) {
        req.flash('error_msg', 'Senha atual incorreta');
        return res.redirect('/painel/profile');
      }

      if (new_password !== confirm_password) {
        req.flash('error_msg', 'Nova senha e confirmação não coincidem');
        return res.redirect('/painel/profile');
      }

      if (new_password.length < 6) {
        req.flash('error_msg', 'Nova senha deve ter pelo menos 6 caracteres');
        return res.redirect('/painel/profile');
      }
    }

    // Update user data
    const updateData = {};
    if (email) updateData.email = email;
    if (new_password) {
      const bcrypt = require('bcryptjs');
      updateData.senha = await bcrypt.hash(new_password, 10);
    }

    await User.update(updateData, { where: { id: userId } });

    // Update session if email changed
    if (email) {
      req.session.user.email = email;
    }

    // Log activity
    await logActivity(
      req,
      'profile_update',
      'Perfil atualizado'
    );

    req.flash('success_msg', 'Perfil atualizado com sucesso');
    res.redirect('/painel/profile');
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao atualizar perfil');
    res.redirect('/painel/profile');
  }
});

module.exports = router;
