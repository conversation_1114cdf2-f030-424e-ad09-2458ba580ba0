const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { isRedisAvailable, getRedisStats } = require('../config/redis');
const { ensureAuthenticated, ensureAdmin } = require('../middleware/auth');
const monitor = require('../utils/monitoring');
const os = require('os');

// Health check básico (público)
router.get('/', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    checks: {}
  };

  try {
    // Verificar banco de dados
    const dbStart = Date.now();
    await db.authenticate();
    health.checks.database = {
      status: 'ok',
      responseTime: Date.now() - dbStart
    };
  } catch (err) {
    health.checks.database = {
      status: 'error',
      error: err.message
    };
    health.status = 'error';
  }

  // Redis removido - usando cache em memória
  health.checks.cache = {
    status: 'ok',
    type: 'memory',
    message: 'Cache em memória ativo'
  };

  // Verificar uso de memória
  const memoryUsage = process.memoryUsage();
  const memoryThreshold = 1024 * 1024 * 1024; // 1GB

  health.checks.memory = {
    status: memoryUsage.heapUsed > memoryThreshold ? 'warning' : 'ok',
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
    external: Math.round(memoryUsage.external / 1024 / 1024) // MB
  };

  if (health.checks.memory.status === 'warning') {
    health.status = health.status === 'error' ? 'error' : 'degraded';
  }

  const statusCode = health.status === 'ok' ? 200 :
                    health.status === 'degraded' ? 200 : 503;

  res.status(statusCode).json(health);
});

// Health check detalhado (apenas para admins)
router.get('/detailed', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const detailed = {
      timestamp: new Date().toISOString(),
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
        uptime: process.uptime(),
        loadAverage: os.loadavg(),
        freeMemory: Math.round(os.freemem() / 1024 / 1024), // MB
        totalMemory: Math.round(os.totalmem() / 1024 / 1024), // MB
        cpus: os.cpus().length,
        networkInterfaces: Object.keys(os.networkInterfaces())
      },
      process: {
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        versions: process.versions,
        env: {
          NODE_ENV: process.env.NODE_ENV,
          PORT: process.env.PORT
        }
      },
      database: {},
      redis: {},
      monitoring: monitor.getMetrics()
    };

    // Estatísticas do banco
    try {
      const [connections] = await db.query('SHOW STATUS LIKE "Threads_connected"');
      const [maxConnections] = await db.query('SHOW VARIABLES LIKE "max_connections"');
      const [processlist] = await db.query('SHOW PROCESSLIST');
      const [slowQueries] = await db.query('SHOW STATUS LIKE "Slow_queries"');

      detailed.database = {
        connections: parseInt(connections[0]?.Value || 0),
        maxConnections: parseInt(maxConnections[0]?.Value || 0),
        activeQueries: processlist.length,
        slowQueries: parseInt(slowQueries[0]?.Value || 0),
        status: 'connected'
      };
    } catch (err) {
      detailed.database = {
        status: 'error',
        error: err.message
      };
    }

    // Cache em memória (Redis removido)
    detailed.cache = {
      status: 'active',
      type: 'memory',
      message: 'Cache em memória funcionando'
    };

    res.json(detailed);
  } catch (err) {
    console.error('Erro no health check detalhado:', err);
    res.status(500).json({
      error: 'Erro interno no health check',
      message: err.message
    });
  }
});

// Endpoint para verificar status dos serviços
router.get('/services', ensureAuthenticated, ensureAdmin, async (req, res) => {
  const services = {
    timestamp: new Date().toISOString(),
    services: {}
  };

  // Verificar banco de dados
  try {
    await db.authenticate();
    services.services.database = {
      status: 'healthy',
      lastCheck: new Date().toISOString()
    };
  } catch (err) {
    services.services.database = {
      status: 'unhealthy',
      error: err.message,
      lastCheck: new Date().toISOString()
    };
  }

  // Cache em memória (Redis removido)
  services.services.cache = {
    status: 'healthy',
    type: 'memory',
    lastCheck: new Date().toISOString()
  };

  // Verificar sistema de arquivos
  try {
    const fs = require('fs');
    const testFile = './temp_health_check.txt';
    fs.writeFileSync(testFile, 'health check');
    fs.unlinkSync(testFile);

    services.services.filesystem = {
      status: 'healthy',
      lastCheck: new Date().toISOString()
    };
  } catch (err) {
    services.services.filesystem = {
      status: 'unhealthy',
      error: err.message,
      lastCheck: new Date().toISOString()
    };
  }

  // Status geral
  const allHealthy = Object.values(services.services).every(service => service.status === 'healthy');
  services.overallStatus = allHealthy ? 'healthy' : 'degraded';

  res.json(services);
});

// Endpoint para métricas básicas (Prometheus format)
router.get('/metrics', (req, res) => {
  const metrics = monitor.getMetrics();

  // Formato Prometheus
  let prometheusMetrics = '';

  // Requests
  prometheusMetrics += `# HELP http_requests_total Total number of HTTP requests\n`;
  prometheusMetrics += `# TYPE http_requests_total counter\n`;
  prometheusMetrics += `http_requests_total{status="success"} ${metrics.requests.successful}\n`;
  prometheusMetrics += `http_requests_total{status="failed"} ${metrics.requests.failed}\n`;

  // Memory
  const memUsage = process.memoryUsage();
  prometheusMetrics += `# HELP process_memory_usage_bytes Process memory usage in bytes\n`;
  prometheusMetrics += `# TYPE process_memory_usage_bytes gauge\n`;
  prometheusMetrics += `process_memory_usage_bytes{type="heap_used"} ${memUsage.heapUsed}\n`;
  prometheusMetrics += `process_memory_usage_bytes{type="heap_total"} ${memUsage.heapTotal}\n`;

  // Uptime
  prometheusMetrics += `# HELP process_uptime_seconds Process uptime in seconds\n`;
  prometheusMetrics += `# TYPE process_uptime_seconds gauge\n`;
  prometheusMetrics += `process_uptime_seconds ${process.uptime()}\n`;

  // Checkers
  prometheusMetrics += `# HELP checker_executions_total Total number of checker executions\n`;
  prometheusMetrics += `# TYPE checker_executions_total counter\n`;
  prometheusMetrics += `checker_executions_total{status="success"} ${metrics.checkers.successful}\n`;
  prometheusMetrics += `checker_executions_total{status="failed"} ${metrics.checkers.failed}\n`;

  res.set('Content-Type', 'text/plain');
  res.send(prometheusMetrics);
});

// Endpoint para verificar conectividade
router.get('/ping', (req, res) => {
  res.json({
    status: 'ok',
    message: 'pong',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Endpoint para verificar readiness (pronto para receber tráfego)
router.get('/ready', async (req, res) => {
  try {
    // Verificar se os serviços essenciais estão funcionando
    await db.authenticate();

    const ready = {
      status: 'ready',
      timestamp: new Date().toISOString(),
      checks: {
        database: 'ok'
      }
    };

    res.json(ready);
  } catch (err) {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: err.message
    });
  }
});

// Endpoint para verificar liveness (aplicação está viva)
router.get('/live', (req, res) => {
  res.json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    pid: process.pid
  });
});

module.exports = router;
