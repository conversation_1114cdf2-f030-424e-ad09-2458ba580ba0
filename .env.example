# PrivXploit - Configuração de Ambiente
# Arquivo de configuração para desenvolvimento local

# Servidor
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# Banco de Dados
DB_HOST=127.0.0.1
DB_USER=cancrosoft
DB_PASSWORD=cancrosoft123
DB_NAME=ofc

# Sessão
SESSION_SECRET=privxploit-dev-secret-key-2024

# URL Base (será detectada automaticamente se não definida)
BASE_URL=http://localhost:3000

# PagFly API Configuration (PIX) - Configurar quando necessário
PAGFLY_PUBLIC_KEY=your_pagfly_public_key_here
PAGFLY_SECRET_KEY=your_pagfly_secret_key_here

# NowPayments Configuration (Crypto) - Configurar quando necessário
NOWPAYMENTS_API_KEY=your_nowpayments_api_key_here
NOWPAYMENTS_IPN_SECRET=your_nowpayments_ipn_secret_here

# Payment Configuration
PAYMENT_WEBHOOK_SECRET=privxploit-webhook-secret-dev

# GitHub Integration (Optional - for private repositories)
GITHUB_TOKEN=your_github_personal_access_token_here
