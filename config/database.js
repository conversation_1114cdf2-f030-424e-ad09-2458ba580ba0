const { Sequelize } = require('sequelize');

// Database configuration - universal
console.log('🗄️  Database Configuration:');
console.log(`   Host: ${process.env.DB_HOST || '127.0.0.1'}`);
console.log(`   Database: ${process.env.DB_NAME || 'ofc'}`);
console.log(`   User: ${process.env.DB_USER || 'cancrosoft'}`);
console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);

const sequelize = new Sequelize(
  process.env.DB_NAME || 'ofc',
  process.env.DB_USER || 'cancrosoft',
  process.env.DB_PASSWORD || 'cancrosoft123',
  {
    host: process.env.DB_HOST || '127.0.0.1',
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'production' ? false : console.log,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    retry: {
      max: 3
    },
    dialectOptions: {
      connectTimeout: 60000
    }
  }
);

module.exports = sequelize;
