{"rateLimiting": {"enabled": true, "general": {"windowMs": 900000, "max": 1000}, "api": {"windowMs": 60000, "max": 20}, "login": {"windowMs": 900000, "max": 5}}, "cache": {"enabled": true, "defaultTTL": 300, "maxSize": 1000}, "codeReview": {"autoApproveThreshold": 90, "autoRejectThreshold": 30, "maxCodeSize": 50000, "allowedModules": ["axios", "crypto", "querystring", "url", "util"]}, "monitoring": {"enabled": true, "alertThresholds": {"memoryUsage": 85, "cpuUsage": 80, "errorRate": 5, "responseTime": 2000}}}