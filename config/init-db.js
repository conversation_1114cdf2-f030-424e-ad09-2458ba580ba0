const db = require('./database');
const crypto = require('crypto');
const models = require('../models');
const { User, Category, Checker, Plan } = models;

// Initialize database
async function initDB() {
  try {
    // Sync all models with database
    await db.sync({ force: false });

    // Check if admin user exists
    const adminExists = await User.findOne({
      where: {
        rank: 1
      }
    });

    // Create admin user if it doesn't exist
    if (!adminExists) {
      const md5Hash = crypto.createHash('md5').update('admin').digest('hex');

      await User.create({
        usuario: 'admin',
        senha: md5Hash,
        email: '<EMAIL>',
        rank: 1,
        role: 'admin',
        saldo: 100,
        lives: 0,
        criador: 'system',
        status: 'active'
      });

      console.log('Admin user created');
    }

    // Create default categories if they don't exist
    const categories = [
      { name: '<PERSON><PERSON><PERSON><PERSON>', icon: 'fa fa-credit-card', display_order: 1 },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'fa fa-chalkboard-teacher', display_order: 2 },
      { name: '<PERSON><PERSON><PERSON><PERSON>', icon: 'fas fa-ambulance', display_order: 3 },
      { name: 'Ferramentas', icon: 'fa fa-plus', display_order: 4 }
    ];

    for (const category of categories) {
      const exists = await Category.findOne({ where: { name: category.name } });
      if (!exists) {
        await Category.create(category);
        console.log(`Category ${category.name} created`);
      }
    }

    // Get category IDs
    const cartoesCategory = await Category.findOne({ where: { name: 'Cartões' } });
    const assinaturasCategory = await Category.findOne({ where: { name: 'Assinaturas' } });
    const farmaciasCategory = await Category.findOne({ where: { name: 'Farmácias' } });
    const ferramentasCategory = await Category.findOne({ where: { name: 'Ferramentas' } });

    // Create default checkers if they don't exist
    const checkers = [
      // Cartões
      {
        name: 'ggbb',
        endpoint: 'ggbb',
        title: 'GG BB CHECKER',
        category_id: cartoesCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-credit-card',
        display_order: 1
      },
      {
        name: 'ggitau',
        endpoint: 'ggitau',
        title: 'GG ITAÚ CHECKER',
        category_id: cartoesCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-credit-card',
        display_order: 2
      },
      {
        name: 'hiper',
        endpoint: 'hiper',
        title: 'GG HIPERCARD CHECKER',
        category_id: cartoesCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-credit-card',
        display_order: 3
      },

      // Assinaturas
      {
        name: 'oiplay',
        endpoint: 'oiplay',
        title: 'OI PLAY CHECKER',
        category_id: assinaturasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-play',
        display_order: 1
      },
      {
        name: 'olx',
        endpoint: 'olx',
        title: 'OLX CHECKER',
        category_id: assinaturasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-shopping-cart',
        display_order: 2
      },
      {
        name: 'terra',
        endpoint: 'terra',
        title: 'TERRA CHECKER',
        category_id: assinaturasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-globe',
        display_order: 3
      },

      // Farmácias
      {
        name: 'drogasil',
        endpoint: 'drogasil',
        title: 'DROGASIL CHECKER',
        category_id: farmaciasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fas fa-pills',
        display_order: 1
      },
      {
        name: 'raia',
        endpoint: 'raia',
        title: 'DROGA RAIA CHECKER',
        category_id: farmaciasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fas fa-pills',
        display_order: 2
      },

      // Ferramentas
      {
        name: 'cep',
        endpoint: 'cep',
        title: 'CEP CHECKER',
        category_id: ferramentasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-map-marker',
        display_order: 1
      },
      {
        name: 'ip',
        endpoint: 'ip',
        title: 'IP CHECKER',
        category_id: ferramentasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-network-wired',
        display_order: 2
      },
      {
        name: 'bin',
        endpoint: 'bin',
        title: 'BIN CHECKER',
        category_id: ferramentasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-credit-card',
        display_order: 3
      },
      {
        name: 'cpf',
        endpoint: 'cpf',
        title: 'CPF CHECKER',
        category_id: ferramentasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-id-card',
        display_order: 4
      },
      {
        name: 'cnpj',
        endpoint: 'cnpj',
        title: 'CNPJ CHECKER',
        category_id: ferramentasCategory.id,
        price: 1.0,
        status: 'active',
        icon: 'fa fa-building',
        display_order: 5
      }
    ];

    for (const checker of checkers) {
      const exists = await Checker.findOne({ where: { name: checker.name } });
      if (!exists) {
        await Checker.create(checker);
        console.log(`Checker ${checker.name} created`);
      }
    }

    // Create default plans if they don't exist
    const plans = [
      {
        name: 'Básico',
        description: 'Plano básico com 100 créditos',
        price: 50.0,
        credits: 100,
        duration_days: 30,
        status: 'active',
        features: 'Acesso a checkers básicos\nSuporte por email',
        role_granted: 'user'
      },
      {
        name: 'Premium',
        description: 'Plano premium com 300 créditos',
        price: 100.0,
        credits: 300,
        duration_days: 30,
        status: 'active',
        features: 'Acesso a todos os checkers\nSuporte prioritário\nDesconto em recargas',
        role_granted: 'premium'
      },
      {
        name: 'VIP',
        description: 'Plano VIP com 1000 créditos',
        price: 250.0,
        credits: 1000,
        duration_days: 30,
        status: 'active',
        features: 'Acesso a todos os checkers\nSuporte 24/7\nCheckers exclusivos\nPreços reduzidos',
        role_granted: 'vip'
      }
    ];

    for (const plan of plans) {
      const exists = await Plan.findOne({ where: { name: plan.name } });
      if (!exists) {
        await Plan.create(plan);
        console.log(`Plan ${plan.name} created`);
      }
    }

    console.log('Database initialized');
  } catch (err) {
    console.error('Error initializing database:', err);
  }
}

module.exports = initDB;
