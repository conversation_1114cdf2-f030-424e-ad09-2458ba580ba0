// Redis completamente removido - sistema usa apenas cache em memória
console.log('Sistema configurado para cache em memória (Redis removido)');

// Mock client para compatibilidade com código existente
const client = null;

// Função para verificar se o Redis está disponível
const isRedisAvailable = async () => {
  return false; // Redis removido
};

// Função para obter estatísticas do Redis
const getRedisStats = async () => {
  return {
    connected: false,
    message: 'Redis removido - usando cache em memória',
    type: 'memory-only'
  };
};

// Função para limpar cache por padrão
const clearCachePattern = async (pattern) => {
  console.log(`Cache pattern ${pattern} - usando cache em memória`);
  return 0;
};

// Função para obter informações de uma chave
const getCacheInfo = async (key) => {
  return {
    exists: false,
    message: 'Redis removido - usando cache em memória'
  };
};

module.exports = {
  client,
  isRedisAvailable,
  getRedisStats,
  clearCachePattern,
  getCacheInfo
};
