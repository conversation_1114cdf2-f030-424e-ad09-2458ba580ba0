# 🚀 Instruções para Deploy na VPS

## 📋 Pré-requisitos
- VPS com acesso root
- MySQL instalado e rodando
- Node.js v16+ instalado
- PM2 instalado globalmente

## 🔧 Passos para Deploy

### 1. Clonar o repositório na VPS
```bash
cd /var/www
git clone https://github.com/MatxCoder/privxploit.git
cd privxploit
```

### 2. Instalar dependências
```bash
npm install
```

### 3. Configurar banco de dados
```bash
# Criar banco de dados
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS ofc;"
mysql -u root -p -e "GRANT ALL PRIVILEGES ON ofc.* TO 'cancrosoft'@'localhost' IDENTIFIED BY 'cancrosoft123';"
mysql -u root -p -e "FLUSH PRIVILEGES;"

# Importar dados
mysql -u cancrosoft -pcancrosoft123 ofc < database_export.sql
```

### 4. Configurar ambiente
```bash
# Copiar arquivo de configuração da VPS
cp .env.vps .env

# Verificar se as configurações estão corretas
cat .env
```

### 5. Iniciar aplicação
```bash
# Parar processos antigos se existirem
pm2 stop all
pm2 delete all

# Iniciar nova aplicação
pm2 start app.js --name "privxploit"

# Salvar configuração do PM2
pm2 save
pm2 startup
```

### 6. Verificar funcionamento
```bash
# Verificar status
pm2 status

# Verificar logs
pm2 logs privxploit

# Testar aplicação
curl http://localhost
```

## 🔍 Troubleshooting

### Erro de permissão na porta 80
```bash
# Dar permissão para Node.js usar porta 80
sudo setcap 'cap_net_bind_service=+ep' $(which node)
```

### Problemas de banco de dados
```bash
# Verificar se MySQL está rodando
systemctl status mysql

# Verificar conexão
mysql -u cancrosoft -pcancrosoft123 -e "SHOW DATABASES;"
```

### Logs da aplicação
```bash
# Ver logs em tempo real
pm2 logs privxploit --lines 50

# Ver apenas erros
pm2 logs privxploit --err
```

## 📊 Configurações importantes

### Banco de dados
- **Host**: 127.0.0.1
- **Usuário**: cancrosoft
- **Senha**: cancrosoft123
- **Database**: ofc

### Aplicação
- **Porta**: 80
- **Ambiente**: development
- **PM2**: privxploit

### APIs configuradas
- **PagFly**: Chave de produção configurada
- **GitHub**: Token configurado para versionamento

## ✅ Verificação final
1. Aplicação rodando na porta 80
2. Banco de dados conectado
3. PM2 gerenciando o processo
4. Logs sem erros críticos
5. Interface acessível via navegador
