#!/usr/bin/env node

/**
 * Script para testar o sistema de code review
 * Este script verifica se o sistema está funcionando corretamente
 */

const { Checker, CodeReview, User, Category } = require('./models');
const crypto = require('crypto');

async function testCodeReviewSystem() {
  console.log('🔍 Testando Sistema de Code Review...\n');

  try {
    // 1. Verificar se existem checkers
    const checkers = await Checker.findAll({
      include: [{ model: Category, as: 'category' }],
      limit: 5
    });

    console.log(`📊 Total de checkers encontrados: ${checkers.length}`);

    if (checkers.length === 0) {
      console.log('⚠️  Nenhum checker encontrado. Criando um checker de teste...');
      
      // Criar categoria de teste se não existir
      let testCategory = await Category.findOne({ where: { name: 'Teste' } });
      if (!testCategory) {
        testCategory = await Category.create({
          name: 'Teste',
          description: 'Categoria de teste',
          icon: 'fa fa-test',
          status: 'active',
          display_order: 999
        });
        console.log('✅ Categoria de teste criada');
      }

      // Criar checker de teste
      const testChecker = await Checker.create({
        name: 'test_checker',
        endpoint: 'test',
        title: 'Checker de Teste',
        description: 'Checker criado para teste do sistema de code review',
        category_id: testCategory.id,
        price: 1.0,
        status: 'active',
        custom_code: `
module.exports = async function(req, res, checker) {
  try {
    const { lista } = req.query;
    
    if (!lista) {
      return res.status(400).send('Parâmetro lista é obrigatório');
    }
    
    // Simular processamento
    const isApproved = Math.random() > 0.5;
    
    if (isApproved) {
      return res.send('[LIVE] Teste aprovado | ' + lista);
    } else {
      return res.send('[DIE] Teste reprovado | ' + lista);
    }
  } catch (err) {
    console.error('[CHECKER TEST]', err);
    return res.status(500).send('[DIE] Erro ao processar requisição');
  }
};
        `.trim()
      });

      console.log('✅ Checker de teste criado');
      checkers.push(testChecker);
    }

    // 2. Verificar status de aprovação de cada checker
    console.log('\n📋 Status dos checkers:');
    for (const checker of checkers) {
      let isApproved = false;
      let codeReview = null;

      if (checker.approved_code_hash) {
        codeReview = await CodeReview.findOne({
          where: {
            approved_code_hash: checker.approved_code_hash,
            status: 'approved'
          }
        });
        isApproved = !!codeReview;
      }

      const status = isApproved ? '✅ Aprovado' : '⏳ Pendente';
      console.log(`  - ${checker.title} (${checker.name}): ${status}`);
      
      if (!isApproved && (checker.custom_code || checker.module_path)) {
        console.log(`    💡 Este checker precisa de code review`);
      }
    }

    // 3. Verificar code reviews pendentes
    const pendingReviews = await CodeReview.findAll({
      where: { status: ['pending', 'needs_revision'] },
      include: [
        { model: Checker, as: 'checker', attributes: ['id', 'name', 'title'] }
      ]
    });

    console.log(`\n📝 Code reviews pendentes: ${pendingReviews.length}`);
    
    if (pendingReviews.length > 0) {
      console.log('   Reviews que precisam de aprovação:');
      for (const review of pendingReviews) {
        const checkerName = review.checker ? review.checker.title : 'N/A';
        console.log(`   - Review #${review.id} - ${checkerName} (${review.status})`);
      }
    }

    // 4. Verificar se há checkers sem code review
    const checkersWithoutReview = [];
    for (const checker of checkers) {
      if (checker.custom_code || checker.module_path) {
        const hasReview = await CodeReview.findOne({
          where: { checker_id: checker.id }
        });
        
        if (!hasReview) {
          checkersWithoutReview.push(checker);
        }
      }
    }

    if (checkersWithoutReview.length > 0) {
      console.log(`\n⚠️  Checkers sem code review: ${checkersWithoutReview.length}`);
      for (const checker of checkersWithoutReview) {
        console.log(`   - ${checker.title} (${checker.name})`);
      }
    }

    // 5. Resumo final
    console.log('\n📊 RESUMO:');
    console.log(`   Total de checkers: ${checkers.length}`);
    console.log(`   Code reviews pendentes: ${pendingReviews.length}`);
    console.log(`   Checkers sem review: ${checkersWithoutReview.length}`);
    
    const approvedCheckers = checkers.filter(c => c.approved_code_hash).length;
    console.log(`   Checkers aprovados: ${approvedCheckers}`);
    console.log(`   Checkers pendentes: ${checkers.length - approvedCheckers}`);

    if (pendingReviews.length > 0) {
      console.log('\n💡 Para aprovar code reviews, acesse: /admin/code-reviews');
    }

    console.log('\n✅ Teste concluído com sucesso!');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
    process.exit(1);
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testCodeReviewSystem()
    .then(() => process.exit(0))
    .catch(err => {
      console.error('❌ Erro fatal:', err);
      process.exit(1);
    });
}

module.exports = testCodeReviewSystem;
