-- Script para inserir dados iniciais no banco PrivXploit
-- Execute APÓS o recreate_database.sql

USE ofc;

-- 1. INSERIR USUÁRIO ADMIN PADRÃO
INSERT INTO usuarios (usuario, senha, email, saldo, `rank`, `role`, status) VALUES
('admin', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 1000.00, 1, 'admin', 'active');

-- 2. INSERIR CATEGORIAS PADRÃO
INSERT INTO categories (name, description, icon, status, display_order, required_role) VALUES
('Cartões', 'Verificação de cartões de crédito e débito', 'fa fa-credit-card', 'active', 1, 'user'),
('Assinaturas', 'Verificação de contas de streaming e serviços', 'fa fa-play-circle', 'active', 2, 'user'),
('Farmácias', 'Verificação de contas de farmácias online', 'fa fa-pills', 'active', 3, 'user'),
('Ferramentas', 'Ferramentas diversas e utilitários', 'fa fa-tools', 'active', 4, 'user');

-- 3. INSERIR CONFIGURAÇÕES PADRÃO
INSERT INTO settings (`key`, value, description, type, category, is_public) VALUES
('site_name', 'PrivXploit', 'Nome do site', 'string', 'general', true),
('site_description', 'Plataforma de verificação de dados', 'Descrição do site', 'string', 'general', true),
('maintenance_mode', 'false', 'Modo de manutenção', 'boolean', 'general', false),
('registration_enabled', 'true', 'Permitir novos registros', 'boolean', 'general', false),
('default_credits', '10', 'Créditos padrão para novos usuários', 'number', 'general', false),
('min_withdrawal', '50.00', 'Valor mínimo para saque', 'number', 'financial', false),
('pix_enabled', 'true', 'PIX habilitado', 'boolean', 'payment', false),
('crypto_enabled', 'true', 'Criptomoedas habilitadas', 'boolean', 'payment', false),
('credit_card_enabled', 'false', 'Cartão de crédito habilitado', 'boolean', 'payment', false),
('boleto_enabled', 'false', 'Boleto habilitado', 'boolean', 'payment', false);

-- 4. INSERIR PLANOS PADRÃO
INSERT INTO plans (name, description, price, credits, status) VALUES
('Básico', 'Plano básico com 100 créditos', 10.00, 100, 'active'),
('Intermediário', 'Plano intermediário com 500 créditos', 40.00, 500, 'active'),
('Avançado', 'Plano avançado com 1000 créditos', 70.00, 1000, 'active'),
('Premium', 'Plano premium com 2500 créditos', 150.00, 2500, 'active');

-- 5. INSERIR CHECKER DE EXEMPLO (APROVADO)
INSERT INTO checkers (
  name,
  endpoint,
  title,
  description,
  category_id,
  price,
  status,
  icon,
  charge_type,
  required_role,
  display_order,
  custom_code,
  approved_code_hash
) VALUES (
  'example_checker',
  'example',
  'Checker de Exemplo',
  'Checker de exemplo para demonstração',
  (SELECT id FROM categories WHERE name = 'Ferramentas' LIMIT 1),
  1.00,
  'active',
  'fa fa-check-circle',
  'per_test',
  'user',
  1,
  'module.exports = async function(req, res, checker) {
    try {
      const { lista } = req.query;

      if (!lista) {
        return res.status(400).send("Parâmetro lista é obrigatório");
      }

      // Simular verificação
      const isValid = Math.random() > 0.5;

      if (isValid) {
        return res.send("[LIVE] Exemplo válido | " + lista);
      } else {
        return res.send("[DIE] Exemplo inválido | " + lista);
      }
    } catch (err) {
      console.error("[CHECKER EXAMPLE]", err);
      return res.status(500).send("[DIE] Erro ao processar requisição");
    }
  };',
  'approved_example_hash_123'
);

-- 6. INSERIR CODE REVIEW APROVADO PARA O CHECKER DE EXEMPLO
INSERT INTO code_reviews (
  checker_id,
  submitted_by,
  code_content,
  code_type,
  code_hash,
  status,
  reviewed_by,
  review_notes,
  approved_code_hash,
  submitted_at,
  reviewed_at
) VALUES (
  (SELECT id FROM checkers WHERE name = 'example_checker' LIMIT 1),
  (SELECT id FROM usuarios WHERE `role` = 'admin' LIMIT 1),
  'module.exports = async function(req, res, checker) {
    try {
      const { lista } = req.query;

      if (!lista) {
        return res.status(400).send("Parâmetro lista é obrigatório");
      }

      // Simular verificação
      const isValid = Math.random() > 0.5;

      if (isValid) {
        return res.send("[LIVE] Exemplo válido | " + lista);
      } else {
        return res.send("[DIE] Exemplo inválido | " + lista);
      }
    } catch (err) {
      console.error("[CHECKER EXAMPLE]", err);
      return res.status(500).send("[DIE] Erro ao processar requisição");
    }
  };',
  'custom_code',
  'example_hash_123',
  'approved',
  (SELECT id FROM usuarios WHERE `role` = 'admin' LIMIT 1),
  'Código aprovado - exemplo funcional',
  'approved_example_hash_123',
  NOW(),
  NOW()
);

-- 7. INSERIR LOG DE ATIVIDADE INICIAL
INSERT INTO activity_logs (user_id, action, description, entity_type, entity_id) VALUES
((SELECT id FROM usuarios WHERE `role` = 'admin' LIMIT 1), 'database_recreated', 'Banco de dados recriado com sucesso', 'system', NULL);

-- 8. VERIFICAR SE TUDO FOI CRIADO CORRETAMENTE
SELECT 'Verificando estrutura do banco...' AS status;

SELECT
  'usuarios' AS tabela,
  COUNT(*) AS registros
FROM usuarios
UNION ALL
SELECT
  'categories' AS tabela,
  COUNT(*) AS registros
FROM categories
UNION ALL
SELECT
  'checkers' AS tabela,
  COUNT(*) AS registros
FROM checkers
UNION ALL
SELECT
  'code_reviews' AS tabela,
  COUNT(*) AS registros
FROM code_reviews
UNION ALL
SELECT
  'settings' AS tabela,
  COUNT(*) AS registros
FROM settings
UNION ALL
SELECT
  'plans' AS tabela,
  COUNT(*) AS registros
FROM plans;

-- 9. VERIFICAR SE A COLUNA approved_code_hash EXISTE
SELECT
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'checkers'
AND COLUMN_NAME = 'approved_code_hash'
AND TABLE_SCHEMA = 'ofc';

SELECT 'Banco de dados recriado com sucesso!' AS resultado;
