const mysql = require('mysql2/promise');

async function fixDatabase() {
  console.log('🔧 Corrigindo banco de dados...');
  
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    user: 'cancrosoft',
    password: 'cancrosoft123',
    database: 'ofc'
  });

  try {
    // Verificar se a coluna existe
    const [rows] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'checkers' 
      AND COLUMN_NAME = 'approved_code_hash'
      AND TABLE_SCHEMA = 'ofc'
    `);

    if (rows.length > 0) {
      console.log('✅ Coluna approved_code_hash já existe');
      return;
    }

    // Adicionar a coluna
    await connection.execute(`
      ALTER TABLE checkers 
      ADD COLUMN approved_code_hash VARCHAR(64) NULL 
      COMMENT 'Hash do código aprovado para verificação de integridade'
    `);

    console.log('✅ Coluna approved_code_hash adicionada com sucesso!');

  } catch (error) {
    console.error('❌ Erro:', error.message);
  } finally {
    await connection.end();
  }
}

fixDatabase();
