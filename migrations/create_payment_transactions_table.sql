-- <PERSON><PERSON><PERSON> da tabela payment_transactions
-- Execute este script no seu banco de dados MySQL

CREATE TABLE IF NOT EXISTS `payment_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `plan_id` int(11) DEFAULT NULL,
  `external_id` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL COMMENT 'Valor em reais',
  `credits` int(11) NOT NULL COMMENT 'Quantidade de créditos a serem adicionados',
  `payment_method` enum('pix','crypto') NOT NULL,
  `crypto_currency` varchar(10) DEFAULT NULL COMMENT 'Tipo de criptomoeda (BTC, ETH, USDT, etc.)',
  `status` enum('pending','paid','expired','cancelled','failed') DEFAULT 'pending',
  `payment_url` text DEFAULT NULL COMMENT 'URL para pagamento (QR Code PIX ou endereço crypto)',
  `qr_code` text DEFAULT NULL COMMENT 'Código QR para PIX',
  `expires_at` datetime DEFAULT NULL COMMENT 'Data de expiração do pagamento',
  `paid_at` datetime DEFAULT NULL COMMENT 'Data do pagamento confirmado',
  `webhook_data` json DEFAULT NULL COMMENT 'Dados recebidos via webhook',
  `notes` text DEFAULT NULL COMMENT 'Observações adicionais',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `external_id` (`external_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_created_at` (`createdAt`),
  CONSTRAINT `fk_payment_transactions_user` FOREIGN KEY (`user_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_payment_transactions_plan` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela para armazenar transações de pagamento PIX e criptomoedas';

-- Inserir alguns dados de exemplo (opcional)
-- INSERT INTO `payment_transactions` 
-- (`user_id`, `external_id`, `amount`, `credits`, `payment_method`, `status`, `createdAt`, `updatedAt`) 
-- VALUES 
-- (1, 'CS_1_1234567890_test', 50.00, 500, 'pix', 'pending', NOW(), NOW()),
-- (1, 'CS_1_1234567891_test', 100.00, 1000, 'crypto', 'paid', NOW(), NOW());

-- Verificar se a tabela foi criada corretamente
-- DESCRIBE payment_transactions;

-- Verificar dados inseridos
-- SELECT * FROM payment_transactions;
