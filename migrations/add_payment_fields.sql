-- Migration para adicionar novos campos na tabela payment_transactions
-- Data: 2024

-- Adicionar novos campos para suporte a Mercado Pago e NowPayments
ALTER TABLE `payment_transactions` 
ADD COLUMN `provider_id` VARCHAR(255) NULL COMMENT 'ID da transação no provedor (Mercado Pago, NowPayments)' AFTER `qr_code`,
ADD COLUMN `crypto_amount` DECIMAL(20,8) NULL COMMENT 'Valor em criptomoeda' AFTER `provider_id`,
ADD COLUMN `usd_amount` DECIMAL(10,2) NULL COMMENT 'Valor em USD (para crypto)' AFTER `crypto_amount`;

-- Adicionar índices para melhor performance
ALTER TABLE `payment_transactions` 
ADD INDEX `idx_provider_id` (`provider_id`),
ADD INDEX `idx_crypto_currency` (`crypto_currency`),
ADD INDEX `idx_payment_method_status` (`payment_method`, `status`);

-- Atual<PERSON>r coment<PERSON>rios das colunas existentes se necessário
ALTER TABLE `payment_transactions` 
MODIFY COLUMN `payment_url` TEXT NULL COMMENT 'URL para pagamento (QR Code PIX ou endereço crypto)',
MODIFY COLUMN `qr_code` TEXT NULL COMMENT 'Código QR para PIX (base64)';
