'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Verificar se a coluna já existe
      const tableDescription = await queryInterface.describeTable('checkers');
      
      if (!tableDescription.approved_code_hash) {
        await queryInterface.addColumn('checkers', 'approved_code_hash', {
          type: Sequelize.STRING(64),
          allowNull: true,
          comment: 'Hash do código aprovado para verificação de integridade'
        });
        
        console.log('✅ Coluna approved_code_hash adicionada à tabela checkers');
      } else {
        console.log('ℹ️  Coluna approved_code_hash já existe na tabela checkers');
      }
    } catch (error) {
      console.error('❌ Erro ao adicionar coluna approved_code_hash:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Verificar se a coluna existe antes de remover
      const tableDescription = await queryInterface.describeTable('checkers');
      
      if (tableDescription.approved_code_hash) {
        await queryInterface.removeColumn('checkers', 'approved_code_hash');
        console.log('✅ Coluna approved_code_hash removida da tabela checkers');
      } else {
        console.log('ℹ️  Coluna approved_code_hash não existe na tabela checkers');
      }
    } catch (error) {
      console.error('❌ Erro ao remover coluna approved_code_hash:', error);
      throw error;
    }
  }
};
