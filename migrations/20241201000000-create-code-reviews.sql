-- Migração para criar tabela de revisões de código
-- Execute este script no seu banco de dados MySQL

CREATE TABLE IF NOT EXISTS `code_reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `checker_id` int(11) DEFAULT NULL,
  `submitted_by` int(11) NOT NULL,
  `reviewed_by` int(11) DEFAULT NULL,
  `code_content` longtext NOT NULL,
  `code_type` enum('module','custom_code') NOT NULL DEFAULT 'custom_code',
  `original_filename` varchar(255) DEFAULT NULL,
  `status` enum('pending','approved','rejected','needs_revision') NOT NULL DEFAULT 'pending',
  `security_score` int(11) DEFAULT NULL COMMENT 'Score de segurança de 0-100 baseado na análise automática',
  `security_issues` json DEFAULT NULL COMMENT 'Lista de problemas de segurança encontrados',
  `review_notes` text DEFAULT NULL COMMENT 'Notas do revisor sobre o código',
  `rejection_reason` text DEFAULT NULL,
  `approved_code_hash` varchar(64) DEFAULT NULL COMMENT 'Hash SHA-256 do código aprovado para verificação de integridade',
  `execution_sandbox` enum('strict','limited','standard') NOT NULL DEFAULT 'strict' COMMENT 'Nível de sandbox para execução do código',
  `max_execution_time` int(11) NOT NULL DEFAULT 5000 COMMENT 'Tempo máximo de execução em milissegundos',
  `allowed_modules` json DEFAULT NULL COMMENT 'Lista de módulos permitidos para este código',
  `test_results` json DEFAULT NULL COMMENT 'Resultados dos testes automatizados',
  `performance_metrics` json DEFAULT NULL COMMENT 'Métricas de performance do código',
  `submitted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reviewed_at` datetime DEFAULT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_code_reviews_status` (`status`),
  KEY `idx_code_reviews_submitted_by` (`submitted_by`),
  KEY `idx_code_reviews_reviewed_by` (`reviewed_by`),
  KEY `idx_code_reviews_checker_id` (`checker_id`),
  KEY `idx_code_reviews_submitted_at` (`submitted_at`),
  KEY `idx_code_reviews_approved_hash` (`approved_code_hash`),
  CONSTRAINT `fk_code_reviews_checker` FOREIGN KEY (`checker_id`) REFERENCES `checkers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_code_reviews_submitted_by` FOREIGN KEY (`submitted_by`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_code_reviews_reviewed_by` FOREIGN KEY (`reviewed_by`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Adicionar coluna approved_code_hash na tabela checkers se não existir
ALTER TABLE `checkers`
ADD COLUMN IF NOT EXISTS `approved_code_hash` varchar(64) DEFAULT NULL COMMENT 'Hash do código aprovado' AFTER `custom_code`;

-- Adicionar índice para o hash aprovado
ALTER TABLE `checkers`
ADD INDEX IF NOT EXISTS `idx_checkers_approved_hash` (`approved_code_hash`);



-- Verificar se as tabelas foram criadas corretamente
SELECT
  TABLE_NAME,
  TABLE_ROWS,
  CREATE_TIME
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME IN ('code_reviews', 'checkers');

-- Verificar índices criados
SELECT
  TABLE_NAME,
  INDEX_NAME,
  COLUMN_NAME,
  NON_UNIQUE
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME IN ('code_reviews', 'checkers')
  AND INDEX_NAME LIKE '%code%' OR INDEX_NAME LIKE '%hash%'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
