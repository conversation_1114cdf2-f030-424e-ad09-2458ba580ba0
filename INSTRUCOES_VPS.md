# 🔧 Instruções para Corrigir Banco de Dados na VPS

O erro `Unknown column 'approved_code_hash' in 'field list'` indica que a coluna não existe no banco de produção. Siga uma das opções abaixo para corrigir:

## 📋 **Opção 1: Script Node.js (Recomendado)**

1. **Acesse a VPS via SSH:**
   ```bash
   ssh root@*************
   ```

2. **Vá para o diretório do projeto:**
   ```bash
   cd /var/www/privxploit
   ```

3. **Execute o script de correção:**
   ```bash
   node fix_vps_database.js
   ```

4. **Reinicie o servidor:**
   ```bash
   pm2 restart all
   ```

## 📋 **Opção 2: SQL Direto**

1. **Acesse o MySQL:**
   ```bash
   mysql -u cancrosoft -p
   # Digite a senha: cancrosoft123
   ```

2. **Execute os comandos SQL:**
   ```sql
   USE ofc;
   
   ALTER TABLE checkers 
   ADD COLUMN approved_code_hash VARCHAR(64) NULL 
   COMMENT 'Hash do código aprovado para verificação de integridade';
   
   DESCRIBE checkers;
   ```

3. **Saia do MySQL:**
   ```sql
   EXIT;
   ```

4. **Reinicie o servidor:**
   ```bash
   pm2 restart all
   ```

## 📋 **Opção 3: Arquivo SQL**

1. **Copie o arquivo SQL para a VPS:**
   ```bash
   scp fix_database.sql root@*************:/var/www/privxploit/
   ```

2. **Execute o arquivo SQL:**
   ```bash
   mysql -u cancrosoft -p ofc < fix_database.sql
   ```

## 🔍 **Verificar se Funcionou**

Após executar qualquer opção, verifique se a coluna foi criada:

```sql
mysql -u cancrosoft -p -e "DESCRIBE ofc.checkers;" | grep approved_code_hash
```

Se retornar uma linha com `approved_code_hash`, a migração foi bem-sucedida!

## 🚀 **Após a Correção**

1. **Reinicie o servidor:**
   ```bash
   pm2 restart all
   ```

2. **Verifique os logs:**
   ```bash
   pm2 logs
   ```

3. **Teste o sistema:**
   - Acesse o painel admin
   - Vá para "Gerenciar Checkers"
   - Verifique se a coluna "Code Review" aparece
   - Teste criar um novo checker

## ⚠️ **Se Ainda Houver Problemas**

Se o erro persistir, pode ser necessário:

1. **Verificar se todas as tabelas existem:**
   ```sql
   SHOW TABLES LIKE 'code_reviews';
   ```

2. **Executar todas as migrações pendentes:**
   ```bash
   npm run migrate
   ```

3. **Verificar logs detalhados:**
   ```bash
   pm2 logs --lines 50
   ```

## 📞 **Suporte**

Se precisar de ajuda, forneça:
- Logs do erro completo
- Resultado do comando `DESCRIBE checkers;`
- Versão do Node.js: `node --version`
- Status do PM2: `pm2 status`
