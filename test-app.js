require('dotenv').config();

console.log('🚀 Iniciando teste da aplicação...');
console.log('📋 Variáveis de ambiente:');
console.log('   NODE_ENV:', process.env.NODE_ENV || 'development');
console.log('   PORT:', process.env.PORT || 3000);
console.log('   DB_HOST:', process.env.DB_HOST);
console.log('   DB_NAME:', process.env.DB_NAME);
console.log('   DB_USER:', process.env.DB_USER);

// Testar conexão com banco
console.log('\n🔌 Testando conexão com banco de dados...');
const db = require('./config/database');

db.authenticate()
  .then(() => {
    console.log('✅ Conexão com banco de dados estabelecida com sucesso!');
    
    // Testar carregamento de modelos
    console.log('\n📦 Testando carregamento de modelos...');
    try {
      const User = require('./models/User');
      const Plan = require('./models/Plan');
      const Transaction = require('./models/Transaction');
      console.log('✅ Modelos carregados com sucesso!');
      
      // Testar Express
      console.log('\n🌐 Testando Express...');
      const express = require('express');
      const app = express();
      
      app.get('/test', (req, res) => {
        res.json({ status: 'OK', message: 'Aplicação funcionando!' });
      });
      
      const port = 3001; // Usar porta diferente para teste
      const server = app.listen(port, () => {
        console.log(`✅ Servidor rodando na porta ${port}`);
        console.log(`🌍 Acesse: http://localhost:${port}/test`);
        
        // Fechar após 5 segundos
        setTimeout(() => {
          console.log('\n🔚 Teste concluído com sucesso!');
          server.close();
          process.exit(0);
        }, 5000);
      });
      
    } catch (err) {
      console.error('❌ Erro ao carregar modelos:', err.message);
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('❌ Erro na conexão com banco:', err.message);
    process.exit(1);
  });
