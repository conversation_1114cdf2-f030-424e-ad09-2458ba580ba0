#!/usr/bin/env node

/**
 * Script para configurar autenticação do Git com token
 * Este script configura o repositório para usar token de acesso pessoal
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROJECT_DIR = '/var/www/privxploit';
const REPO_URL = 'https://github.com/MatxCoder/privxploit.git';

async function configureGitAuth() {
  console.log('🔧 Configurando autenticação do Git...\n');

  try {
    // 1. Criar diretório se não existir
    if (!fs.existsSync(PROJECT_DIR)) {
      console.log('📁 Criando diretório do projeto...');
      execSync(`sudo mkdir -p ${PROJECT_DIR}`);
      execSync(`sudo chown $USER:$USER ${PROJECT_DIR}`);
    }

    // 2. Entrar no diretório
    process.chdir(PROJECT_DIR);

    // 3. Verificar se é um repositório Git
    if (!fs.existsSync('.git')) {
      console.log('📥 Clonando repositório...');
      execSync(`git clone ${REPO_URL} .`);
    }

    // 4. Configurar credential helper
    console.log('⚙️  Configurando credential helper...');
    execSync('git config credential.helper store');

    // 5. Configurar para usar token
    console.log('🔑 Configurando para usar token...');
    
    // Ler configurações existentes se houver
    let username = 'MatxCoder'; // padrão
    let email = '<EMAIL>'; // padrão
    
    try {
      username = execSync('git config user.name', { encoding: 'utf8' }).trim();
    } catch (e) {
      execSync(`git config user.name "${username}"`);
    }
    
    try {
      email = execSync('git config user.email', { encoding: 'utf8' }).trim();
    } catch (e) {
      execSync(`git config user.email "${email}"`);
    }

    console.log(`👤 Usuário: ${username}`);
    console.log(`📧 Email: ${email}`);

    // 6. Instruções para o token
    console.log('\n🔑 CONFIGURAÇÃO DO TOKEN:');
    console.log('1. Acesse: https://github.com/settings/tokens');
    console.log('2. Clique em "Generate new token (classic)"');
    console.log('3. Selecione os escopos: repo, workflow, write:packages');
    console.log('4. Copie o token gerado');
    console.log('5. Execute o comando abaixo substituindo SEU_TOKEN:');
    console.log('');
    console.log(`git remote set-url origin https://${username}:<EMAIL>/MatxCoder/privxploit.git`);
    console.log('');

    // 7. Criar arquivo de configuração
    const configScript = `#!/bin/bash
# Script para aplicar token
# Uso: ./apply_token.sh SEU_TOKEN_AQUI

if [ -z "$1" ]; then
    echo "❌ Erro: Token não fornecido"
    echo "Uso: ./apply_token.sh SEU_TOKEN"
    exit 1
fi

TOKEN="$1"
USERNAME="${username}"

cd ${PROJECT_DIR}
git remote set-url origin "https://\${USERNAME}:\${TOKEN}@github.com/MatxCoder/privxploit.git"

echo "✅ Token aplicado com sucesso!"
echo "🧪 Testando configuração..."

git fetch origin
if [ $? -eq 0 ]; then
    echo "✅ Autenticação funcionando!"
else
    echo "❌ Erro na autenticação. Verifique o token."
fi
`;

    fs.writeFileSync('apply_token.sh', configScript);
    execSync('chmod +x apply_token.sh');

    console.log('📄 Arquivo apply_token.sh criado!');
    console.log('');
    console.log('💡 Para aplicar o token, execute:');
    console.log(`   cd ${PROJECT_DIR}`);
    console.log('   ./apply_token.sh SEU_TOKEN_AQUI');

    // 8. Status atual
    console.log('\n📊 Status atual do repositório:');
    try {
      const status = execSync('git status --porcelain', { encoding: 'utf8' });
      if (status.trim()) {
        console.log('📝 Arquivos modificados encontrados');
      } else {
        console.log('✅ Repositório limpo');
      }
    } catch (e) {
      console.log('⚠️  Erro ao verificar status');
    }

    console.log('\n✅ Configuração inicial concluída!');

  } catch (error) {
    console.error('❌ Erro durante a configuração:', error.message);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  configureGitAuth();
}

module.exports = configureGitAuth;
