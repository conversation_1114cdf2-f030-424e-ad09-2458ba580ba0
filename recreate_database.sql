-- Script para recriar o banco de dados PrivXploit do zero
-- Execute este script na VPS para corrigir todos os problemas de estrutura

-- 1. BACKUP DOS DADOS IMPORTANTES (execute antes de deletar)
-- mysqldump -u cancrosoft -p ofc > backup_ofc_$(date +%Y%m%d_%H%M%S).sql

-- 2. DELETAR E RECRIAR O BANCO
DROP DATABASE IF EXISTS ofc;
CREATE DATABASE ofc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ofc;

-- 3. CRIAR TABELA DE USUÁRIOS
CREATE TABLE usuarios (
  id INT AUTO_INCREMENT PRIMARY KEY,
  usuario VARCHAR(255) NOT NULL UNIQUE,
  senha VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  saldo DECIMAL(10,2) DEFAULT 0.00,
  `rank` INT DEFAULT 0,
  `role` ENUM('user', 'programmer', 'admin') DEFAULT 'user',
  criador VARCHAR(255) DEFAULT NULL,
  referral_code VARCHAR(50) UNIQUE,
  referred_by INT DEFAULT NULL,
  lives INT DEFAULT 0,
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
  lastLogin DATETIME DEFAULT NULL,
  resetPasswordToken VARCHAR(255) DEFAULT NULL,
  resetPasswordExpires DATETIME DEFAULT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_usuario (usuario),
  INDEX idx_email (email),
  INDEX idx_referral_code (referral_code),
  FOREIGN KEY (referred_by) REFERENCES usuarios(id) ON DELETE SET NULL
);

-- 4. CRIAR TABELA DE CATEGORIAS
CREATE TABLE categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  icon VARCHAR(255) DEFAULT 'fa fa-folder',
  status ENUM('active', 'inactive') DEFAULT 'active',
  display_order INT DEFAULT 0,
  required_role ENUM('user', 'programmer', 'admin') DEFAULT 'user',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_status (status),
  INDEX idx_display_order (display_order)
);

-- 5. CRIAR TABELA DE CHECKERS (COM TODAS AS COLUNAS NECESSÁRIAS)
CREATE TABLE checkers (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  endpoint VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  category_id INT NOT NULL,
  price DECIMAL(10,2) DEFAULT 1.00,
  status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
  icon VARCHAR(255) DEFAULT 'fa fa-check',
  background_image VARCHAR(255) DEFAULT NULL,
  charge_type ENUM('per_test', 'per_batch', 'unlimited') DEFAULT 'per_test',
  required_role ENUM('user', 'programmer', 'admin') DEFAULT 'user',
  display_order INT DEFAULT 0,
  module_path VARCHAR(500) DEFAULT NULL,
  custom_code TEXT DEFAULT NULL,
  programmer_id INT DEFAULT NULL,
  approved_code_hash VARCHAR(64) DEFAULT NULL COMMENT 'Hash do código aprovado para verificação de integridade',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_endpoint (endpoint),
  INDEX idx_category_id (category_id),
  INDEX idx_status (status),
  INDEX idx_approved_code_hash (approved_code_hash),
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
  FOREIGN KEY (programmer_id) REFERENCES usuarios(id) ON DELETE SET NULL
);

-- 6. CRIAR TABELA DE CODE REVIEWS
CREATE TABLE code_reviews (
  id INT AUTO_INCREMENT PRIMARY KEY,
  checker_id INT NOT NULL,
  submitted_by INT NOT NULL,
  code_content LONGTEXT NOT NULL,
  code_type ENUM('custom_code', 'module') DEFAULT 'custom_code',
  original_filename VARCHAR(255) DEFAULT NULL,
  code_hash VARCHAR(64) NOT NULL,
  status ENUM('pending', 'approved', 'rejected', 'needs_revision') DEFAULT 'pending',
  reviewed_by INT DEFAULT NULL,
  review_notes TEXT DEFAULT NULL,
  approved_code_hash VARCHAR(64) DEFAULT NULL,
  submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  reviewed_at DATETIME DEFAULT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_checker_id (checker_id),
  INDEX idx_submitted_by (submitted_by),
  INDEX idx_status (status),
  INDEX idx_code_hash (code_hash),
  INDEX idx_approved_code_hash (approved_code_hash),
  FOREIGN KEY (checker_id) REFERENCES checkers(id) ON DELETE CASCADE,
  FOREIGN KEY (submitted_by) REFERENCES usuarios(id) ON DELETE CASCADE,
  FOREIGN KEY (reviewed_by) REFERENCES usuarios(id) ON DELETE SET NULL
);

-- 7. CRIAR TABELA DE TRANSAÇÕES
CREATE TABLE transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  checker_id INT DEFAULT NULL,
  type ENUM('credit', 'debit', 'bonus', 'refund') NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  description TEXT,
  status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
  reference_id VARCHAR(255) DEFAULT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_checker_id (checker_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_reference_id (reference_id),
  FOREIGN KEY (user_id) REFERENCES usuarios(id) ON DELETE CASCADE,
  FOREIGN KEY (checker_id) REFERENCES checkers(id) ON DELETE SET NULL
);

-- 8. CRIAR TABELA DE PLANOS
CREATE TABLE plans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  credits INT NOT NULL,
  duration_days INT DEFAULT NULL,
  status ENUM('active', 'inactive') DEFAULT 'active',
  features JSON DEFAULT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_status (status),
  INDEX idx_price (price)
);

-- 9. CRIAR TABELA DE TRANSAÇÕES DE PAGAMENTO
CREATE TABLE payment_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  plan_id INT DEFAULT NULL,
  amount DECIMAL(10,2) NOT NULL,
  payment_method ENUM('pix', 'crypto', 'credit_card', 'boleto') NOT NULL,
  payment_id VARCHAR(255) DEFAULT NULL,
  qr_code TEXT DEFAULT NULL,
  status ENUM('pending', 'completed', 'failed', 'cancelled', 'expired') DEFAULT 'pending',
  expires_at DATETIME DEFAULT NULL,
  paid_at DATETIME DEFAULT NULL,
  metadata JSON DEFAULT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_plan_id (plan_id),
  INDEX idx_payment_id (payment_id),
  INDEX idx_status (status),
  INDEX idx_payment_method (payment_method),
  FOREIGN KEY (user_id) REFERENCES usuarios(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE SET NULL
);

-- 10. CRIAR TABELA DE ASSINATURAS
CREATE TABLE subscriptions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  plan_id INT NOT NULL,
  status ENUM('active', 'inactive', 'cancelled', 'expired') DEFAULT 'active',
  starts_at DATETIME NOT NULL,
  expires_at DATETIME NOT NULL,
  auto_renew BOOLEAN DEFAULT FALSE,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_plan_id (plan_id),
  INDEX idx_status (status),
  INDEX idx_expires_at (expires_at),
  FOREIGN KEY (user_id) REFERENCES usuarios(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE CASCADE
);

-- 11. CRIAR TABELA DE LOGS DE ATIVIDADE
CREATE TABLE activity_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT DEFAULT NULL,
  action VARCHAR(255) NOT NULL,
  description TEXT,
  entity_type VARCHAR(100) DEFAULT NULL,
  entity_id INT DEFAULT NULL,
  ip_address VARCHAR(45) DEFAULT NULL,
  user_agent TEXT DEFAULT NULL,
  metadata JSON DEFAULT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_entity (entity_type, entity_id),
  INDEX idx_created_at (createdAt),
  FOREIGN KEY (user_id) REFERENCES usuarios(id) ON DELETE SET NULL
);

-- 12. CRIAR TABELA DE GANHOS
CREATE TABLE earnings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  type ENUM('referral', 'bonus', 'commission', 'cashback') NOT NULL,
  description TEXT,
  reference_id VARCHAR(255) DEFAULT NULL,
  status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_reference_id (reference_id),
  FOREIGN KEY (user_id) REFERENCES usuarios(id) ON DELETE CASCADE
);

-- 13. CRIAR TABELA DE CONFIGURAÇÕES
CREATE TABLE settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  `key` VARCHAR(255) NOT NULL UNIQUE,
  value TEXT,
  description TEXT,
  type ENUM('string', 'number', 'boolean', 'json', 'encrypted') DEFAULT 'string',
  category VARCHAR(100) DEFAULT 'general',
  is_public BOOLEAN DEFAULT FALSE,
  is_encrypted BOOLEAN DEFAULT FALSE,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_key (`key`),
  INDEX idx_category (category),
  INDEX idx_is_public (is_public)
);
