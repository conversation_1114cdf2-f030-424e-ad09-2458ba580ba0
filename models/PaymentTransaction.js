const { DataTypes } = require('sequelize');
const db = require('../config/database');

const PaymentTransaction = db.define('payment_transaction', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  plan_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'plans',
      key: 'id'
    }
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  external_id: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  credits: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  payment_method: {
    type: DataTypes.ENUM('pix', 'crypto'),
    allowNull: false
  },
  crypto_currency: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'paid', 'expired', 'cancelled', 'failed'),
    defaultValue: 'pending'
  },
  payment_url: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  qr_code: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  provider_id: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  crypto_amount: {
    type: DataTypes.DECIMAL(20, 8),
    allowNull: true
  },
  usd_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  paid_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  webhook_data: {
    type: DataTypes.JSON,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'payment_transactions',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['plan_id']
    },
    {
      fields: ['external_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['payment_method']
    },
    {
      fields: ['crypto_currency']
    },
    {
      fields: ['provider_id']
    }
  ]
});

module.exports = PaymentTransaction;
