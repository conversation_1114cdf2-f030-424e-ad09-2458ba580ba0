const { DataTypes } = require('sequelize');
const db = require('../config/database');

const Plan = db.define('plan', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  credits: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  duration_days: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active'
  },
  features: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  role_granted: {
    type: DataTypes.ENUM('user', 'premium', 'vip'),
    allowNull: true,
    defaultValue: 'user'
  }
}, {
  timestamps: true,
  tableName: 'plans',
  indexes: [
    {
      fields: ['status']
    },
    {
      fields: ['price']
    },
    {
      fields: ['credits']
    },
    {
      fields: ['role_granted']
    }
  ]
});

module.exports = Plan;
