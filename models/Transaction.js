const { DataTypes } = require('sequelize');
const db = require('../config/database');

const Transaction = db.define('transaction', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  amount: {
    type: DataTypes.FLOAT,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('credit', 'debit'),
    allowNull: false
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  checker_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'checkers',
      key: 'id'
    }
  },
  admin_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  balance_before: {
    type: DataTypes.FLOAT,
    allowNull: false
  },
  balance_after: {
    type: DataTypes.FLOAT,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'completed', 'failed', 'refunded'),
    defaultValue: 'completed'
  },
  reference_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'transactions',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['checker_id']
    },
    {
      fields: ['admin_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['reference_id']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['user_id', 'type']
    },
    {
      fields: ['user_id', 'status']
    }
  ]
});

module.exports = Transaction;
