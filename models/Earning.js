const { DataTypes } = require('sequelize');
const db = require('../config/database');

const Earning = db.define('earning', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  amount: {
    type: DataTypes.FLOAT,
    allowNull: false
  },
  source_type: {
    type: DataTypes.ENUM('programmer', 'affiliate', 'site'),
    allowNull: false
  },
  transaction_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'transactions',
      key: 'id'
    }
  },
  checker_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'checkers',
      key: 'id'
    }
  }
}, {
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  tableName: 'earnings'
});

module.exports = Earning;
