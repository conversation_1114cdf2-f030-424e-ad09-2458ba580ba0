const { DataTypes } = require('sequelize');
const db = require('../config/database');

const ActivityLog = db.define('activity_log', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  action: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  details: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  resource_type: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  resource_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'activity_logs',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['action']
    },
    {
      fields: ['resource_type']
    },
    {
      fields: ['resource_id']
    },
    {
      fields: ['ip_address']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['user_id', 'action']
    },
    {
      fields: ['user_id', 'createdAt']
    },
    {
      fields: ['resource_type', 'resource_id']
    }
  ]
});

module.exports = ActivityLog;
