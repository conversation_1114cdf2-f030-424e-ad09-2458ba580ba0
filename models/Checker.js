const { DataTypes } = require('sequelize');
const db = require('../config/database');

const Checker = db.define('checker', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  endpoint: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  category_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  price: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 1.0
  },
  status: {
    type: DataTypes.ENUM('active', 'maintenance', 'disabled'),
    defaultValue: 'active'
  },
  icon: {
    type: DataTypes.STRING(50),
    allowNull: true,
    defaultValue: 'fa fa-check'
  },
  background_image: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  charge_type: {
    type: DataTypes.ENUM('per_test', 'per_live'),
    defaultValue: 'per_test'
  },
  required_role: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'user'
  },
  display_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  module_path: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  custom_code: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  programmer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  approved_code_hash: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: 'Hash do código aprovado para verificação de integridade'
  }
}, {
  timestamps: true,
  tableName: 'checkers'
});

// Método para verificar se o checker foi aprovado
Checker.prototype.isApproved = async function() {
  if (!this.approved_code_hash) {
    return false;
  }

  const { CodeReview } = require('./index');
  const codeReview = await CodeReview.findOne({
    where: {
      approved_code_hash: this.approved_code_hash,
      status: 'approved'
    }
  });

  return !!codeReview;
};

// Método estático para verificar se um checker foi aprovado
Checker.isCheckerApproved = async function(checker) {
  if (!checker.approved_code_hash) {
    return false;
  }

  const { CodeReview } = require('./index');
  const codeReview = await CodeReview.findOne({
    where: {
      approved_code_hash: checker.approved_code_hash,
      status: 'approved'
    }
  });

  return !!codeReview;
};

module.exports = Checker;
