const { DataTypes } = require('sequelize');
const db = require('../config/database');

const CodeReview = db.define('code_review', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  checker_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'checkers',
      key: 'id'
    }
  },
  submitted_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  reviewed_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  code_content: {
    type: DataTypes.TEXT('long'),
    allowNull: false
  },
  code_type: {
    type: DataTypes.ENUM('module', 'custom_code'),
    allowNull: false,
    defaultValue: 'custom_code'
  },
  original_filename: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'needs_revision'),
    allowNull: false,
    defaultValue: 'pending'
  },
  security_score: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Score de segurança de 0-100 baseado na análise automática'
  },
  security_issues: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Lista de problemas de segurança encontrados'
  },
  review_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notas do revisor sobre o código'
  },
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  approved_code_hash: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: 'Hash SHA-256 do código aprovado para verificação de integridade'
  },
  execution_sandbox: {
    type: DataTypes.ENUM('strict', 'limited', 'standard'),
    allowNull: false,
    defaultValue: 'strict',
    comment: 'Nível de sandbox para execução do código'
  },
  max_execution_time: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 5000,
    comment: 'Tempo máximo de execução em milissegundos'
  },
  allowed_modules: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Lista de módulos permitidos para este código'
  },
  test_results: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Resultados dos testes automatizados'
  },
  performance_metrics: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Métricas de performance do código'
  },
  submitted_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  reviewed_at: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'code_reviews',
  indexes: [
    {
      fields: ['status']
    },
    {
      fields: ['submitted_by']
    },
    {
      fields: ['reviewed_by']
    },
    {
      fields: ['checker_id']
    },
    {
      fields: ['submitted_at']
    }
  ]
});

module.exports = CodeReview;
