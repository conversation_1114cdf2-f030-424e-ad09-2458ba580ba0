const { DataTypes } = require('sequelize');
const db = require('../config/database');

const Category = db.define('category', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  icon: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'fa fa-folder'
  },
  status: {
    type: DataTypes.ENUM('active', 'disabled'),
    defaultValue: 'active'
  },
  display_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  required_role: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'user'
  }
}, {
  timestamps: true,
  tableName: 'categories'
});

module.exports = Category;
