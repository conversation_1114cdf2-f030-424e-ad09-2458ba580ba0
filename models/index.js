const User = require('./User');
const Checker = require('./Checker');
const Category = require('./Category');
const Transaction = require('./Transaction');
const PaymentTransaction = require('./PaymentTransaction');
const Plan = require('./Plan');
const Subscription = require('./Subscription');
const ActivityLog = require('./ActivityLog');
const Earning = require('./Earning');
const CodeReview = require('./CodeReview');
const Setting = require('./Setting');

// Define associations
Category.hasMany(Checker, { foreignKey: 'category_id', as: 'checkers' });
Checker.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });

User.hasMany(Transaction, { foreignKey: 'user_id', as: 'transactions' });
Transaction.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

User.hasMany(Transaction, { foreignKey: 'admin_id', as: 'adminTransactions' });
Transaction.belongsTo(User, { foreignKey: 'admin_id', as: 'admin' });

Checker.hasMany(Transaction, { foreignKey: 'checker_id', as: 'transactions' });
Transaction.belongsTo(Checker, { foreignKey: 'checker_id', as: 'checker' });

User.hasMany(Subscription, { foreignKey: 'user_id', as: 'subscriptions' });
Subscription.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

Plan.hasMany(Subscription, { foreignKey: 'plan_id', as: 'subscriptions' });
Subscription.belongsTo(Plan, { foreignKey: 'plan_id', as: 'plan' });

Transaction.hasOne(Subscription, { foreignKey: 'transaction_id', as: 'subscription' });
Subscription.belongsTo(Transaction, { foreignKey: 'transaction_id', as: 'transaction' });

User.hasMany(ActivityLog, { foreignKey: 'user_id', as: 'activityLogs' });
ActivityLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// Programmer relationship with checkers
User.hasMany(Checker, { foreignKey: 'programmer_id', as: 'createdCheckers' });
Checker.belongsTo(User, { foreignKey: 'programmer_id', as: 'programmer' });

// Referral relationships
User.hasMany(User, { foreignKey: 'referred_by', as: 'referrals' });
User.belongsTo(User, { foreignKey: 'referred_by', as: 'referrer' });

// Earnings associations
User.hasMany(Earning, { foreignKey: 'user_id', as: 'earnings' });
Earning.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

Transaction.hasMany(Earning, { foreignKey: 'transaction_id', as: 'earnings' });
Earning.belongsTo(Transaction, { foreignKey: 'transaction_id', as: 'transaction' });

Checker.hasMany(Earning, { foreignKey: 'checker_id', as: 'earnings' });
Earning.belongsTo(Checker, { foreignKey: 'checker_id', as: 'checker' });

// CodeReview associations
User.hasMany(CodeReview, { foreignKey: 'submitted_by', as: 'submittedCodeReviews' });
CodeReview.belongsTo(User, { foreignKey: 'submitted_by', as: 'submitter' });

User.hasMany(CodeReview, { foreignKey: 'reviewed_by', as: 'reviewedCodeReviews' });
CodeReview.belongsTo(User, { foreignKey: 'reviewed_by', as: 'reviewer' });

Checker.hasMany(CodeReview, { foreignKey: 'checker_id', as: 'codeReviews' });
CodeReview.belongsTo(Checker, { foreignKey: 'checker_id', as: 'checker' });

// PaymentTransaction associations
User.hasMany(PaymentTransaction, { foreignKey: 'user_id', as: 'paymentTransactions' });
PaymentTransaction.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

Plan.hasMany(PaymentTransaction, { foreignKey: 'plan_id', as: 'paymentTransactions' });
PaymentTransaction.belongsTo(Plan, { foreignKey: 'plan_id', as: 'plan' });

module.exports = {
  User,
  Checker,
  Category,
  Transaction,
  PaymentTransaction,
  Plan,
  Subscription,
  ActivityLog,
  Earning,
  CodeReview,
  Setting
};
