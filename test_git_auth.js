#!/usr/bin/env node

/**
 * Script para testar a autenticação do Git
 */

const { execSync } = require('child_process');
const path = require('path');

async function testGitAuth() {
  console.log('🔧 Testando autenticação do Git...\n');

  try {
    // Verificar se estamos em um repositório Git
    const gitDir = execSync('git rev-parse --git-dir', { encoding: 'utf8' }).trim();
    console.log('✅ Repositório Git encontrado:', gitDir);

    // Verificar URL remota atual
    const remoteUrl = execSync('git remote get-url origin', { encoding: 'utf8' }).trim();
    console.log('🔗 URL remota atual:', remoteUrl);

    // Verificar se a URL contém token
    if (remoteUrl.includes('@github.com')) {
      console.log('✅ URL configurada com autenticação');
    } else {
      console.log('⚠️  URL não contém autenticação');
    }

    // Testar fetch
    console.log('\n🧪 Testando git fetch...');
    try {
      const fetchResult = execSync('git fetch origin', { encoding: 'utf8', timeout: 10000 });
      console.log('✅ Git fetch executado com sucesso');
      if (fetchResult.trim()) {
        console.log('📄 Resultado:', fetchResult.trim());
      }
    } catch (fetchError) {
      console.error('❌ Erro no git fetch:', fetchError.message);
      
      if (fetchError.message.includes('Authentication failed')) {
        console.log('\n💡 Soluções:');
        console.log('1. Configure o token GitHub no painel admin');
        console.log('2. Ou execute: git remote set-url origin https://USERNAME:<EMAIL>/MatxCoder/privxploit.git');
      }
    }

    // Verificar status
    console.log('\n📊 Status do repositório:');
    const status = execSync('git status --porcelain', { encoding: 'utf8' }).trim();
    if (status) {
      console.log('📝 Arquivos modificados encontrados');
    } else {
      console.log('✅ Repositório limpo');
    }

    // Verificar branch atual
    const branch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    console.log('🌿 Branch atual:', branch);

    // Verificar último commit
    const lastCommit = execSync('git log -1 --oneline', { encoding: 'utf8' }).trim();
    console.log('📝 Último commit:', lastCommit);

    console.log('\n✅ Teste concluído!');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
    
    if (error.message.includes('not a git repository')) {
      console.log('\n💡 Este diretório não é um repositório Git.');
      console.log('Execute este script no diretório do projeto.');
    }
  }
}

// Executar teste
if (require.main === module) {
  testGitAuth();
}

module.exports = testGitAuth;
