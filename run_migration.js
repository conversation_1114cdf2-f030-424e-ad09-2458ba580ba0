#!/usr/bin/env node

/**
 * Script para executar migração do banco de dados
 * Adiciona a coluna approved_code_hash à tabela checkers
 */

const db = require('./config/database');

async function runMigration() {
  console.log('🔧 Executando migração do banco de dados...\n');

  try {
    // Conectar ao banco
    await db.authenticate();
    console.log('✅ Conectado ao banco de dados');

    // Verificar se a coluna já existe
    const [results] = await db.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'checkers' 
      AND COLUMN_NAME = 'approved_code_hash'
      AND TABLE_SCHEMA = DATABASE()
    `);

    if (results.length > 0) {
      console.log('ℹ️  Coluna approved_code_hash já existe na tabela checkers');
      console.log('✅ Migração não necessária');
      return;
    }

    console.log('📝 Adicionando coluna approved_code_hash à tabela checkers...');

    // Adicionar a coluna
    await db.query(`
      ALTER TABLE checkers 
      ADD COLUMN approved_code_hash VARCHAR(64) NULL 
      COMMENT 'Hash do código aprovado para verificação de integridade'
    `);

    console.log('✅ Coluna approved_code_hash adicionada com sucesso!');

    // Verificar se foi adicionada corretamente
    const [verification] = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'checkers' 
      AND COLUMN_NAME = 'approved_code_hash'
      AND TABLE_SCHEMA = DATABASE()
    `);

    if (verification.length > 0) {
      const column = verification[0];
      console.log('📊 Detalhes da coluna criada:');
      console.log(`   Nome: ${column.COLUMN_NAME}`);
      console.log(`   Tipo: ${column.DATA_TYPE}`);
      console.log(`   Nullable: ${column.IS_NULLABLE}`);
      console.log(`   Comentário: ${column.COLUMN_COMMENT}`);
    }

    console.log('\n🎉 Migração concluída com sucesso!');
    console.log('💡 Agora você pode reiniciar o servidor para aplicar as mudanças.');

  } catch (error) {
    console.error('❌ Erro durante a migração:', error.message);
    
    if (error.code === 'ER_DUP_FIELDNAME') {
      console.log('ℹ️  A coluna já existe. Migração não necessária.');
    } else {
      console.error('🔍 Detalhes do erro:', error);
      process.exit(1);
    }
  } finally {
    await db.close();
    console.log('🔌 Conexão com banco fechada');
  }
}

// Executar migração se chamado diretamente
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('\n✅ Script finalizado');
      process.exit(0);
    })
    .catch(err => {
      console.error('❌ Erro fatal:', err);
      process.exit(1);
    });
}

module.exports = runMigration;
