-- Script SQL para adicionar a coluna approved_code_hash à tabela checkers
-- Execute este script no seu banco de dados MySQL na VPS

USE ofc;

-- Verificar se a coluna já existe
SELECT 'Verificando se a coluna existe...' AS status;
SELECT COLUMN_NAME
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'checkers'
AND COLUMN_NAME = 'approved_code_hash'
AND TABLE_SCHEMA = 'ofc';

-- Adicionar a coluna (remova IF NOT EXISTS se der erro)
SELECT 'Adicionando coluna approved_code_hash...' AS status;
ALTER TABLE checkers
ADD COLUMN approved_code_hash VARCHAR(64) NULL
COMMENT 'Hash do código aprovado para verificação de integridade';

-- Verificar se foi adicionada
SELECT 'Verificando estrutura da tabela...' AS status;
DESCRIBE checkers;

-- Verificar novamente se a coluna existe
SELECT 'Confirmando que a coluna foi criada...' AS status;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'checkers'
AND COLUMN_NAME = 'approved_code_hash'
AND TABLE_SCHEMA = 'ofc';

SELECT 'Migração concluída!' AS status;
