/**
 * Exemplo de módulo de checker compatível com middlewares de autenticação e créditos
 *
 * Este módulo demonstra como criar um checker que:
 * 1. Funciona com o middleware de autenticação (usuários logados)
 * 2. Integra com o sistema de débito de créditos
 * 3. Distribui ganhos para programadores e administradores
 * 4. Segue o padrão [LIVE]/[DIE] para respostas
 * 5. Trata erros adequadamente
 *
 * IMPORTANTE: Este checker já recebe req.session.user preenchido pelo middleware
 * e tem acesso à função req.debitAndDistribute() para débito automático
 */

const axios = require('axios'); // Para requisições HTTP externas

module.exports = async function(req, res, checker) {
  try {
    // 1. VALIDAÇÃO DOS PARÂMETROS DE ENTRADA
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).send('[DIE] Parâmetro lista é obrigatório');
    }

    // 2. VALIDAÇÃO DO FORMATO DOS DADOS
    // Exemplo: formato esperado "numero|mes|ano|cvv"
    const parts = lista.split('|');
    const cardNumber = parts[0]?.trim();
    const expMonth = parts[1]?.trim();
    const expYear = parts[2]?.trim();
    const cvv = parts[3]?.trim();

    // Validar se todos os campos estão presentes
    if (!cardNumber || !expMonth || !expYear || !cvv) {
      return res.send(`[DIE] Formato Inválido | Esperado: numero|mes|ano|cvv | Recebido: ${lista}`);
    }

    // Validar formato do cartão (16 dígitos)
    if (!/^\d{16}$/.test(cardNumber)) {
      return res.send(`[DIE] Número Inválido | Deve conter 16 dígitos | ${lista}`);
    }

    // Validar mês (01-12)
    if (!/^(0[1-9]|1[0-2])$/.test(expMonth)) {
      return res.send(`[DIE] Mês Inválido | Deve ser 01-12 | ${lista}`);
    }

    // Validar ano (formato YY ou YYYY)
    if (!/^\d{2}(\d{2})?$/.test(expYear)) {
      return res.send(`[DIE] Ano Inválido | Formato: YY ou YYYY | ${lista}`);
    }

    // Validar CVV (3 ou 4 dígitos)
    if (!/^\d{3,4}$/.test(cvv)) {
      return res.send(`[DIE] CVV Inválido | Deve ter 3 ou 4 dígitos | ${lista}`);
    }

    // 3. VALIDAÇÃO ADICIONAL COM ALGORITMO DE LUHN
    const isValidCard = luhnCheck(cardNumber);
    if (!isValidCard) {
      return res.send(`[DIE] Número de Cartão Inválido | Falha na validação Luhn | ${lista}`);
    }

    // 4. SIMULAÇÃO DE PROCESSAMENTO
    console.log(`[CHECKER EXAMPLE] Processando cartão para usuário ${req.session.user.username}: ${cardNumber.substring(0,6)}******${cardNumber.substring(12)}`);

    // Simular delay de processamento real (1-3 segundos)
    const delay = Math.floor(Math.random() * 2000) + 1000;
    await new Promise(resolve => setTimeout(resolve, delay));

    // 5. SIMULAÇÃO DE CONSULTA À API EXTERNA
    let isApproved = false;
    let responseData = {};

    try {
      // Exemplo de como fazer uma requisição real (descomente para usar)
      /*
      const response = await axios.post('https://api-exemplo.com/validate', {
        card_number: cardNumber,
        exp_month: expMonth,
        exp_year: expYear,
        cvv: cvv
      }, {
        timeout: 10000,
        headers: {
          'Authorization': 'Bearer SEU_TOKEN_AQUI',
          'Content-Type': 'application/json'
        }
      });

      responseData = response.data;
      isApproved = responseData.status === 'approved';
      */

      // Simulação para exemplo (30% de aprovação)
      isApproved = Math.random() > 0.7;

      // Simular dados de resposta
      if (isApproved) {
        responseData = {
          balance: Math.floor(Math.random() * 10000) + 100,
          bank: ['Banco do Brasil', 'Itaú', 'Bradesco', 'Santander'][Math.floor(Math.random() * 4)],
          type: ['CREDIT', 'DEBIT'][Math.floor(Math.random() * 2)],
          brand: ['VISA', 'MASTERCARD', 'ELO'][Math.floor(Math.random() * 3)]
        };
      }

    } catch (apiError) {
      console.error('[CHECKER EXAMPLE] Erro na API externa:', apiError.message);
      // Em caso de erro na API, considerar como reprovado
      isApproved = false;
    }

    // 6. DÉBITO DE CRÉDITOS E DISTRIBUIÇÃO DE GANHOS
    // O middleware já verificou se o usuário tem saldo suficiente
    // Agora precisamos debitar e distribuir os ganhos
    const debited = await req.debitAndDistribute(isApproved);

    if (!debited) {
      console.error('[CHECKER EXAMPLE] Falha ao debitar créditos do usuário:', req.session.user.username);
      return res.status(500).send('[DIE] Erro interno - falha no débito de créditos');
    }

    // 7. FORMATAÇÃO DA RESPOSTA
    if (isApproved) {
      // Formato [LIVE] para aprovações
      const response = [
        `[LIVE] Cartão Aprovado | ${lista}`,
        `Saldo: $${responseData.balance}`,
        `Banco: ${responseData.bank}`,
        `Tipo: ${responseData.type}`,
        `Bandeira: ${responseData.brand}`,
        `Processado por: ${req.session.user.username}`,
        `PrivXploit Checker`
      ].join(' | ');

      return res.send(response);
    } else {
      // Formato [DIE] para reprovações
      const reasons = [
        'Saldo insuficiente',
        'Cartão expirado',
        'Transação negada pelo banco',
        'Limite excedido',
        'Cartão bloqueado',
        'Dados inválidos'
      ];
      const reason = reasons[Math.floor(Math.random() * reasons.length)];

      const response = [
        `[DIE] Cartão Reprovado | ${lista}`,
        `Motivo: ${reason}`,
        `Processado por: ${req.session.user.username}`,
        `PrivXploit Checker`
      ].join(' | ');

      return res.send(response);
    }

  } catch (error) {
    // 8. TRATAMENTO DE ERROS
    console.error('[CHECKER EXAMPLE] Erro inesperado:', error);

    // Log detalhado para debug
    console.error('Detalhes do erro:', {
      message: error.message,
      stack: error.stack,
      user: req.session.user?.username,
      lista: req.query.lista,
      timestamp: new Date().toISOString()
    });

    // Resposta de erro para o usuário
    return res.status(500).send('[DIE] Erro interno do servidor - tente novamente');
  }
};

/**
 * Implementação do algoritmo de Luhn para validar números de cartão
 * @param {string} cardNumber - Número do cartão sem espaços
 * @returns {boolean} - Se o número é válido
 */
function luhnCheck(cardNumber) {
  if (!cardNumber || !/^\d+$/.test(cardNumber)) return false;

  let sum = 0;
  let shouldDouble = false;

  // Percorrer o número de trás para frente
  for (let i = cardNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cardNumber.charAt(i));

    if (shouldDouble) {
      digit *= 2;
      if (digit > 9) digit -= 9;
    }

    sum += digit;
    shouldDouble = !shouldDouble;
  }

  return sum % 10 === 0;
}
