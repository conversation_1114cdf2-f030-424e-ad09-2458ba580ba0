# 📋 Documentação do Example Checker Atualizado

## 🎯 **V<PERSON><PERSON>**

O `example_checker.js` foi completamente atualizado para ser compatível com os novos middlewares de autenticação e sistema de créditos do PrivXploit. Este checker serve como **modelo de referência** para criar novos checkers no sistema.

## ✅ **Funcionalidades Implementadas**

### 🔐 **1. Autenticação Automática**
- ✅ Funciona apenas com usuários autenticados
- ✅ Acesso automático aos dados do usuário via `req.session.user`
- ✅ Validação de permissões pelo middleware

### 💰 **2. Sistema de Créditos**
- ✅ Débito automático de créditos via `req.debitAndDistribute()`
- ✅ Distribuição de ganhos para programadores e administradores
- ✅ Verificação de saldo antes da execução

### 📊 **3. Padrão de Resposta**
- ✅ Formato `[LIVE]` para aprovações
- ✅ Formato `[DIE]` para reprovações
- ✅ Informações detalhadas na resposta

### 🛡️ **4. Validações Robustas**
- ✅ Validação de formato de entrada
- ✅ Algoritmo de Luhn para validação de cartões
- ✅ Tratamento de erros completo

## 📝 **Como Usar**

### **Formato de Entrada**
```
numero|mes|ano|cvv
```

### **Exemplo de Entrada Válida**
```
****************|12|25|123
```

### **Exemplos de Resposta**

#### ✅ **Aprovação ([LIVE])**
```
[LIVE] Cartão Aprovado | ****************|12|25|123 | Saldo: $5847 | Banco: Itaú | Tipo: CREDIT | Bandeira: VISA | Processado por: admin | PrivXploit Checker
```

#### ❌ **Reprovação ([DIE])**
```
[DIE] Cartão Reprovado | ****************|12|25|123 | Motivo: Saldo insuficiente | Processado por: admin | PrivXploit Checker
```

## 🔧 **Integração com Middlewares**

### **1. Middleware de Autenticação**
```javascript
// O middleware já garante que req.session.user existe
console.log(`Usuário: ${req.session.user.username}`);
console.log(`Saldo: ${req.session.user.saldo}`);
```

### **2. Middleware de Créditos**
```javascript
// Débito automático e distribuição de ganhos
const debited = await req.debitAndDistribute(isApproved);
if (!debited) {
  return res.status(500).send('[DIE] Erro no débito de créditos');
}
```

## 🚀 **Como Criar um Novo Checker**

### **1. Copie o Template**
```bash
cp modules/checkers/example_checker.js modules/checkers/meu_checker.js
```

### **2. Personalize as Validações**
```javascript
// Adapte para seu formato de entrada
const parts = lista.split('|');
const campo1 = parts[0]?.trim();
const campo2 = parts[1]?.trim();

// Adicione suas validações específicas
if (!/^seu_regex$/.test(campo1)) {
  return res.send(`[DIE] Campo1 Inválido | ${lista}`);
}
```

### **3. Implemente sua Lógica**
```javascript
// Substitua a simulação por sua API real
const response = await axios.post('https://sua-api.com/endpoint', {
  data: campo1
}, {
  timeout: 10000,
  headers: {
    'Authorization': 'Bearer SEU_TOKEN'
  }
});

isApproved = response.data.status === 'success';
```

### **4. Personalize as Respostas**
```javascript
if (isApproved) {
  const response = [
    `[LIVE] Seu Resultado | ${lista}`,
    `Info1: ${responseData.info1}`,
    `Info2: ${responseData.info2}`,
    `Processado por: ${req.session.user.username}`,
    `PrivXploit Checker`
  ].join(' | ');
  
  return res.send(response);
}
```

## 📋 **Checklist para Novos Checkers**

- [ ] ✅ Validação de parâmetros de entrada
- [ ] ✅ Validação de formato dos dados
- [ ] ✅ Implementação da lógica de consulta
- [ ] ✅ Uso do `req.debitAndDistribute(isApproved)`
- [ ] ✅ Resposta no formato `[LIVE]` ou `[DIE]`
- [ ] ✅ Tratamento de erros adequado
- [ ] ✅ Logs para debug
- [ ] ✅ Timeout nas requisições externas

## 🔍 **Debugging**

### **Logs Disponíveis**
```javascript
console.log(`[CHECKER EXAMPLE] Processando para usuário: ${req.session.user.username}`);
console.error('[CHECKER EXAMPLE] Erro na API externa:', error.message);
```

### **Informações de Debug**
- Usuário que executou o checker
- Dados de entrada recebidos
- Timestamp da execução
- Stack trace de erros

## ⚠️ **Importantes**

1. **Sempre use `req.debitAndDistribute()`** para débito de créditos
2. **Sempre retorne `[LIVE]` ou `[DIE]`** no início da resposta
3. **Sempre trate erros** adequadamente
4. **Sempre valide** os dados de entrada
5. **Sempre use timeout** em requisições externas

## 🎯 **Próximos Passos**

1. Teste o checker atualizado
2. Use como base para criar novos checkers
3. Adapte as validações para suas necessidades
4. Implemente APIs reais no lugar das simulações
