/**
 * Template de Checker para Consulta de CPF
 *
 * Este template demonstra como criar um checker que consulta uma API externa
 * e formata os resultados para exibição no sistema.
 *
 * Requisitos obrigatórios:
 * 1. Exportar uma função assíncrona que recebe (req, res, checker)
 * 2. Validar os parâmetros de entrada
 * 3. Tratar erros adequadamente
 * 4. Formatar a resposta conforme o padrão do sistema
 * 5. Usar tags [LIVE] para aprovados e [DIE] para reprovados
 */

const axios = require('axios'); // Certifique-se de que axios está instalado: npm install axios

module.exports = async function(req, res, checker) {
  try {
    // 1. VALIDAÇÃO DOS PARÂMETROS DE ENTRADA
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).send('Parâmetro lista é obrigatório');
    }

    // Limpar o CPF (remover caracteres não numéricos)
    const cpf = lista.replace(/\D/g, '');

    // Validar formato do CPF (11 dígitos)
    if (cpf.length !== 11) {
      return res.send(`[DIE] CPF Inválido | Formato incorreto: ${lista}`);
    }

    // 2. CONSULTA À API EXTERNA
    try {
      const response = await axios.get(`http://185.228.72.8:8080/pessoas/?cpf=${cpf}`, {
        timeout: 10000 // Timeout de 10 segundos
      });

      // 3. PROCESSAMENTO DA RESPOSTA
      const data = response.data;

      // Verificar se a resposta contém os dados esperados
      if (data && data.cpf) {
        // Formatar a data de nascimento
        const nascimento = data.nasc || 'Não informado';

        // Formatar o sexo
        const sexo = data.sexo || 'Não informado';

        // 4. FORMATAÇÃO DA RESPOSTA PARA O CLIENTE
        // Importante: Usar tag [LIVE] para respostas bem-sucedidas
        return res.send(`[LIVE] CPF: ${data.cpf} | Nome: ${data.name} | Sexo: ${sexo} | Data de Nascimento: ${nascimento}`);
      } else {
        // CPF não encontrado na base de dados - usar tag [DIE]
        return res.send(`[DIE] CPF Não Encontrado | ${lista}`);
      }
    } catch (apiError) {
      // Erro na consulta à API externa
      console.error('Erro na consulta à API:', apiError);

      // Verificar se é um erro de timeout
      if (apiError.code === 'ECONNABORTED') {
        return res.send(`[DIE] Timeout | A consulta demorou muito para responder. Tente novamente.`);
      }

      // Verificar se a API retornou um erro específico
      if (apiError.response) {
        const statusCode = apiError.response.status;

        if (statusCode === 404) {
          return res.send(`[DIE] CPF Não Encontrado | ${lista}`);
        } else if (statusCode === 429) {
          return res.send(`[DIE] Limite Excedido | Muitas requisições. Tente novamente mais tarde.`);
        }
      }

      // Erro genérico
      return res.send(`[DIE] Erro | Falha na consulta. Tente novamente.`);
    }
  } catch (err) {
    // 5. TRATAMENTO DE ERROS GERAIS
    console.error('Erro no checker de CPF:', err);
    return res.status(500).send('[DIE] Erro interno ao processar a requisição');
  }
};
