/**
 * Checker para Consulta GayGay
 *
 * Este módulo implementa um checker que simula uma consulta e retorna resultados
 * formatados com as tags [LIVE] e [DIE] para exibição no sistema.
 *
 * Requisitos obrigatórios:
 * 1. Exportar uma função assíncrona que recebe (req, res, checker)
 * 2. Validar os parâmetros de entrada
 * 3. Tratar erros adequadamente
 * 4. Formatar a resposta conforme o padrão do sistema
 * 5. Usar tags [LIVE] para aprovados e [DIE] para reprovados
 */

const axios = require('axios'); // Certifique-se de que axios está instalado: npm install axios

module.exports = async function(req, res, checker) {
  try {
    // 1. VALIDAÇÃO DOS PARÂMETROS DE ENTRADA
    const { lista } = req.query;

    if (!lista) {
      return res.status(400).send('Parâmetro lista é obrigatório');
    }

    // Validar formato da entrada
    if (lista.length < 5) {
      return res.send(`[DIE] Entrada Inválida | Formato incorreto: ${lista}`);
    }

    // 2. SIMULAÇÃO DE CONSULTA
    try {
      // Simular um atraso de processamento (entre 0.5 e 2 segundos)
      const delay = Math.floor(Math.random() * 1500) + 500;
      await new Promise(resolve => setTimeout(resolve, delay));

      // Simular uma chance de aprovação (30%)
      const isApproved = Math.random() > 0.7;

      // 3. PROCESSAMENTO DA RESPOSTA
      if (isApproved) {
        // Gerar dados fictícios para aprovados
        const nomes = ['João Silva', 'Maria Oliveira', 'Pedro Santos', 'Ana Souza', 'Carlos Ferreira'];
        const nome = nomes[Math.floor(Math.random() * nomes.length)];
        
        const idade = Math.floor(Math.random() * 40) + 18;
        
        const cidades = ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Salvador', 'Recife'];
        const cidade = cidades[Math.floor(Math.random() * cidades.length)];

        // 4. FORMATAÇÃO DA RESPOSTA PARA O CLIENTE
        // Importante: Usar tag [LIVE] para respostas bem-sucedidas
        return res.send(`[LIVE] Consulta: ${lista} | Nome: ${nome} | Idade: ${idade} | Cidade: ${cidade}`);
      } else {
        // Gerar mensagens de erro variadas para reprovados
        const motivos = [
          'Não encontrado na base de dados',
          'Registro expirado',
          'Dados inconsistentes',
          'Registro bloqueado',
          'Informação indisponível'
        ];
        const motivo = motivos[Math.floor(Math.random() * motivos.length)];
        
        // Usar tag [DIE] para respostas reprovadas
        return res.send(`[DIE] ${motivo} | ${lista}`);
      }
    } catch (apiError) {
      // Erro na consulta
      console.error('Erro na consulta:', apiError);

      // Verificar se é um erro de timeout
      if (apiError.code === 'ECONNABORTED') {
        return res.send(`[DIE] Timeout | A consulta demorou muito para responder. Tente novamente.`);
      }

      // Erro genérico
      return res.send(`[DIE] Erro | Falha na consulta. Tente novamente.`);
    }
  } catch (err) {
    // 5. TRATAMENTO DE ERROS GERAIS
    console.error('Erro no checker GayGay:', err);
    return res.status(500).send('[DIE] Erro interno ao processar a requisição');
  }
};
