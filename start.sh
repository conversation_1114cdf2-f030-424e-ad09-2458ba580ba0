#!/bin/bash

# PrivXploit - Script de Inicialização
# Autor: Configuração Automática
# Data: $(date)

echo "🚀 PrivXploit - Iniciando aplicação..."
echo "=================================================="

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ]; then
    echo "❌ Erro: Execute este script no diretório raiz do projeto"
    exit 1
fi

# Verificar se o arquivo .env existe
if [ ! -f ".env" ]; then
    echo "⚠️  Arquivo .env não encontrado, copiando do .env.example..."
    cp .env.example .env
    echo "📝 Configure o arquivo .env antes de continuar"
    exit 1
fi

# Verificar se o Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não está instalado"
    echo "📦 Instale o Node.js versão 18 ou superior"
    exit 1
fi

# Verificar se o MySQL está rodando
if ! systemctl is-active --quiet mysql; then
    echo "⚠️  MySQL não está rodando, tentando iniciar..."
    sudo systemctl start mysql
    if [ $? -ne 0 ]; then
        echo "❌ Erro ao iniciar MySQL"
        exit 1
    fi
fi

# Verificar se as dependências estão instaladas
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Erro ao instalar dependências"
        exit 1
    fi
fi

# Verificar conexão com banco de dados
echo "🔌 Testando conexão com banco de dados..."
node -e "
require('dotenv').config();
const db = require('./config/database');
db.authenticate()
  .then(() => {
    console.log('✅ Conexão com banco OK');
    process.exit(0);
  })
  .catch(err => {
    console.error('❌ Erro na conexão:', err.message);
    process.exit(1);
  });
" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "❌ Erro na conexão com banco de dados"
    echo "📝 Verifique as configurações no arquivo .env"
    exit 1
fi

echo "✅ Todas as verificações passaram!"
echo "🌐 Iniciando servidor..."
echo "=================================================="

# Escolher modo de execução
if [ "$1" = "dev" ]; then
    echo "🔧 Modo desenvolvimento (com nodemon)"
    if command -v nodemon &> /dev/null; then
        nodemon app.js
    else
        echo "⚠️  nodemon não instalado, usando node normal"
        node app.js
    fi
elif [ "$1" = "pm2" ]; then
    echo "🚀 Modo produção (com PM2)"
    if command -v pm2 &> /dev/null; then
        pm2 start ecosystem.config.js
        pm2 logs privxploit
    else
        echo "❌ PM2 não está instalado"
        echo "📦 Instale com: npm install -g pm2"
        exit 1
    fi
else
    echo "🌐 Modo normal"
    node app.js
fi
