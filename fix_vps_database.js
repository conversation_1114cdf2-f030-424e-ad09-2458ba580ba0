#!/usr/bin/env node

/**
 * Script para corrigir banco de dados na VPS
 * Adiciona a coluna approved_code_hash à tabela checkers
 * Execute este script na VPS: node fix_vps_database.js
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixVPSDatabase() {
  console.log('🔧 Corrigindo banco de dados na VPS...\n');

  // Configurações do banco (usando variáveis do .env)
  const dbConfig = {
    host: process.env.DB_HOST || '127.0.0.1',
    user: process.env.DB_USER || 'cancrosoft',
    password: process.env.DB_PASSWORD || 'cancrosoft123',
    database: process.env.DB_NAME || 'ofc'
  };

  console.log(`📊 Conectando ao banco: ${dbConfig.host}/${dbConfig.database}`);

  let connection;
  
  try {
    // Conectar ao banco
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Conectado ao banco de dados');

    // Verificar se a coluna já existe
    console.log('🔍 Verificando se a coluna approved_code_hash existe...');
    
    const [rows] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'checkers' 
      AND COLUMN_NAME = 'approved_code_hash'
      AND TABLE_SCHEMA = ?
    `, [dbConfig.database]);

    if (rows.length > 0) {
      console.log('✅ Coluna approved_code_hash já existe na tabela checkers');
      console.log('ℹ️  Nenhuma ação necessária');
      return;
    }

    console.log('📝 Coluna não encontrada. Adicionando approved_code_hash...');

    // Adicionar a coluna
    await connection.execute(`
      ALTER TABLE checkers 
      ADD COLUMN approved_code_hash VARCHAR(64) NULL 
      COMMENT 'Hash do código aprovado para verificação de integridade'
    `);

    console.log('✅ Coluna approved_code_hash adicionada com sucesso!');

    // Verificar se foi adicionada corretamente
    const [verification] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'checkers' 
      AND COLUMN_NAME = 'approved_code_hash'
      AND TABLE_SCHEMA = ?
    `, [dbConfig.database]);

    if (verification.length > 0) {
      const column = verification[0];
      console.log('\n📊 Detalhes da coluna criada:');
      console.log(`   Nome: ${column.COLUMN_NAME}`);
      console.log(`   Tipo: ${column.DATA_TYPE}`);
      console.log(`   Nullable: ${column.IS_NULLABLE}`);
      console.log(`   Comentário: ${column.COLUMN_COMMENT}`);
    }

    console.log('\n🎉 Migração concluída com sucesso!');
    console.log('💡 Reinicie o servidor para aplicar as mudanças:');
    console.log('   pm2 restart all');
    console.log('   ou');
    console.log('   npm start');

  } catch (error) {
    console.error('\n❌ Erro durante a migração:', error.message);
    
    if (error.code === 'ER_DUP_FIELDNAME') {
      console.log('ℹ️  A coluna já existe. Migração não necessária.');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('🔐 Erro de acesso ao banco. Verifique as credenciais no .env');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('🔌 Não foi possível conectar ao banco. Verifique se o MySQL está rodando.');
    } else {
      console.error('🔍 Código do erro:', error.code);
      console.error('🔍 Detalhes:', error);
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Conexão com banco fechada');
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  fixVPSDatabase()
    .then(() => {
      console.log('\n✅ Script finalizado com sucesso');
      process.exit(0);
    })
    .catch(err => {
      console.error('\n❌ Erro fatal:', err.message);
      process.exit(1);
    });
}

module.exports = fixVPSDatabase;
