require('dotenv').config();

async function testCodeReview() {
  try {
    console.log('🔧 Testando sistema de revisão de código...');
    
    const { Checker, CodeReview } = require('./models');
    
    // Buscar checkers sem aprovação
    const checkersWithoutApproval = await Checker.findAll({
      where: {
        [require('sequelize').Op.or]: [
          { approved_code_hash: null },
          { approved_code_hash: '' }
        ]
      },
      limit: 5
    });

    console.log(`📋 Encontrados ${checkersWithoutApproval.length} checkers sem aprovação`);
    
    checkersWithoutApproval.forEach(checker => {
      console.log(`   - ${checker.name} (ID: ${checker.id})`);
    });

    // Buscar revisões pendentes
    const pendingReviews = await CodeReview.findAll({
      where: {
        status: ['pending', 'needs_revision']
      },
      limit: 5
    });

    console.log(`⏳ Encontradas ${pendingReviews.length} revisões pendentes`);
    
    pendingReviews.forEach(review => {
      console.log(`   - Review ID: ${review.id} (Status: ${review.status})`);
    });

    console.log('✅ Teste concluído!');
    process.exit(0);

  } catch (err) {
    console.error('❌ Erro:', err.message);
    process.exit(1);
  }
}

testCodeReview();
