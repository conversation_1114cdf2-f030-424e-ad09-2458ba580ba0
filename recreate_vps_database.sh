#!/bin/bash

# Script para recriar o banco de dados na VPS
# Execute este script na VPS como root

echo "🔧 Recriando banco de dados PrivXploit na VPS..."
echo "⚠️  ATENÇÃO: Este script irá DELETAR o banco atual!"
echo ""

# Configurações do banco
DB_USER="cancrosoft"
DB_PASS="cancrosoft123"
DB_NAME="ofc"

# Verificar se o MySQL está rodando
echo "🔍 Verificando se o MySQL está rodando..."
if ! systemctl is-active --quiet mysql; then
    echo "❌ MySQL não está rodando. Iniciando..."
    systemctl start mysql
    sleep 3
fi

if systemctl is-active --quiet mysql; then
    echo "✅ MySQL está rodando"
else
    echo "❌ Erro: MySQL não conseguiu iniciar"
    exit 1
fi

# Parar o servidor Node.js
echo "🛑 Parando servidor Node.js..."
pm2 stop all 2>/dev/null || echo "ℹ️  Nenhum processo PM2 rodando"

# Fazer backup do banco atual (se existir)
echo "💾 Fazendo backup do banco atual..."
BACKUP_FILE="/var/www/privxploit/backup_ofc_$(date +%Y%m%d_%H%M%S).sql"
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_FILE 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Backup salvo em: $BACKUP_FILE"
else
    echo "⚠️  Não foi possível fazer backup (banco pode não existir)"
fi

# Ir para o diretório do projeto
cd /var/www/privxploit/privxploit || cd /var/www/privxploit

# Verificar se os arquivos SQL existem
if [ ! -f "recreate_database.sql" ]; then
    echo "❌ Arquivo recreate_database.sql não encontrado"
    echo "💡 Certifique-se de que os arquivos SQL estão no diretório do projeto"
    exit 1
fi

if [ ! -f "insert_initial_data.sql" ]; then
    echo "❌ Arquivo insert_initial_data.sql não encontrado"
    exit 1
fi

# Executar script de recriação do banco
echo "🗄️  Recriando estrutura do banco..."
mysql -u $DB_USER -p$DB_PASS < recreate_database.sql

if [ $? -eq 0 ]; then
    echo "✅ Estrutura do banco criada com sucesso"
else
    echo "❌ Erro ao criar estrutura do banco"
    exit 1
fi

# Inserir dados iniciais
echo "📝 Inserindo dados iniciais..."
mysql -u $DB_USER -p$DB_PASS < insert_initial_data.sql

if [ $? -eq 0 ]; then
    echo "✅ Dados iniciais inseridos com sucesso"
else
    echo "❌ Erro ao inserir dados iniciais"
    exit 1
fi

# Verificar se a coluna approved_code_hash foi criada
echo "🔍 Verificando se a coluna approved_code_hash existe..."
COLUMN_CHECK=$(mysql -u $DB_USER -p$DB_PASS -e "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'checkers' AND COLUMN_NAME = 'approved_code_hash' AND TABLE_SCHEMA = '$DB_NAME';" | grep approved_code_hash)

if [ -n "$COLUMN_CHECK" ]; then
    echo "✅ Coluna approved_code_hash criada com sucesso"
else
    echo "❌ Erro: Coluna approved_code_hash não foi criada"
    exit 1
fi

# Verificar estrutura das tabelas principais
echo "📊 Verificando estrutura das tabelas..."
mysql -u $DB_USER -p$DB_PASS -e "
USE $DB_NAME;
SELECT 
  'usuarios' AS tabela,
  COUNT(*) AS registros
FROM usuarios
UNION ALL
SELECT 
  'categories' AS tabela,
  COUNT(*) AS registros
FROM categories
UNION ALL
SELECT 
  'checkers' AS tabela,
  COUNT(*) AS registros
FROM checkers
UNION ALL
SELECT 
  'settings' AS tabela,
  COUNT(*) AS registros
FROM settings;
"

# Instalar dependências se necessário
echo "📦 Verificando dependências..."
if [ -f "package.json" ]; then
    npm install --production
fi

# Reiniciar o servidor
echo "🚀 Reiniciando servidor..."
pm2 start ecosystem.config.js 2>/dev/null || pm2 start app.js --name "privxploit" 2>/dev/null || npm start &

# Aguardar um pouco para o servidor iniciar
sleep 5

# Verificar se o servidor está rodando
echo "🔍 Verificando se o servidor está rodando..."
if pm2 list | grep -q "online"; then
    echo "✅ Servidor está rodando"
else
    echo "⚠️  Servidor pode não estar rodando. Verifique com: pm2 logs"
fi

echo ""
echo "🎉 Banco de dados recriado com sucesso!"
echo ""
echo "📊 Resumo:"
echo "   - Banco de dados: $DB_NAME"
echo "   - Backup salvo em: $BACKUP_FILE"
echo "   - Usuário admin criado: admin / password"
echo "   - Email admin: <EMAIL>"
echo "   - Categorias criadas: 4"
echo "   - Checker de exemplo criado e aprovado"
echo ""
echo "💡 Próximos passos:"
echo "   1. Acesse o painel: http://196.251.73.17"
echo "   2. Faça login com: admin / password"
echo "   3. Altere a senha do admin"
echo "   4. Configure as settings necessárias"
echo "   5. Teste o sistema de code review"
echo ""
echo "🔧 Comandos úteis:"
echo "   pm2 logs          # Ver logs do servidor"
echo "   pm2 restart all   # Reiniciar servidor"
echo "   pm2 status        # Status dos processos"
